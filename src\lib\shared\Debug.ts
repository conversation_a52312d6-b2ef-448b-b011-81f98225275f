export type DebugItem = {
	[K in keyof DebugItemValues]: {
		title: string;
		type: K;
		value: DebugItemValues[K];
		classes?: string;
		code?: boolean;
	};
}[keyof DebugItemValues];

export type DebugItemValues = {
	component: DebugComponent;
	html: DebugHtml;
};

export type DebugComponent = {
	[K in keyof DebugComponentValues]: {
		component: K;
		props: DebugComponentValues[K];
	};
}[keyof DebugComponentValues];

export type DebugComponentValues = {
	button: {
		href?: string;
		text: any;
		type?:
			| 'button'
			| 'error'
			| 'success'
			| 'warning'
			| 'info'
			| 'primary'
			| 'secondary'
			| 'tertiary'
			| 'gradient';
		width?: string;
		height?: string;
		class?: string;
	};
};

interface DebugHtml {
	data: string;
}
