<script lang="ts">
	import { onMount, type Component, type SvelteComponent } from 'svelte';
	import Draggable from '../util/Draggable.svelte';
	import type { DebugComponentValues, DebugItem } from '$lib/shared/Debug';
	import Button from '../forms/Button.svelte';
	import type { JsonSessionStorage } from '$lib/shared/Session';

	interface Props {
		debugInfo: DebugItem[];
		sessionUser?: JsonSessionStorage['user'];
		sessionPermissions?: string[];
	}

	let { debugInfo, sessionUser, sessionPermissions = [] }: Props = $props();

	let debugMain: HTMLDivElement;
	let initialHeight = 482;
	let height = $state('482px');
	let hidden = $state(false);
	let image: string = $state('');

	function resize() {
		const currentHeight = debugMain.getBoundingClientRect().height;
		if (currentHeight >= initialHeight) {
			height = '24px';
			hidden = true;
		} else {
			height = 'auto';
			debugMain.style.minHeight = `${initialHeight}px`;
			hidden = false;
		}
	}

	/* useful information */

	$effect(() => {
		if (!debugMain) return;

		if (hidden) {
			debugMain.style.height = '24px';
			debugMain.style.minHeight = '24px';
		} else {
			debugMain.style.height = 'auto';
			debugMain.style.minHeight = `${initialHeight}px`;
		}

		if (image.length > 0) {
			debugMain.style.backgroundImage = `url('${image}')`;
			debugMain.style.backgroundSize = 'cover';
			debugMain.style.backgroundPosition = 'center';
			debugMain.style.backgroundRepeat = 'no-repeat';
			debugMain.style.backdropFilter = 'blur(10px)';
			window.localStorage.setItem('dbg-bg-img', image);
		}
	});

	onMount(() => {
		const img = window.localStorage.getItem('dbg-bg-img');
		if (img) {
			image = img;
		}
	});
</script>

<Draggable target="dbg-nav" style="z-index: 1001;" retain={true}>
	{#snippet component()}
		<div
			class="relative z-[1001] w-[600px] rounded-md bg-[var(--bg-debug)] pb-4 transition-all"
			bind:this={debugMain}
		>
			{#if image}
				<div
					class="absolute inset-0 rounded-md"
					style="background-image: url('{image}'); background-size: cover; background-position: center; background-repeat: no-repeat; filter: blur(10px); transform: scale(1.1); z-index: -1;"
				></div>
			{/if}
			<div
				class="dbg-nav flex w-full cursor-grab select-none items-center justify-between rounded-t-md bg-[var(--bg-debug-header)] p-2"
			>
				<!-- nav -->
				<div class="w-fit">
					<h1 class="font-heebo font-bold text-[var(--text-debug-primary)]">Debug Menu</h1>
				</div>
				<ul class="flex justify-end space-x-4">
					<li></li>
					<li>
						<button class="text-[var(--text-debug-primary)]" onclick={resize}>
							{#if !hidden}
								<span class="icon-[lets-icons--view-hide-fill]" style="width: 24px; height: 24px;"
								></span>
							{:else}
								<span class="icon-[lets-icons--view-fill]" style="width: 24px; height: 24px;"
								></span>
							{/if}
						</button>
					</li>
				</ul>
			</div>
			<!-- Content -->
			<div class="grid grid-flow-dense gap-2 p-4">
				{#if !hidden}
					{#each debugInfo as item}
						<div class="flex flex-col gap-1 {item.classes || ''}">
							<p class="text-sm text-[var(--text-debug-key)]">{item.title}</p>
							<p class="text-wrap text-xs text-[var(--text-debug-value)]">
								{#if item.code}
									<code class="rounded-sm bg-[var(--bg-debug-code)] p-1 font-mono">
										{#if item.type === 'component'}
											{#if item.value.component === 'button'}
												{@const props = item.value.props as DebugComponentValues['button']}
												<Button {...props} />
											{/if}
										{:else if item.type === 'html'}
											{@html item.value.data}
										{/if}
									</code>
								{:else if item.type === 'component'}
									{#if item.value.component === 'button'}
										{@const props = item.value.props as DebugComponentValues['button']}
										<Button {...props} />
									{/if}
								{:else if item.type === 'html'}
									{@html item.value.data}
								{/if}
							</p>
						</div>
					{/each}
					<div class="flex flex-col gap-1">
						<p class="text-sm text-[var(--text-debug-key)]">Set background image for debugger</p>
						<div class="grid grid-cols-4 text-wrap text-xs text-[var(--text-debug-value)]">
							<input
								type="text"
								class="col-span-3 w-full rounded-md bg-[var(--bg-debug-code)] p-2 text-[var(--text-debug-value)]"
								bind:value={image}
							/>
						</div>
					</div>

					<div class="flex flex-col gap-1">
						<p class="text-sm text-[var(--text-debug-key)]">Session Data</p>
						<div class="col-span-4 mb-2 flex w-full flex-row gap-2">
							<Button type="error" href="/dbg/s/reset" class="col-span-2 text-sm" height="24px">
								{#snippet text()}
									Reset
								{/snippet}
							</Button>
							<Button
								type="secondary"
								href="/dbg/s/test-user"
								class="col-span-2 text-sm"
								height="24px"
								width="fit-content"
							>
								{#snippet text()}
									Inject Test User
								{/snippet}
							</Button>
						</div>
						<div class="grid grid-cols-4 gap-2 text-wrap text-xs text-[var(--text-debug-value)]">
							<form class="col-span-4 flex flex-col gap-2" action="/dbg/s/update" method="POST">
								<div class="grid grid-cols-2 gap-2">
									<input
										type="text"
										name="user.id"
										placeholder="User ID"
										class="w-full rounded-md bg-[var(--bg-debug-code)] p-2 text-[var(--text-debug-value)]"
										value={sessionUser?.id || ''}
									/>
									<input
										type="text"
										name="user.username"
										placeholder="Username"
										class="w-full rounded-md bg-[var(--bg-debug-code)] p-2 text-[var(--text-debug-value)]"
										value={sessionUser?.username || ''}
									/>
									<input
										type="text"
										name="user.email"
										placeholder="Email"
										class="w-full rounded-md bg-[var(--bg-debug-code)] p-2 text-[var(--text-debug-value)]"
										value={sessionUser?.email || ''}
									/>
									<input
										type="text"
										name="user.phone_number"
										placeholder="Phone Number"
										class="w-full rounded-md bg-[var(--bg-debug-code)] p-2 text-[var(--text-debug-value)]"
										value={sessionUser?.user_info?.phone_number || ''}
									/>
									<input
										type="text"
										name="user.first_name"
										placeholder="First Name"
										class="w-full rounded-md bg-[var(--bg-debug-code)] p-2 text-[var(--text-debug-value)]"
										value={sessionUser?.user_info?.first_name || ''}
									/>
									<input
										type="text"
										name="user.last_name"
										placeholder="Last Name"
										class="w-full rounded-md bg-[var(--bg-debug-code)] p-2 text-[var(--text-debug-value)]"
										value={sessionUser?.user_info?.last_name || ''}
									/>
									<input
										type="text"
										name="permissions"
										placeholder="Permissions (comma-separated)"
										class="w-full rounded-md bg-[var(--bg-debug-code)] p-2 text-[var(--text-debug-value)]"
										value={sessionPermissions}
									/>
								</div>
								<Button type="primary" class="w-full" height="24px">
									{#snippet text()}
										Update Session
									{/snippet}
								</Button>
							</form>
						</div>
					</div>
				{/if}
			</div>
		</div>
	{/snippet}
</Draggable>
