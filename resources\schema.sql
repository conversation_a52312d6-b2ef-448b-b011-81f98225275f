-- Users table
CREATE TABLE IF NOT EXISTS Users (
    -- Id comes in the format of UUID
    -- This is stored in sessions and is used across the platform to reference this user
    -- This does not in any way represent a token and can not be used to access session info.
    id                     text PRIMARY KEY NOT NULL,
    -- This can be null for the time being cause we are UNSURE of how this will look in the future
    -- I have no idea if we have the intention of designing accounts around usernames.
    username               varchar(22),
    email                  text NOT NULL,
    -- The hashed password using SHA-256
    password_hash          text NOT NULL,
    language               text NOT NULL,
    -- Site roles is an array of generally available roles.
    -- The site will validate these roles and prune invalid ones.
    roles                  text[],
    -- These are the scopes specifically granted to this user.
    -- usually a result of a site admin
    permissions            text[],
    -- Whether or not the email on this account has been verified. If this is false,
    -- The user still needs to verify their email, and a token will be present in
    -- AccountsPending
    verified               Boolean NOT NULL,
    created_at             timestamp(3) DEFAULT NOW()::TIMESTAMP(3),
    updated_at             timestamp(3) DEFAULT NOW()::TIMESTAMP(3),
    -- the provider of the user
    provider               text NOT NULL,
    -- Whether or not the account is pending deletion.
    -- This could be set by the user or an administrator.
    deletion_timestamp     timestamp(3) DEFAULT NULL,
    -- Whether or not the account is pending deletion.
    pending_deletion       Boolean NOT NULL DEFAULT false
);

CREATE TABLE IF NOT EXISTS UserInfo (
    id                    text PRIMARY KEY NOT NULL,
    -- The first name of the user (encrypted)
    first_name            text,
    -- The last name of the user (encrypted)
    last_name             text,
    -- Their phone number (encrypted)
    phone_number          text,
    -- Their billing address (encrypted)
    billing_address       text,
    -- Their billing city (encrypted)
    billing_city          text,
    -- Their billing state (encrypted)
    billing_state         text,
    -- Their billing country (encrypted)
    billing_country       text,
    -- Their billing postal code (encrypted)
    billing_postal_code   text,
    CONSTRAINT fk_user FOREIGN KEY(id)
        REFERENCES Users(id)
        ON DELETE CASCADE
);

-- Scopes table
CREATE TABLE IF NOT EXISTS Scopes (
    -- The scope is a string that represents the scope of the application.
    -- For example: user.read, user.write, etc.
    name                  text PRIMARY KEY NOT NULL,
    description           text,
    -- This is whether or not the scope is required for the web application.
    -- If it is, it can not be removed from the application.
    required              boolean NOT NULL DEFAULT false,
    created_at            timestamp(3) DEFAULT NOW()::TIMESTAMP(3),
    updated_at            timestamp(3) DEFAULT NOW()::TIMESTAMP(3)
);

-- EmailTokens table
CREATE TABLE IF NOT EXISTS EmailTokens (
    user_id                text PRIMARY KEY NOT NULL,
    -- This is a token that is used to verify the email of a user.
    -- This is a one time use token.
    token                  text NOT NULL,
    -- The time the token was created at.
    created_at             timestamp(3) NOT NULL,
    -- The time the token expires at.
    expires_at             timestamp(3) NOT NULL,
    CONSTRAINT fk_user FOREIGN KEY(user_id)
        REFERENCES Users(id)
        ON DELETE CASCADE
);

-- Roles table
CREATE TABLE IF NOT EXISTS Roles (
    id                    int PRIMARY KEY NOT NULL,
    -- The name of the role
    name                  text NOT NULL,
    -- The description of the role
    description           text,
    -- The permissions of the role
    permissions           text[],
    -- The parent role of the role
    parent_id             int,
    created_at            timestamp(3) DEFAULT NOW()::TIMESTAMP(3),
    updated_at            timestamp(3) DEFAULT NOW()::TIMESTAMP(3),
    CONSTRAINT fk_parent FOREIGN KEY(parent_id)
        REFERENCES Roles(id)
        ON DELETE CASCADE
);

-- Applications table (depends on Users)
CREATE TABLE IF NOT EXISTS Applications (
    id                    varchar(32) PRIMARY KEY NOT NULL,
    name                  text NOT NULL,
    icon                  text,
    secret                text NOT NULL,
    -- This is a list of VALID URI's that the application
    -- allows. If there are none allowed, then this application will reject
    -- any attempts to verify.
    redirect_uri          text[] NOT NULL,
    description           text,
    -- Whether or not the author is allowing users to use this application.
    enabled               boolean,
    -- This is a entity status object
    -- It represents the state of the application.
    status                jsonb,
    -- These are internal options for a Application set by VLP, such as status.
    -- This is mainly used to ban an application.
    owner_id             text NOT NULL,
    -- The scopes applied by a site admin, these can be used.
    -- By default none are set all need to be requested
    permissions          text[],
    -- The scopes enabled by the developer.
    scopes               text[],
    -- The date the application was created at.
    created_at           timestamp(3) DEFAULT NOW()::TIMESTAMP(3),
    -- The time the application expires (if at all)
    expires_at           timestamp(3) DEFAULT NULL,
    -- Application will be deleted when the user is deleted!
    -- This is important because company apps will stop working if the associated
    -- user account is removed.
    CONSTRAINT fk_owner FOREIGN KEY(owner_id)
        REFERENCES Users(id)
        ON DELETE CASCADE
);

-- Sessions table
CREATE TABLE IF NOT EXISTS Sessions (
    id                    TEXT PRIMARY KEY NOT NULL,
    -- This is a token that is used to verify the email of a user.
    -- This is a one time use token.
    token                 text NOT NULL,
    -- The time the token was created at.
    created_at            timestamp(3) DEFAULT NOW()::TIMESTAMP(3),
    -- The time the token expires at.
    expires_at            timestamp(3) DEFAULT NULL,
    -- The time the token was last used at.
    last_used             timestamp(3) DEFAULT NULL,
    -- The owner of the session
    owner_id              text NOT NULL,
    -- The type of owner
    owner_type            text NOT NULL CHECK (owner_type IN ('user', 'application'))
);

-- AccessTokens table
CREATE TABLE IF NOT EXISTS AccessTokens (
    id                    int PRIMARY KEY NOT NULL,
    access_token          text NOT NULL,
    user_id               text NOT NULL,
    client_id             text NOT NULL,
    expires               bigint NOT NULL,
    scopes                text[] NOT NULL,
    created_at            bigint NOT NULL,
    updated_at            bigint NOT NULL
);