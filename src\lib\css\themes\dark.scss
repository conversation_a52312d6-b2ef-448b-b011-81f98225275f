.dark {
	--bg: #030e17; // #041320 - old
	--bg-secondary: #061219;
	--text-primary: #ffffff;
	--text-secondary: #ffffff;
	--accent: #0f141f;

	/** Navbar */
	--nav-bg: #0f171f;
	--nav-text: #fff;
	--nav-text-secondary: gray;
	--nav-font-weight: 700;

	/* Debug */
	--bg-debug-header: #6e6e6e;
	--bg-debug: #0f1417;
	--text-debug-value: #39a9ff;
	--text-debug-key: #ffffff;
	--text-debug-primary: #ffffff;
	--text-debug-secondary: #a3a3a3;
	--text-link: #2bb2f5;
	--text-link-hover: #86d0f5;
	--bg-debug-code: rgba(0, 0, 0, 0.568);

	/* Button bg */
	--btn-primary: #0d2a4c;
	--btn-hover-primary: #0f3a6d;
	--btn-secondary: #323639;
	--btn-hover-secondary: #424547;
	--btn-tertiary: #323639;
	--btn-hover-tertiary: #424547;
	--btn-error: #da2e2e;
	--btn-hover-error: #fa6a6a;
	--btn-success: #19db50;
	--btn-hover-success: #59d47c;
	--btn-warning: #ffa200;
	--btn-hover-warning: #ffb533;
	--btn-info: #00ffff;
	--btn-hover-info: #33ffff;

	--link-btn-text: #86d0f5;
	--link-btn-text-hover: #a3d6f5;

	/* Inputs */
	--input-disabled-bg: rgba(255, 255, 255, 0.021);
	--input-disabled-text: #565656;
	--input-readonly-bg: #1f1f1f;
	--input-readonly-text: #909090;
	--input-disabled-cursor: not-allowed;
	--input-readonly-cursor: default;
	--bg-form-field-input: rgba(255, 255, 255, 0.059);

	/* Cards */
	--card-container-bg: #061219;
	--card-bg: #0f191f;
	--card-button-bg: #0d2a4c;
	--card-text-primary: #f0f0f0;
	--card-text-secondary: #d1d5db;
	--card-hover-bg: #0f1d26;

	/* Miscelaneous */
	--tr-blue: #8dbcd4;
	--tr-blue-alt: #519cc7;

	--footer-bg: #041720;

	/* Hero */
	--hero-bg: #0d1117;
	--hero-text-color: #ffffff;

	--hero-title-color: #5e97b7;

	--hero-button-text: #ffffff;
	--hero-button-gradient-from: #4c8fb3;
	--hero-button-gradient-to: #5e97b7;
	--hero-button-gradient-hover-from: #3b82f6;
	--hero-button-gradient-hover-to: #4c8fb3;

	--hero-spotlight-bg: #1f2a3c;

	/** Other generic colors */
	--teal-byte: #112231;
	--text-primary: #ffffff;
	--text-secondary: #a3a3a3;
	/** gradient */
	--gradient-start: white;
	--gradient-end: #86d0f5;

	/* Contact Section */
	--contact-gradient-from: #1a3b4d;
	--contact-gradient-to: #0f2433;
	--contact-card-bg: rgba(0, 0, 0, 0.2);
	--contact-card-hover: rgba(0, 0, 0, 0.3);
	--contact-icon-bg: rgba(81, 156, 199, 0.2);
	--contact-text: #e5e7eb;
	--contact-blur-bg: rgba(81, 156, 199, 0.2);

	/* Error Page */
	--error-bg-gradient-from: #0d1117;
	--error-bg-gradient-to: #030e17;
	--error-text-primary: #ffffff;
	--error-text-secondary: #86d0f5;
	--error-accent: #519cc7;
	--error-glow: #2bb2f5;
	--error-particle: #86d0f5;
	--error-button-bg: rgba(81, 156, 199, 0.2);
	--error-button-hover: rgba(81, 156, 199, 0.3);
	--error-button-text: #ffffff;
	--error-code-color: #39a9ff;

	/* Error Colors */
	--error-color: #ef4444;
	--error-color-hover: #f87171;
	--error-color-light: #7f1d1d;
	--error-color-dark: #450a0a;
	--error-color-text: #ffffff;
	--error-color-text-secondary: #fecaca;
}

/* This is the default >_< /*/
::root {
	--bg: #041720;
	--bg-secondary: #061219;
	--nav-bg: #0f191f;
	--text-primary: #ffffff;
	--text-secondary: #ffffff;
	--accent: #0f191f;

	/* Debug */
	--bg-debug-header: #6e6e6e;
	--bg-debug: #0f1417;
	--text-debug-value: #39a9ff;
	--text-debug-key: #ffffff;
	--text-debug-primary: #ffffff;
	--text-debug-secondary: #a3a3a3;
	--bg-debug-code: rgba(0, 0, 0, 0.568);

	/* Button bg */
	--btn-primary: #0d2a4c;
	--btn-hover-primary: #0f3a6d;
	--btn-secondary: #323639;
	--btn-hover-secondary: #424547;
	--btn-tertiary: #323639;
	--btn-hover-tertiary: #424547;
	--btn-error: #da2e2e;
	--btn-hover-error: #fa6a6a;
	--btn-success: #19db50;
	--btn-hover-success: #59d47c;
	--btn-warning: #ffa200;
	--btn-hover-warning: #ffb533;
	--btn-info: #00ffff;
	--btn-hover-info: #33ffff;

	/* Cards */
	--card-container-bg: #061219;
	--card-bg: #0f191f;
	--card-button-bg: #0d2a4c;
	--card-text-primary: #f0f0f0;
	--card-text-secondary: #d1d5db;
	--card-hover-bg: #0f1d26;

	/* Forms */
	--bg-form-container: black;
	--text-form-field-label: #a3a3a3;
	--border-form-field-input: #424547;
	--border-form-field-input-hover: #86d0f5;
	--bg-form-field-input: #0f191f;
	--bg-form-field-input-hover: #0d2a4c;
	--text-form-field: #ffffff;
	--text-form-field-input-hover: #ffffff;
	--input-disabled-bg: #1a1a1a;
	--input-disabled-text: #565656;
	--input-readonly-bg: #1f1f1f;
	--input-readonly-text: #909090;
	--input-disabled-cursor: not-allowed;
	--input-readonly-cursor: default;

	/* Miscelaneous */
	--tr-blue: #8dbcd4;
	--tr-blue-alt: #519cc7;

	--footer-bg: #041720;

	--hero-bg: #0d1117;
	--hero-text-color: #ffffff;

	--hero-title-color: #5e97b7;

	--hero-button-text: #ffffff;
	--hero-button-gradient-from: #4c8fb3;
	--hero-button-gradient-to: #5e97b7;
	--hero-button-gradient-hover-from: #3b82f6;
	--hero-button-gradient-hover-to: #4c8fb3;

	--hero-spotlight-bg: #1f2a3c;

	/** Other generic colors */
	--teal-byte: #112731;
	--text-primary: #ffffff;
	--text-secondary: #a3a3a3;
	/** gradient */
	--gradient-start: white;
	--gradient-end: #86d0f5;

	/* Error Page */
	--error-bg-gradient-from: #0d1117;
	--error-bg-gradient-to: #030e17;
	--error-text-primary: #ffffff;
	--error-text-secondary: #86d0f5;
	--error-accent: #519cc7;
	--error-glow: #2bb2f5;
	--error-particle: #86d0f5;
	--error-button-bg: rgba(81, 156, 199, 0.2);
	--error-button-hover: rgba(81, 156, 199, 0.3);
	--error-button-text: #ffffff;
	--error-code-color: #39a9ff;

	/* Error Colors */
	--error-color: #ef4444;
	--error-color-hover: #f87171;
	--error-color-light: #7f1d1d;
	--error-color-dark: #450a0a;
	--error-color-text: #ffffff;
	--error-color-text-secondary: #fecaca;
}
