import client from '../redis';
import { Session } from '.';
export class SessionStore {
	/**
	 * Sets a session in the session store
	 */
	static async set(id: string, session: Session) {
		await client.set(id, JSON.stringify(session.toJSON()));
	}

	/**
	 * Gets a session from redis.
	 */
	static async get(id: string): Promise<Session | undefined> {
		const x = (await Promise.race([
			new Promise((res, _) => setTimeout(() => res(undefined), 1000)),
			client.get(id)
		])) as undefined | string;
		return x ? Session.fromJSON(JSON.parse(x)) : undefined;
	}

	/**
	 * Deletes a session from redis.
	 */
	static async delete(id: string): Promise<void> {
		await client.del(id);
	}
}
