import { Pool } from 'pg';
import type {
	User,
	Session,
	UserCreate,
	UserResponse,
	UserInfo,
	UserInfoCreate,
	TokenType
} from './types';
import { building } from '$app/environment';
import Util from 'util';
import { BoltId } from '../lib/utils/bolt';
import { AuthService } from './auth';
import { config } from 'dotenv';
import { encryptField, decryptField, encryptUserData, decryptUserData } from '$lib/js/encryption';
import { readFile } from 'fs/promises';
import { join } from 'path';
import { randomBytes } from 'crypto';

config();

const {
	DATABASE_USER,
	DATABASE_PASSWORD,
	DATABASE_HOST,
	DATABASE_PORT,
	DATABASE_NAME,
	NODE_ENV,
	BOLT_NODE_ID
} = process.env;

// Initialize BoltId with a node ID (you should set this based on your deployment environment)
// For example, you could use an environment variable or a configuration value
BoltId.initialize(parseInt(BOLT_NODE_ID || '1')); // Using 1 as a default node ID

const pool = new Pool({
	user: DATABASE_USER,
	password: DATABASE_PASSWORD,
	host: DATABASE_HOST,
	database: DATABASE_NAME,
	port: parseInt(DATABASE_PORT || '5432'),
	ssl: NODE_ENV === 'production' ? { rejectUnauthorized: false } : false
});

try {
	if (building) {
		throw new Error('[->] Skipping database connection in build mode...');
	}
	await pool.connect();
	console.log('[!] Connected to database!');

	// find all tables and log their names to console
	const result = await pool.query(
		'SELECT table_name FROM information_schema.tables WHERE table_schema = $1',
		['public']
	);
	console.log('[!] Tables: ' + result.rows.map((row) => row.table_name).join(', '));
} catch (e) {
	console.log('[!] Failed to connect to database!');
	console.log('[!] Database user: ' + DATABASE_USER);
	console.log('[!] Database password: ' + DATABASE_PASSWORD);
	console.log('[!] Database host: ' + DATABASE_HOST);
	console.log('[!] Database port: ' + DATABASE_PORT);
	console.log('[!] Database error: ' + Util.inspect(e, false, 3, true));
}

// Database client class
export class Database {
	getUsers(size: number, arg1: number, sensitive: boolean) {
		throw new Error('Method not implemented.');
	}
	getUserTransactions(userId: string) {
		throw new Error('Method not implemented.');
	}
	// User management
	async getUser(email: string): Promise<UserResponse | null> {
		try {
			const result = await pool.query('SELECT * FROM users WHERE email = $1', [email]);
			if (!result.rows[0]) return null;
			const user = this.stripSensitiveData(result.rows[0]);
			const userInfo = await this.getUserInfo(user.id);
			return { ...user, user_info: userInfo || undefined };
		} catch (error) {
			console.error('[!] Error in getUser:', error);
			return null;
		}
	}

	async getUserByUsername(username: string): Promise<UserResponse | null> {
		try {
			const result = await pool.query('SELECT * FROM users WHERE username = $1', [username]);
			if (!result.rows[0]) return null;
			const user = this.stripSensitiveData(result.rows[0]);
			const userInfo = await this.getUserInfo(user.id);
			return { ...user, user_info: userInfo || undefined };
		} catch (error) {
			console.error('[!] Error in getUserByUsername:', error);
			return null;
		}
	}

	async getUserById(id: string): Promise<UserResponse | null> {
		try {
			const result = await pool.query('SELECT * FROM users WHERE id = $1', [id]);
			if (!result.rows[0]) return null;
			const user = this.stripSensitiveData(result.rows[0]);
			const userInfo = await this.getUserInfo(user.id);
			return { ...user, user_info: userInfo || undefined };
		} catch (error) {
			console.error('[!] Error in getUserById:', error);
			return null;
		}
	}

	// Get user with password hash (for authentication)
	async getUserWithPassword(emailOrUsername: string): Promise<User | null> {
		try {
			const result = await pool.query('SELECT * FROM users WHERE email = $1 OR username = $2', [
				emailOrUsername,
				emailOrUsername
			]);
			return result.rows[0] || null;
		} catch (error) {
			console.error('[!] Error in getUserWithPassword:', error);
			return null;
		}
	}

	async createUser(data: UserCreate): Promise<UserResponse | null> {
		try {
			// Generate a Bolt ID for the user
			const userId = BoltId.generate();
			const now = new Date();

			// Hash the password using bcrypt
			const passwordHash = await AuthService.hashPassword(data.password);

			const result = await pool.query(
				`INSERT INTO users (
					id, email, username, password_hash, language, roles, permissions,
					verified, provider, pending_deletion, created_at, updated_at
				) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)
				RETURNING *`,
				[
					userId,
					data.email,
					data.username,
					passwordHash,
					data.language,
					data.roles,
					data.permissions,
					data.verified,
					data.provider || 'texrepairs',
					data.pending_deletion,
					now,
					now
				]
			);

			// Strip sensitive data before returning
			const { password_hash, ...userWithoutPassword } = result.rows[0];
			return userWithoutPassword;
		} catch (error) {
			console.error('[!] Error in createUser:', error);
			return null;
		}
	}

	// Helper method to strip sensitive data from user objects
	private stripSensitiveData(user: User): UserResponse {
		const { password_hash, ...userWithoutPassword } = user;
		return userWithoutPassword;
	}

	// Session management
	async getSession(id: string): Promise<Session | null> {
		try {
			const result = await pool.query(
				'SELECT * FROM sessions WHERE id = $1 AND expires_at > NOW()',
				[id]
			);
			if (!result.rows[0]) return null;
			return {
				...result.rows[0],
				created_at: new Date(result.rows[0].created_at),
				expires_at: new Date(result.rows[0].expires_at),
				last_used_at: new Date(result.rows[0].last_used)
			};
		} catch (error) {
			console.error('[!] Error in getSession:', error);
			return null;
		}
	}

	async createSession(data: Omit<Session, 'id'>): Promise<Session | null> {
		try {
			// Generate a Bolt ID for the session
			const sessionId = BoltId.generate();

			const result = await pool.query(
				`INSERT INTO sessions (
					id, token, expires_at, owner_id, owner_type,
					last_used, created_at
				) VALUES ($1, $2, $3, $4, $5, $6, $7)
				RETURNING *`,
				[
					sessionId,
					data.token,
					data.expires_at,
					data.owner_id,
					data.owner_type,
					data.last_used_at,
					data.created_at
				]
			);
			return {
				...result.rows[0],
				created_at: new Date(result.rows[0].created_at),
				expires_at: new Date(result.rows[0].expires_at),
				last_used_at: new Date(result.rows[0].last_used)
			};
		} catch (error) {
			console.error('[!] Error in createSession:', error);
			return null;
		}
	}

	async updateSessionLastUsed(sessionId: string): Promise<boolean> {
		try {
			await pool.query('UPDATE sessions SET last_used = NOW() WHERE id = $1', [sessionId]);
			return true;
		} catch (error) {
			console.error('[!] Error in updateSessionLastUsed:', error);
			return false;
		}
	}

	async deleteSession(token: string): Promise<boolean> {
		try {
			await pool.query('DELETE FROM sessions WHERE token = $1', [token]);
			return true;
		} catch (error) {
			console.error('[!] Error in deleteSession:', error);
			return false;
		}
	}

	// UserInfo management
	async getUserInfo(userId: string): Promise<UserInfo | null> {
		try {
			const result = await pool.query('SELECT * FROM UserInfo WHERE id = $1', [userId]);
			if (!result.rows[0]) return null;

			const userInfo = result.rows[0];
			// Decrypt each field
			const decryptedInfo = {
				id: userInfo.id,
				first_name: decryptField(userInfo.first_name, userId),
				last_name: decryptField(userInfo.last_name, userId),
				phone_number: decryptField(userInfo.phone_number, userId),
				billing_address: decryptField(userInfo.billing_address, userId),
				billing_city: decryptField(userInfo.billing_city, userId),
				billing_state: decryptField(userInfo.billing_state, userId),
				billing_country: decryptField(userInfo.billing_country, userId),
				billing_postal_code: decryptField(userInfo.billing_postal_code, userId)
			};

			return decryptedInfo;
		} catch (error) {
			console.error('[!] Error in getUserInfo:', error);
			return null;
		}
	}

	async createUserInfo(userId: string, data: UserInfoCreate): Promise<UserInfo | null> {
		try {
			// Encrypt each field
			const encryptedData = {
				first_name: encryptField(data.first_name, userId),
				last_name: encryptField(data.last_name, userId),
				phone_number: encryptField(data.phone_number, userId),
				billing_address: encryptField(data.billing_address, userId),
				billing_city: encryptField(data.billing_city, userId),
				billing_state: encryptField(data.billing_state, userId),
				billing_country: encryptField(data.billing_country, userId),
				billing_postal_code: encryptField(data.billing_postal_code, userId)
			};

			const result = await pool.query(
				`INSERT INTO UserInfo (
					id, first_name, last_name, phone_number,
					billing_address, billing_city, billing_state,
					billing_country, billing_postal_code
				) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
				RETURNING *`,
				[
					userId,
					encryptedData.first_name,
					encryptedData.last_name,
					encryptedData.phone_number,
					encryptedData.billing_address,
					encryptedData.billing_city,
					encryptedData.billing_state,
					encryptedData.billing_country,
					encryptedData.billing_postal_code
				]
			);

			// Return decrypted data
			return this.getUserInfo(userId);
		} catch (error) {
			console.error('[!] Error in createUserInfo:', error);
			return null;
		}
	}

	async updateUserInfo(userId: string, data: Partial<UserInfoCreate>): Promise<UserInfo | null> {
		try {
			// Get existing user info
			const existingInfo = await this.getUserInfo(userId);
			if (!existingInfo) return null;

			// Merge existing data with updates
			const updatedData = {
				...existingInfo,
				...data
			};

			// Encrypt each field
			const encryptedData = {
				first_name: encryptField(updatedData.first_name, userId),
				last_name: encryptField(updatedData.last_name, userId),
				phone_number: encryptField(updatedData.phone_number, userId),
				billing_address: encryptField(updatedData.billing_address, userId),
				billing_city: encryptField(updatedData.billing_city, userId),
				billing_state: encryptField(updatedData.billing_state, userId),
				billing_country: encryptField(updatedData.billing_country, userId),
				billing_postal_code: encryptField(updatedData.billing_postal_code, userId)
			};

			// Update the database
			await pool.query(
				`UPDATE UserInfo 
				SET first_name = $2,
					last_name = $3,
					phone_number = $4,
					billing_address = $5,
					billing_city = $6,
					billing_state = $7,
					billing_country = $8,
					billing_postal_code = $9
				WHERE id = $1`,
				[
					userId,
					encryptedData.first_name,
					encryptedData.last_name,
					encryptedData.phone_number,
					encryptedData.billing_address,
					encryptedData.billing_city,
					encryptedData.billing_state,
					encryptedData.billing_country,
					encryptedData.billing_postal_code
				]
			);

			// Return decrypted data
			return this.getUserInfo(userId);
		} catch (error) {
			console.error('[!] Error in updateUserInfo:', error);
			return null;
		}
	}

	// Update getUserFromSession to include user info
	async getUserFromSession(token: string): Promise<UserResponse | null> {
		try {
			const session = await this.getSession(token);
			if (!session) return null;
			const user = await this.getUserById(session.owner_id);
			return user;
		} catch (error) {
			console.error('[!] Error in getUserFromSession:', error);
			return null;
		}
	}

	// Token management
	async createToken(
		userId: string,
		type: TokenType,
		expiresInHours: number = 24
	): Promise<string | null> {
		try {
			// Generate a random token
			const token = randomBytes(32).toString('hex');
			const now = new Date();
			const expiresAt = new Date(now.getTime() + expiresInHours * 60 * 60 * 1000);

			// Delete any existing tokens for this user and type
			await pool.query('DELETE FROM EmailTokens WHERE user_id = $1 AND type = $2', [userId, type]);

			// Insert the new token
			await pool.query(
				`INSERT INTO EmailTokens (user_id, token, type, created_at, expires_at)
				VALUES ($1, $2, $3, $4, $5)`,
				[userId, token, type, now, expiresAt]
			);

			return token;
		} catch (error) {
			console.error('[!] Error in createToken:', error);
			return null;
		}
	}

	async deleteToken(userId: string, type: TokenType): Promise<void> {
		await pool.query('DELETE FROM EmailTokens WHERE user_id = $1 AND type = $2', [userId, type]);
	}

	async verifyToken(
		token: string,
		type: TokenType
	): Promise<{ valid: boolean; userId: string | null }> {
		try {
			const result = await pool.query(
				`SELECT user_id FROM EmailTokens 
				WHERE token = $1 
				AND type = $2
				AND expires_at > NOW()`,
				[token, type]
			);

			if (!result.rows[0]) {
				return { valid: false, userId: null };
			}

			const userId = result.rows[0].user_id;

			// Delete the used token
			await pool.query('DELETE FROM EmailTokens WHERE token = $1', [token]);

			// If this is an email verification token, update user's verified status
			if (type === 'email_verification') {
				await pool.query('UPDATE Users SET verified = true WHERE id = $1', [userId]);
			}

			return { valid: true, userId };
		} catch (error) {
			console.error('[!] Error in verifyToken:', error);
			return { valid: false, userId: null };
		}
	}

	// For backward compatibility, keep these methods but make them use the new unified system
	async createEmailToken(userId: string, expiresInHours: number = 24): Promise<string | null> {
		return this.createToken(userId, 'email_verification', expiresInHours);
	}

	async deleteEmailToken(userId: string): Promise<void> {
		await this.deleteToken(userId, 'email_verification');
	}

	async verifyEmailToken(token: string): Promise<{ valid: boolean; userId: string | null }> {
		return this.verifyToken(token, 'email_verification');
	}

	async createPasswordResetToken(
		userId: string,
		expiresInHours: number = 1
	): Promise<string | null> {
		return this.createToken(userId, 'password_reset', expiresInHours);
	}

	async deletePasswordResetToken(userId: string): Promise<void> {
		await this.deleteToken(userId, 'password_reset');
	}

	async verifyPasswordResetToken(
		token: string
	): Promise<{ valid: boolean; userId: string | null }> {
		return this.verifyToken(token, 'password_reset');
	}

	async deleteUser(userId: string): Promise<boolean> {
		try {
			await pool.query('DELETE FROM users WHERE id = $1', [userId]);
			await pool.query('DELETE FROM Sessions WHERE owner_id = $1', [userId]);
			return true;
		} catch (error) {
			console.error('[!] Error in deleteUser:', error);
			return false;
		}
	}

	async disableUser(userId: string): Promise<boolean> {
		try {
			const deletionTimestamp = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000);
			await pool.query(
				'UPDATE users SET pending_deletion = true, deletion_timestamp = $1 WHERE id = $2',
				[deletionTimestamp, userId]
			);
			return true;
		} catch (error) {
			console.error('[!] Error in disableUser:', error);
			return false;
		}
	}
}

// Export a singleton instance
export const db = new Database();
