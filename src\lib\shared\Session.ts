export type Permissions = 'texrepairs.default' | 'texrepairs.email.share' | '*';
export interface JsonSessionStorage {
	user?: {
		id: string;
		username: string;
		email: string;
		user_info?: {
			first_name: string;
			last_name: string;
			phone_number: string;
			billing_address?: string;
			billing_city?: string;
			billing_state?: string;
			billing_country?: string;
			billing_postal_code?: string;
		};
		verified: boolean;
	};
	permissions: Permissions[];
	roles?: string[];
	createdAt: number;
}

export interface JsonSession {
	id: string;
	storage: JsonSessionStorage;
}
