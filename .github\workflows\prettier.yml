name: Prettier Check
on:
  push:
    branches:
      - master
      - staging
      - next
  pull_request:
    branches:
      - master
      - staging
      - next
jobs:
  Prettier:
    runs-on: ubuntu-latest
    if: ${{ !contains(github.event.head_commit.message, '-skip') }}
    steps:
      - name: Checkout Repository
        uses: actions/checkout@v4
      - name: Setup Node.js environment
        uses: actions/setup-node@v4.1.0
      - name: Install prettier
        run: npm i
      - name: Run Prettier
        run: npx prettier . --check
