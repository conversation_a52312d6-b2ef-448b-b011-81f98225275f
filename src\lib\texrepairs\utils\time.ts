/**
 * A utility class that wraps around the Date object
 * We user this to make it easier to interact with the Date object instead of
 * using things like `const ONE_HOUR = 60 * 60 * 1000;` etc...
 */
export class Time {
	private static readonly ONE_SECOND = 1000;
	private static readonly ONE_MINUTE = Time.ONE_SECOND * 60;
	private static readonly ONE_HOUR = Time.ONE_MINUTE * 60;
	private static readonly ONE_DAY = Time.ONE_HOUR * 24;
	private static readonly ONE_WEEK = Time.ONE_DAY * 7;
	private static readonly ONE_MONTH = Time.ONE_DAY * 30;
	private static readonly ONE_YEAR = Time.ONE_DAY * 365;

	public constructor(private diff: number = 0) {}

	/**
	 * Adds a number of seconds to the current time
	 */
	public seconds(seconds: number): Time {
		this.diff += seconds * Time.ONE_SECOND;
		return this;
	}

	/**
	 * Adds a number of minutes to the current time
	 */
	public minutes(minutes: number): Time {
		this.diff += minutes * Time.ONE_MINUTE;
		return this;
	}

	/**
	 * Adds a number of hours to the current time
	 */
	public hours(hours: number): Time {
		this.diff += hours * Time.ONE_HOUR;
		return this;
	}

	/**
	 * Adds a number of days to the current time
	 */
	public days(days: number): Time {
		this.diff += days * Time.ONE_DAY;
		return this;
	}

	/**
	 * Adds a number of weeks to the current time
	 */
	public weeks(weeks: number): Time {
		this.diff += weeks * Time.ONE_WEEK;
		return this;
	}

	/**
	 * Adds a number of months to the current time
	 */
	public months(months: number): Time {
		this.diff += months * Time.ONE_MONTH;
		return this;
	}

	/**
	 * Adds a number of years to the current time
	 */
	public years(years: number): Time {
		this.diff += years * Time.ONE_YEAR;
		return this;
	}

	/**
	 * Gets the current time in milliseconds and adds the diff
	 * to get a future time from now.
	 */
	public fromNow(): number {
		return Time.now() + this.diff;
	}

	/**
	 * Similar to `fromNow` but returns the time in seconds
	 */
	public fromNowSecs(): number {
		return this.fromNow() / Time.ONE_SECOND;
	}

	/**
	 * Similar to `fromNow` but returns the date object
	 */
	public toDate(): Date {
		return new Date(this.fromNow());
	}

	/**
	 * Gets the current diff as millis
	 */
	public asMillis(): number {
		return this.diff;
	}

	/**
	 * Gets the current diff in seconds
	 */
	public asSecs(): number {
		return this.diff / Time.ONE_SECOND;
	}

	/**
	 * Gets the current diff in minutes
	 */
	public asMins(): number {
		return this.diff / Time.ONE_MINUTE;
	}

	/**
	 * Gets the current diff in hours
	 */
	public asHours(): number {
		return this.diff / Time.ONE_HOUR;
	}

	/**
	 * Gets the current diff in days
	 */
	public asDays(): number {
		return this.diff / Time.ONE_DAY;
	}

	/**
	 * Gets the current diff in weeks
	 */
	public asWeeks(): number {
		return this.diff / Time.ONE_WEEK;
	}

	/**
	 * Gets the current diff in months
	 */
	public asMonths(): number {
		return this.diff / Time.ONE_MONTH;
	}

	/**
	 * Gets the current diff in years
	 */
	public asYears(): number {
		return this.diff / Time.ONE_YEAR;
	}

	/**
	 * Gets the difference in decades
	 */
	public asDecades(): number {
		return this.diff / (Time.ONE_YEAR * 10);
	}

	/**
	 * Returns the current time in milliseconds
	 */
	public static now(): number {
		return Date.now();
	}

	/**
	 * Returns the current time in seconds
	 */
	public static nowSecs(): number {
		return Date.now() / Time.ONE_SECOND;
	}

	/**
	 * Returns the current time in minutes
	 */
	public static nowMins(): number {
		return Date.now() / Time.ONE_MINUTE;
	}

	/**
	 * Returns the current time in hours
	 */
	public static nowHours(): number {
		return Date.now() / Time.ONE_HOUR;
	}

	/**
	 * Returns the current time in days
	 */
	public static nowDays(): number {
		return Date.now() / Time.ONE_DAY;
	}

	/**
	 * Returns the current time in weeks
	 */
	public static nowWeeks(): number {
		return Date.now() / Time.ONE_WEEK;
	}

	/**
	 * Returns the current time in months
	 */
	public static nowMonths(): number {
		return Date.now() / Time.ONE_MONTH;
	}

	/**
	 * Returns the current time in years
	 */
	public static nowYears(): number {
		return Date.now() / Time.ONE_YEAR;
	}

	/**
	 * Returns elapsed time in milliseconds
	 */
	public static elaspsed(time: number): number {
		return Time.now() - time;
	}

	/**
	 * Returns the elapsed time in seconds (LOSSY)
	 */
	public static elaspsedSecs(time: number): number {
		return Math.floor(Time.nowSecs() - time);
	}

	/**
	 * Returns the elapsed time in minutes (LOSSY)
	 */
	public static elaspsedMins(time: number): number {
		return Math.floor(Time.nowMins() - time);
	}
}
