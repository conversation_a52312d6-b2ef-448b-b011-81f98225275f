<script lang="ts">
	import { fade } from 'svelte/transition';
	import { onMount } from 'svelte';

	interface CodeLine {
		text: string;
		delay: number;
	}

	let visibleLines: CodeLine[] = [];
	let codeLines: CodeLine[] = [
		{ text: 'function transformBusiness() {', delay: 0 },
		{ text: '    const solutions = {', delay: 200 },
		{ text: "        efficiency: 'optimized',", delay: 400 },
		{ text: "        scalability: 'infinite',", delay: 600 },
		{ text: "        innovation: 'unlimited'", delay: 800 },
		{ text: '    };', delay: 1000 },
		{ text: '    return solutions;', delay: 1200 },
		{ text: '}', delay: 1400 }
	];

	onMount(() => {
		codeLines.forEach((line) => {
			setTimeout(() => {
				visibleLines = [...visibleLines, line];
			}, line.delay);
		});
	});
</script>

<div class="space-y-16 py-8" in:fade>
	<!-- Hero Section -->
	<section
		class="relative overflow-hidden rounded-2xl bg-gradient-to-r from-blue-600 to-indigo-600 p-8 md:p-12"
	>
		<div class="relative z-10 max-w-3xl">
			<h1 class="mb-4 text-4xl font-bold md:text-5xl">Custom Software Development</h1>
			<p class="text-xl text-indigo-100">
				Building powerful, scalable software solutions that help your business succeed. From
				point-of-sale systems to complex enterprise applications we have you covered.
			</p>
		</div>
		<div class="absolute right-0 top-0 -z-10 h-full w-full opacity-20">
			<svg
				xmlns="http://www.w3.org/2000/svg"
				fill="none"
				viewBox="0 0 24 24"
				stroke-width="1.5"
				stroke="currentColor"
				class="h-full w-full"
			>
				<path
					stroke-linecap="round"
					stroke-linejoin="round"
					d="M17.25 6.75L22.5 12l-5.25 5.25m-10.5 0L1.5 12l5.25-5.25m7.5-3l-4.5 16.5"
				/>
			</svg>
		</div>
	</section>

	<!-- Features Grid -->
	<section
		class="relative rounded-2xl bg-gradient-to-br from-blue-600/10 via-indigo-600/5 to-transparent p-8"
	>
		<div class="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
			<div
				class="rounded-xl bg-gradient-to-br from-blue-600/20 via-indigo-600/10 to-transparent p-6 backdrop-blur-sm transition-all hover:from-blue-600/30 hover:via-indigo-600/20 hover:to-transparent hover:shadow-lg hover:shadow-blue-500/10"
			>
				<svg
					xmlns="http://www.w3.org/2000/svg"
					fill="none"
					viewBox="0 0 24 24"
					stroke-width="1.5"
					stroke="currentColor"
					class="mb-4 h-12 w-12 text-[var(--accent-primary)]"
				>
					<path
						stroke-linecap="round"
						stroke-linejoin="round"
						d="M3.75 3v11.25A2.25 2.25 0 006 16.5h2.25M3.75 3h-1.5m1.5 0h16.5m0 0h1.5m-1.5 0v11.25A2.25 2.25 0 0118 16.5h-2.25m-7.5 0h7.5m-7.5 0l-1 3m8.5-3l1 3m0 0l.5 1.5m-.5-1.5h-9.5m0 0l-.5 1.5m.75-9l3-3 2.148 2.148A12.061 12.061 0 0116.5 7.605"
					/>
				</svg>
				<h3 class="mb-2 text-xl font-semibold">Point of Sale Systems</h3>
				<p class="text-gray-300">
					Modern, intuitive POS solutions that streamline your business operations and enhance
					customer experience with real-time processing and analytics.
				</p>
			</div>
			<div
				class="rounded-xl bg-gradient-to-br from-blue-600/20 via-indigo-600/10 to-transparent p-6 backdrop-blur-sm transition-all hover:from-blue-600/30 hover:via-indigo-600/20 hover:to-transparent hover:shadow-lg hover:shadow-blue-500/10"
			>
				<svg
					xmlns="http://www.w3.org/2000/svg"
					fill="none"
					viewBox="0 0 24 24"
					stroke-width="1.5"
					stroke="currentColor"
					class="mb-4 h-12 w-12 text-[var(--accent-primary)]"
				>
					<path
						stroke-linecap="round"
						stroke-linejoin="round"
						d="M9 17.25v1.007a3 3 0 01-.879 2.122L7.5 21h9l-.621-.621A3 3 0 0115 18.257V17.25m6-12V15a2.25 2.25 0 01-2.25 2.25H5.25A2.25 2.25 0 013 15V5.25m18 0A2.25 2.25 0 0018.75 3H5.25A2.25 2.25 0 003 5.25m18 0V12a2.25 2.25 0 01-2.25 2.25H5.25A2.25 2.25 0 013 12V5.25"
					/>
				</svg>
				<h3 class="mb-2 text-xl font-semibold">Desktop Applications</h3>
				<p class="text-gray-300">
					Powerful, feature-rich desktop applications built with modern frameworks, offering
					seamless performance and intuitive user interfaces.
				</p>
			</div>
			<div
				class="rounded-xl bg-gradient-to-br from-blue-600/20 via-indigo-600/10 to-transparent p-6 backdrop-blur-sm transition-all hover:from-blue-600/30 hover:via-indigo-600/20 hover:to-transparent hover:shadow-lg hover:shadow-blue-500/10"
			>
				<svg
					xmlns="http://www.w3.org/2000/svg"
					fill="none"
					viewBox="0 0 24 24"
					stroke-width="1.5"
					stroke="currentColor"
					class="mb-4 h-12 w-12 text-[var(--accent-primary)]"
				>
					<path
						stroke-linecap="round"
						stroke-linejoin="round"
						d="M3.75 13.5l10.5-11.25L12 10.5h8.25L9.75 21.75 12 13.5H3.75z"
					/>
				</svg>
				<h3 class="mb-2 text-xl font-semibold">Enterprise Solutions</h3>
				<p class="text-gray-300">
					Scalable enterprise software that grows with your business, featuring advanced security,
					integration capabilities, and comprehensive analytics.
				</p>
			</div>
		</div>
	</section>

	<!-- Development Process -->
	<section
		class="relative rounded-2xl bg-gradient-to-br from-blue-600/5 via-indigo-600/5 to-transparent p-8"
	>
		<div class="relative">
			<h2 class="mb-12 text-center text-3xl font-bold">
				<span class="relative inline-block">
					Our Development Process
					<span
						class="absolute -bottom-2 left-0 h-1 w-full bg-gradient-to-r from-[var(--accent-primary)] to-[var(--accent-secondary)]"
					></span>
				</span>
			</h2>
			<div class="grid gap-8 md:grid-cols-2 lg:grid-cols-4">
				<div
					class="group relative overflow-hidden rounded-xl bg-gradient-to-br from-blue-600/10 to-transparent p-8 transition-all hover:shadow-lg hover:shadow-blue-500/10"
				>
					<div class="bg-[var(--card-container-bg)]/80 absolute inset-0 backdrop-blur-sm"></div>
					<div class="relative">
						<div class="mb-6 flex items-center gap-4">
							<div
								class="flex h-12 w-12 items-center justify-center rounded-lg bg-gradient-to-br from-blue-500 to-indigo-500"
							>
								<svg
									xmlns="http://www.w3.org/2000/svg"
									fill="none"
									viewBox="0 0 24 24"
									stroke-width="1.5"
									stroke="white"
									class="h-6 w-6"
								>
									<path
										stroke-linecap="round"
										stroke-linejoin="round"
										d="M9.75 3.104v5.714a2.25 2.25 0 01-.659 1.591L5 14.5M9.75 3.104c-.251.023-.501.05-.75.082m.75-.082a24.301 24.301 0 014.5 0m0 0v5.714c0 .597.237 1.17.659 1.591L19.8 15.3M14.25 3.104c.251.023.501.05.75.082M19.8 15.3l-1.57.393A9.065 9.065 0 0112 15a9.065 9.065 0 00-6.23-.693L5 14.5m14.8.8l1.402 1.402c1.232 1.232.65 3.318-1.067 3.611A48.309 48.309 0 0112 21c-2.773 0-5.491-.235-8.135-.687-1.718-.293-2.3-2.379-1.067-3.61L5 14.5"
									/>
								</svg>
							</div>
							<h3 class="text-2xl font-semibold text-[var(--accent-primary)]">
								Design & Architecture
							</h3>
						</div>
						<p class="text-gray-300">Creating robust and modern designs</p>
					</div>
				</div>
				<div
					class="group relative overflow-hidden rounded-xl bg-gradient-to-br from-blue-600/10 to-transparent p-8 transition-all hover:shadow-lg hover:shadow-blue-500/10"
				>
					<div class="bg-[var(--card-container-bg)]/80 absolute inset-0 backdrop-blur-sm"></div>
					<div class="relative">
						<div class="mb-6 flex items-center gap-4">
							<div
								class="flex h-12 w-12 items-center justify-center rounded-lg bg-gradient-to-br from-blue-500 to-indigo-500"
							>
								<svg
									xmlns="http://www.w3.org/2000/svg"
									fill="none"
									viewBox="0 0 24 24"
									stroke-width="1.5"
									stroke="white"
									class="h-6 w-6"
								>
									<path
										stroke-linecap="round"
										stroke-linejoin="round"
										d="M16.023 9.348h4.992v-.001M2.985 19.644v-4.992m0 0h4.992m-4.993 0l3.181 3.183a8.25 8.25 0 0013.803-3.7M4.031 9.865a8.25 8.25 0 0113.803-3.7l3.181 3.182m0-4.991v4.99"
									/>
								</svg>
							</div>
							<h3 class="text-2xl font-semibold text-[var(--accent-primary)]">Iteration</h3>
						</div>
						<p class="text-gray-300">
							Work with you to iterate and make changes on the design and architecture making sure
							we're on the same page
						</p>
					</div>
				</div>
				<div
					class="group relative overflow-hidden rounded-xl bg-gradient-to-br from-blue-600/10 to-transparent p-8 transition-all hover:shadow-lg hover:shadow-blue-500/10"
				>
					<div class="bg-[var(--card-container-bg)]/80 absolute inset-0 backdrop-blur-sm"></div>
					<div class="relative">
						<div class="mb-6 flex items-center gap-4">
							<div
								class="flex h-12 w-12 items-center justify-center rounded-lg bg-gradient-to-br from-blue-500 to-indigo-500"
							>
								<svg
									xmlns="http://www.w3.org/2000/svg"
									fill="none"
									viewBox="0 0 24 24"
									stroke-width="1.5"
									stroke="white"
									class="h-6 w-6"
								>
									<path
										stroke-linecap="round"
										stroke-linejoin="round"
										d="M17.25 6.75L22.5 12l-5.25 5.25m-10.5 0L1.5 12l5.25-5.25m7.5-3l-4.5 16.5"
									/>
								</svg>
							</div>
							<h3 class="text-2xl font-semibold text-[var(--accent-primary)]">Development</h3>
						</div>
						<p class="text-gray-300">Agile development with regular updates and feedback</p>
					</div>
				</div>
				<div
					class="group relative overflow-hidden rounded-xl bg-gradient-to-br from-indigo-600/10 to-transparent p-8 transition-all hover:shadow-lg hover:shadow-indigo-500/10"
				>
					<div class="bg-[var(--card-container-bg)]/80 absolute inset-0 backdrop-blur-sm"></div>
					<div class="relative">
						<div class="mb-6 flex items-center gap-4">
							<div
								class="flex h-12 w-12 items-center justify-center rounded-lg bg-gradient-to-br from-indigo-500 to-purple-500"
							>
								<svg
									xmlns="http://www.w3.org/2000/svg"
									fill="none"
									viewBox="0 0 24 24"
									stroke-width="1.5"
									stroke="white"
									class="h-6 w-6"
								>
									<path
										stroke-linecap="round"
										stroke-linejoin="round"
										d="M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
									/>
								</svg>
							</div>
							<h3 class="text-2xl font-semibold text-[var(--accent-primary)]">
								Testing & Deployment
							</h3>
						</div>
						<p class="text-gray-300">Rigorous testing and smooth deployment process</p>
					</div>
				</div>
			</div>
		</div>
	</section>

	<!-- Call to Action -->
	<section
		class="relative overflow-hidden rounded-2xl bg-gradient-to-br from-indigo-600/20 via-purple-600/20 to-transparent p-12"
	>
		<div class="bg-[var(--card-container-bg)]/40 absolute inset-0 backdrop-blur-sm"></div>
		<div class="relative mx-auto max-w-3xl text-center">
			<h2 class="mb-4 text-4xl font-bold">
				Ready to transform your business with custom software?
			</h2>
			<p class="mb-8 text-xl text-indigo-100">
				Let's discuss how our software development expertise can help you achieve your goals. We're
				here to turn your ideas into powerful solutions.
			</p>
			<a
				href="/account/services/software"
				class="hover:shadow-[var(--accent-primary)]/20 group relative inline-flex items-center gap-2 overflow-hidden rounded-lg bg-gradient-to-r from-[var(--accent-primary)] to-[var(--accent-secondary)] px-10 py-4 text-lg font-semibold text-white transition-all hover:shadow-lg"
			>
				<span class="relative z-10">Start Your Project</span>
				<svg
					xmlns="http://www.w3.org/2000/svg"
					fill="none"
					viewBox="0 0 24 24"
					stroke-width="2"
					stroke="currentColor"
					class="h-5 w-5 transition-transform group-hover:translate-x-1"
				>
					<path
						stroke-linecap="round"
						stroke-linejoin="round"
						d="M13.5 4.5L21 12m0 0l-7.5 7.5M21 12H3"
					/>
				</svg>
			</a>
		</div>
	</section>
</div>

<style>
	.code-line {
		opacity: 1;
		transform: translateY(0);
	}

	@keyframes fadeInUp {
		from {
			opacity: 0;
			transform: translateY(10px);
		}
		to {
			opacity: 1;
			transform: translateY(0);
		}
	}

	@keyframes float {
		0%,
		100% {
			transform: translateY(0) translateX(0);
		}
		50% {
			transform: translateY(-20px) translateX(10px);
		}
	}

	.code-block {
		font-family: 'Fira Mono', 'Consolas', 'Menlo', monospace;
		font-size: 1rem;
		color: #e0e7ef;
		background: none;
		padding: 0;
		margin: 0;
		line-height: 1.7;
		display: block;
	}
	.token-keyword {
		color: #7dd3fc;
		font-weight: bold;
	}
	.token-function {
		color: #f472b6;
	}
	.token-variable {
		color: #facc15;
	}
	.token-property {
		color: #a5b4fc;
	}
	.token-string\' {
		color: #6ee7b7;
	}

	@keyframes gradient-x {
		0%,
		100% {
			background-position: 0% 50%;
		}
		50% {
			background-position: 100% 50%;
		}
	}

	.animate-gradient-x {
		background-size: 200% 200%;
		animation: gradient-x 3s ease infinite;
		mask:
			linear-gradient(#fff 0 0) content-box,
			linear-gradient(#fff 0 0);
		mask-composite: exclude;
		padding: 1px;
	}
</style>
