<script lang="ts">
	import { marked } from 'marked';
	import { gfmHeadingId } from 'marked-gfm-heading-id';
	import markedAlert from 'marked-alert';
	import { onMount } from 'svelte';
	import 'github-markdown-css';

	interface Props {
		content: string;
	}

	let { content }: Props = $props();

	let markup: HTMLElement | undefined = $state();

	onMount(async () => {
		marked.use(gfmHeadingId());
		marked.use(markedAlert());
		const html = await marked(content);
		if (markup) {
			markup.innerHTML = html;
			generateHeadingLinks();
		}
	});

	function generateHeadingLinks() {
		if (!markup) return;
		const headings = [...markup.querySelectorAll('h1, h2, h3')];
		headings.forEach((heading: Element) => {
			const id = heading.getAttribute('id');
			if (id) {
				heading.innerHTML = `<a id="${id}" href="#${id}" style="color: white; display: inline-flex; flex-direction: row; gap: 12px; align-items: center;" class="lulspanxd">
                    ${heading.textContent}
                    <span>
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-link"><path d="M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71"/><path d="M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71"/></svg>
                    </span>
                </a>`;
			}
		});
	}

	function generateList(links: { id: string; title: string; depth: number }[]) {
		return `
            <ul>
                ${links
									.map(
										(link) =>
											`<li class="${link.depth > 1 ? 'pl-4' : ''}"><a href="#${link.id}">${
												link.title
											}</a></li>`
									)
									.join('')}
            </ul>
        `;
	}
</script>

<article class="markdown-body" bind:this={markup}></article>

<style lang="scss" global>
	.lulspanxd {
		span {
			display: none;
		}

		&:hover {
			// remove underline on hover
			text-decoration: none !important;

			span {
				display: inline-flex;
			}
		}
	}
	.markdown-body {
		box-sizing: border-box;
		min-width: 200px;
		max-width: 980px;
		margin: 0 auto;
		padding: 45px;
		background-color: transparent;

		p {
			color: rgb(195, 195, 195);
		}

		ul,
		ol {
			color: rgb(195, 195, 195);
		}

		ol {
			list-style-type: decimal;
		}

		ul {
			list-style-type: disc;
		}

		li::marker {
			color: white;
		}
	}

	@media (max-width: 767px) {
		.markdown-body {
			padding: 15px;
		}
	}
</style>
