export type BoltId = string; // 64-bit unsigned integer with embedded metadata

export type Language =
	| 'en-US'
	| 'en-AU'
	| 'en-GB'
	| 'es-MX'
	| 'es-ES'
	| 'fr-CA'
	| 'fr-FR'
	| 'de-DE'
	| 'de-AT';

export type TransactionType =
	| 'charge'
	| 'subscription'
	| 'refund'
	| 'chargeback'
	| 'provisional_charge'
	| 'provisional_refund';

export interface User {
	id: string;
	email: string;
	username: string | null;
	password_hash: string;
	language: Language;
	roles: string[];
	permissions: string[];
	verified: boolean;
	provider: string;
	pending_deletion: boolean;
	created_at: Date;
	updated_at: Date;
}

// Type for user creation/registration (omits sensitive fields)
export type UserCreate = Omit<User, 'id' | 'password_hash' | 'created_at' | 'updated_at'> & {
	password: string;
};

export interface UserInfo {
	id: string;
	first_name: string | null;
	last_name: string | null;
	phone_number: string | null;
	billing_address: string | null;
	billing_city: string | null;
	billing_state: string | null;
	billing_country: string | null;
	billing_postal_code: string | null;
}

// Update UserResponse to include user info
export type UserResponse = Omit<User, 'password_hash'> & {
	user_info?: UserInfo;
};

// Type for creating user info
export type UserInfoCreate = Omit<UserInfo, 'id'>;

export interface Transaction {
	id: string;
	type: TransactionType;
	amount: number;
	amount_refunded: number;
	currency: string;
	provider: string;
	billing?: BillingAddress;
	created_at: number;
	status: string;
	payment_method?: PaymentMethod;
	paid: boolean;
	fraud_factor?: FraudFactor | null;
	delivery_id?: string | null;
	item_id: string;
	statement_descriptor: string;
}

export interface BillingAddress {
	line1: string;
	line2?: string;
	city: string;
	state: string;
	postal_code: string;
	country: string;
}

export interface PaymentMethod {
	type: string;
	last4: string;
	brand: string;
}

export interface FraudFactor {
	score: number;
	risk_level: string;
	factors: string[];
}

export interface Application {
	id: string;
	name: string;
	icon?: string;
	secret: string;
	redirect_uri: string[];
	description?: string;
	enabled: boolean;
	status: any; // JSON object
	owner_id: string;
	permissions: string[];
	scopes: string[];
	created_at: number;
	expires_at?: number;
}

export interface AccessToken {
	id: number;
	access_token: string;
	user_id: string;
	client_id: string;
	expires: number;
	scopes: string[];
	created_at: number;
	updated_at: number;
}

export interface Session {
	id: string;
	token: string;
	expires_at: Date;
	owner_id: string;
	owner_type: string;
	last_used_at: Date;
	created_at: Date;
}

export type TokenType = 'email_verification' | 'password_reset';

export interface EmailToken {
	user_id: string;
	token: string;
	type: TokenType;
	created_at: Date;
	expires_at: Date;
}

export interface Role {
	id: number;
	name: string;
	description?: string;
	permissions: string[];
	parent_ids: number[];
	created_at: number;
	updated_at: number;
}

export interface Scope {
	name: string;
	description?: string;
	required: boolean;
	created_at: number;
	updated_at: number;
}

// ... rest of the file stays the same ...
// ... existing code ...
