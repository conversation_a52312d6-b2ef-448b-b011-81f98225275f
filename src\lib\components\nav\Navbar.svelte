<script lang="ts">
	import { page } from '$app/state';
	import type { JsonSessionStorage } from '$lib/shared/Session';
	import '$lib/css/components/Navbar.scss';
	import Logo from '../icon/Logo.svelte';
	import { onMount } from 'svelte';
	import Hamburger from './hamburger/Hamburger.svelte';

	let sessionUser: JsonSessionStorage['user'] | undefined = page.data.sessionUser;
	let isMenuOpen = $state(false);

	// Toggle body scroll when menu opens/closes
	$effect(() => {
		if (isMenuOpen) {
			document.body.style.overflow = 'hidden';
		} else {
			document.body.style.overflow = '';
		}
	});
</script>

<nav
	class="color-[var(--nav-primary)] flex h-[70px] w-full select-none flex-row items-center justify-between bg-[var(--nav-bg)] px-8"
>
	<!-- This is the left side fo the navbar -->
	<div class="flex w-full text-left">
		<!-- Hamburger -->
		<Hamburger bind:checked={isMenuOpen} {sessionUser}>
			{#snippet content()}
				<button
					class="flex h-10 w-10 items-center justify-center rounded-lg text-white transition-colors hover:bg-white/5"
					onclick={() => {
						isMenuOpen = !isMenuOpen;
					}}
					aria-label="Toggle navigation menu"
				>
					<span class="icon-[mingcute--menu-line] h-6 w-6"></span>
				</button>
			{/snippet}
		</Hamburger>
	</div>

	<!-- Logo -->
	<div class="flex w-full justify-center">
		<a href="/" class="text-2xl font-bold text-white" data-sveltekit-reload>
			<Logo fill="#fff" />
		</a>
	</div>

	{#if sessionUser?.id !== undefined}
		<div class="flex w-full flex-row items-center justify-end gap-2">
			<a href="/logout" class="text-gl ml-4 font-bold text-white" data-sveltekit-reload>Logout</a>
		</div>
	{:else}
		<div class="flex w-full flex-row items-center justify-end gap-2">
			<a href="/login" class="text-md font-bold text-[var(--text-nav)]">Login</a>
		</div>
	{/if}
</nav>

<style>
	/* Hide scrollbar but allow scrolling */
	.no-scrollbar {
		-ms-overflow-style: none; /* IE and Edge */
		scrollbar-width: none; /* Firefox */
	}

	.no-scrollbar::-webkit-scrollbar {
		display: none; /* Chrome, Safari and Opera */
	}
</style>
