// See https://svelte.dev/docs/kit/types#app.d.ts

import type { Session } from './lib/server/sessions/index.js';

// for information about these interfaces
declare global {
	namespace App {
		interface Error {
			status?: number;
			message?: string;
		}
		interface Locals {
			cookies?: Record<string, string>;
			session?: Session;
		}
		// interface PageData {}
		// interface PageState {}
		// interface Platform {}
	}
}

export {};
