<script lang="ts">
	import { page } from '$app/stores';
	import { fly, fade, scale } from 'svelte/transition';
	import { onMount } from 'svelte';

	let particles: { x: number; y: number; size: number; speedX: number; speedY: number }[] = [];
	let container: HTMLElement;

	// Generate particles
	onMount(() => {
		const numParticles = 50;
		for (let i = 0; i < numParticles; i++) {
			particles.push({
				x: Math.random() * 100,
				y: Math.random() * 100,
				size: Math.random() * 3 + 1,
				speedX: (Math.random() - 0.5) * 0.2,
				speedY: (Math.random() - 0.5) * 0.2
			});
		}

		// Animate particles
		function animateParticles() {
			particles = particles.map((p) => ({
				...p,
				x: (p.x + p.speedX + 100) % 100,
				y: (p.y + p.speedY + 100) % 100
			}));
			requestAnimationFrame(animateParticles);
		}

		animateParticles();
	});

	function goHome() {
		window.location.href = '/';
	}

	function randomMessage() {
		const messages = [
			"You've stumbled into a void.",
			'This is not the page you are looking for (probably).',
			'Uh oh, whoopsie how did we get here?',
			'You opened the wrong door, try the other one.',
			"Oh hey! You've let the light in by finding this page, please click home page so it can be dark and I can sleep.",
			"I didn't spend forever testing this website just for you to find the 404 page. Are you really undermining all my hard work?",
			"I'm not even mad, I'm just disappointed.",
			'Why are you even reading this? You should have clicked return home by now.',
			"I didn't spend a bunch of time designing this page just for you click away.",
			"Sorry, I'm not as cool as google, there's no game here.",
			"This is why you don't go mining at night.",
			"Something tells me you haven't drank enough water today.",
			'What do you call a twitchy cow?',
			'Beef jerky.'
		];
		return messages[Math.floor(Math.random() * messages.length)];
	}
</script>

<svelte:head>
	<style>
		.error-container {
			min-height: 100vh;
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
			background: linear-gradient(
				135deg,
				var(--error-bg-gradient-from),
				var(--error-bg-gradient-to)
			);
			position: relative;
			overflow: hidden;
			padding: 2rem;
		}

		.particle {
			position: absolute;
			background: var(--error-particle);
			border-radius: 50%;
			opacity: 0.6;
			pointer-events: none;
			filter: blur(1px);
		}

		.error-content {
			text-align: center;
			z-index: 1;
			max-width: 800px;
		}

		.error-code {
			font-size: 8rem;
			font-weight: 900;
			color: var(--error-code-color);
			margin: 0;
			line-height: 1;
			opacity: 0.9;
		}

		.error-message {
			font-size: 2rem;
			color: var(--error-text-primary);
			margin: 1rem 0 2rem;
		}

		.error-description {
			color: var(--error-text-secondary);
			font-size: 1.2rem;
			margin-bottom: 3rem;
			max-width: 600px;
			margin-left: auto;
			margin-right: auto;
		}

		.home-button {
			background: var(--error-button-bg);
			color: var(--error-button-text);
			border: none;
			padding: 1rem 2rem;
			font-size: 1.1rem;
			border-radius: 50px;
			cursor: pointer;
			transition: all 0.3s ease;
			backdrop-filter: blur(10px);
			border: 1px solid rgba(255, 255, 255, 0.1);
		}

		.home-button:hover {
			background: var(--error-button-hover);
			transform: translateY(-2px);
			box-shadow: 0 0 20px var(--error-glow);
		}
	</style>
</svelte:head>

<div class="error-container" bind:this={container}>
	{#each particles as particle, i}
		<div
			class="particle"
			style="
                left: {particle.x}%;
                top: {particle.y}%;
                width: {particle.size}px;
                height: {particle.size}px;
            "
		></div>
	{/each}

	<div class="error-content" in:fly={{ y: 50, duration: 1000, delay: 200 }}>
		<h1 class="error-code">
			{$page.status}
		</h1>

		<h2 class="error-message" in:fade={{ duration: 800, delay: 600 }}>
			{$page.error?.message || 'Something went wrong'}
		</h2>

		<p class="error-description" in:fade={{ duration: 800, delay: 800 }}>
			{randomMessage()}
		</p>

		<button class="home-button" on:click={goHome} in:fade={{ duration: 800, delay: 1000 }}>
			Return Home
		</button>
	</div>
</div>
