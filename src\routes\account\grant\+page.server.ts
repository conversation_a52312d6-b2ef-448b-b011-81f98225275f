import { error, redirect } from '@sveltejs/kit';
import type { PageServerLoad } from './$types';
import { db } from '@backend/db';

export const load: PageServerLoad = async ({ url }) => {
	const token = url.searchParams.get('token');

	if (!token) {
		return {
			error: true,
			message: 'Missing verification token'
		};
	}

	const { valid } = await db.verifyEmailToken(token);

	if (!valid) {
		return {
			error: true,
			message: 'Invalid or expired verification token'
		};
	}

	// If we get here, the token was valid and the user is now verified
	return {
		success: true,
		message: 'Email successfully verified! You can now log in to your account.'
	};
};
