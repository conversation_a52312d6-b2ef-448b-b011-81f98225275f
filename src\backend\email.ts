import { readFile } from 'fs/promises';
import { join } from 'path';
import sgMail from '@sendgrid/mail';
import { config } from 'dotenv';

config();

// Initialize SendGrid with API key
sgMail.setApiKey(process.env.SENDGRID_API_KEY || '');

export class EmailService {
	private static async getEmailTemplate(templateName: string): Promise<string> {
		try {
			const templatePath = join(process.cwd(), 'resources', 'email', templateName);
			return await readFile(templatePath, 'utf8');
		} catch (error) {
			console.error('[!] Error reading email template:', error);
			throw new Error('Failed to read email template');
		}
	}

	static async sendVerificationEmail(email: string, token: string): Promise<boolean> {
		try {
			// Get the base URL from environment variable or default to localhost
			const baseUrl = process.env.EMAIL_BASE_URL || 'http://localhost:3000';
			const confirmUrl = `${baseUrl}/account/grant?token=${token}`;

			let htmlContent = (await this.getEmailTemplate('confirm-email.html'))
				.replace(/{{confirmUrl}}/g, confirmUrl)
				.replace(/{{email}}/g, email);

			const msg = {
				to: email,
				from: '<EMAIL>', // This should be a verified sender
				subject: 'Confirm Your Email - Texrepairs',
				html: htmlContent
			};

			await sgMail.send(msg);
			return true;
		} catch (error) {
			console.error('[!] Error sending verification email:', error);
			return false;
		}
	}

	static async sendPasswordResetEmail(email: string, token: string): Promise<boolean> {
		try {
			// Get the base URL from environment variable or default to localhost
			const baseUrl = process.env.EMAIL_BASE_URL || 'http://localhost:3000';
			const resetUrl = `${baseUrl}/account/reset?token=${token}`;
			let htmlContent = await this.getEmailTemplate('reset-password.html');

			const logoUrl = 'https://texrepairs.com/icon-white.png';
			htmlContent = htmlContent.replace(
				/<svg[^>]*>[\s\S]*?<\/svg>/,
				`<img src="${logoUrl}" alt="Texrepairs Logo" width="171" height="78" style="max-width: 100%; height: auto; display: block; margin: 0 auto;">`
			);

			htmlContent = htmlContent.replace(/{{resetUrl}}/g, resetUrl).replace(/{{email}}/g, email);

			const msg = {
				to: email,
				from: '<EMAIL>', // This should be a verified sender
				subject: 'Reset Your Password - Texrepairs',
				html: htmlContent
			};

			await sgMail.send(msg);
			return true;
		} catch (error) {
			console.error('[!] Error sending password reset email:', error);
			return false;
		}
	}

	static async sendDeleteAccountEmail(email: string, username: string): Promise<boolean> {
		try {
			// Read the email template
			let htmlContent = (await this.getEmailTemplate('delete-account.html'))
				.replace(/{{username}}/g, username)
				.replace(/{{email}}/g, email);

			const msg = {
				to: email,
				from: '<EMAIL>', // This should be a verified sender
				subject: 'Account Deletion - Texrepairs',
				html: htmlContent
			};

			await sgMail.send(msg);
			return true;
		} catch (error) {
			console.error('[!] Error sending delete account email:', error);
			return false;
		}
	}
}
