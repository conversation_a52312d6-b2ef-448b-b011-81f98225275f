<script lang="ts">
	import type { WithElementRef } from 'bits-ui';
	import type { HTMLAttributes } from 'svelte/elements';

	let {
		ref = $bindable(null),
		class: className,
		children,
		...restProps
	}: WithElementRef<HTMLAttributes<HTMLDivElement>> = $props();
</script>

<div
	bind:this={ref}
	class="grid grid-cols-1 gap-3 sm:grid-cols-2 md:gap-4 {className || ''}"
	{...restProps}
>
	{@render children?.()}
</div>
