<script lang="ts">
	import Logo from './icon/Logo.svelte';

	const currentYear = new Date().getFullYear();

	const footerLinks = {
		company: [
			{ name: 'About Us', href: '/about' },
			{ name: 'Careers', href: '/careers' },
			{ name: 'Contact', href: '/contact' },
			{ name: 'Blog', href: '/blog' }
		],
		support: [
			{ name: 'Help Center', href: '/help' },
			{ name: 'FAQ', href: '/faq' },
			{ name: 'Terms of Service', href: '/legal/terms' },
			{ name: 'Privacy Policy', href: '/legal/privacy' }
		]
	};

	let { class: className = '' } = $props();
</script>

<footer class="relative overflow-hidden bg-[var(--card-container-bg)] {className}">
	<div class="relative mx-auto max-w-6xl px-4 py-12">
		<div class="grid gap-8 md:grid-cols-2 lg:grid-cols-4">
			<div class="space-y-4">
				<Logo fill="currentColor" class="h-8 w-auto" />
				<p class="text-sm text-[var(--text-secondary)]">
					High quality software, Embedded Systems and websites made from the ground up.
				</p>
			</div>

			<div>
				<h3 class="mb-4 text-lg font-semibold text-[var(--text-primary)]">Company</h3>
				<ul class="space-y-2">
					{#each footerLinks.company as link}
						<li>
							<a
								href={link.href}
								class="text-[var(--text-secondary)] transition-colors duration-200 hover:text-[var(--btn-primary)]"
								data-sveltekit-reload
							>
								{link.name}
							</a>
						</li>
					{/each}
				</ul>
			</div>

			<div>
				<h3 class="mb-4 text-lg font-semibold text-[var(--text-primary)]">Support</h3>
				<ul class="space-y-2">
					{#each footerLinks.support as link}
						<li>
							<a
								href={link.href}
								class="text-[var(--text-secondary)] transition-colors duration-200 hover:text-[var(--btn-primary)]"
								data-sveltekit-reload
							>
								{link.name}
							</a>
						</li>
					{/each}
				</ul>
			</div>

			<div>
				<h3 class="mb-4 text-lg font-semibold text-[var(--text-primary)]">Connect With Us</h3>
				<div class="flex space-x-4">
					<a
						href="https://facebook.com/texrepairs"
						class="group flex h-10 w-10 items-center justify-center rounded-full bg-[var(--card-bg)] text-[var(--text-secondary)] transition-all duration-300 hover:bg-[var(--btn-primary)] hover:text-white"
						target="_blank"
						rel="noopener noreferrer"
						aria-label="Visit our Facebook page"
						data-sveltekit-reload
					>
						<span class="icon-[line-md--facebook] h-5 w-5"></span>
					</a>
					<a
						href="https://twitter.com/texrepairs"
						class="group flex h-10 w-10 items-center justify-center rounded-full bg-[var(--card-bg)] text-[var(--text-secondary)] transition-all duration-300 hover:bg-[var(--btn-primary)] hover:text-white"
						target="_blank"
						rel="noopener noreferrer"
						aria-label="Visit our Twitter page"
						data-sveltekit-reload
					>
						<span class="icon-[ri--twitter-x-fill] h-5 w-5"></span>
					</a>
					<a
						href="https://instagram.com/texrepair"
						class="group flex h-10 w-10 items-center justify-center rounded-full bg-[var(--card-bg)] text-[var(--text-secondary)] transition-all duration-300 hover:bg-[var(--btn-primary)] hover:text-white"
						target="_blank"
						rel="noopener noreferrer"
						aria-label="Visit our Instagram page"
						data-sveltekit-reload
					>
						<span class="icon-[mdi--instagram] h-5 w-5"></span>
					</a>
				</div>
			</div>
		</div>

		<div class="mt-12 border-t border-[var(--border-color)] pt-8">
			<div class="flex flex-col items-center justify-between gap-4 sm:flex-row">
				<p class="text-sm text-[var(--text-secondary)]">
					&copy; 2024-{currentYear} TexRepairs LLC. All rights reserved.
				</p>
				<div class="flex items-center space-x-4">
					<span class="text-sm text-[var(--text-secondary)]">Dallas, Texas</span>
					<span class="text-sm text-[var(--text-secondary)]"><EMAIL></span>
				</div>
			</div>
		</div>
	</div>
</footer>
