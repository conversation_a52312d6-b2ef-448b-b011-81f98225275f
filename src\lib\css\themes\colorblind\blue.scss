.cb-blue-dark {
	--bg: #1c2a34;
	--bg-secondary: #2a3b46;
	--nav-bg: #1e2d39;
	--text-primary: #ffffff;
	--text-secondary: #ffffff;
	--accent: #1e2d39;

	/* Debug */
	--bg-debug-header: #6e6e6e;
	--bg-debug: #0f1417;
	--text-debug-value: #39a9ff;
	--text-debug-key: #ffffff;
	--text-debug-primary: #ffffff;
	--text-debug-secondary: #a3a3a3;
	--bg-debug-code: rgba(0, 0, 0, 0.568);

	/* Button bg */
	--btn-primary: #0d2a4c;
	--btn-hover-primary: #0f3a6d;
	--btn-secondary: #323639;
	--btn-hover-secondary: #424547;
	--btn-tertiary: #323639;
	--btn-hover-tertiary: #424547;
	--btn-error: #da2e2e;
	--btn-hover-error: #fa6a6a;
	--btn-success: #19db50;
	--btn-hover-success: #59d47c;
	--btn-warning: #e08800;
	--btn-hover-warning: #e0a333;
	--btn-info: #ff6600;
	--btn-hover-info: #ff8533;

	/* Cards */
	--card-container-bg: #2a3b46;
	--card-bg: #3a4e5d;
	--card-button-bg: #0d2a4c;
	--card-text-primary: #f0f0f0;
	--card-text-secondary: #d1d5db;

	/* Miscellaneous */
	--tr-blue: #5a90b2;
	--tr-blue-alt: #4a8db3;

	--footer-bg: #2a3b46;

	/* Hero section */
	--hero-bg: #1a232b;
	--hero-text-color: #ffffff;

	--hero-title-color: #5e97b7;
	--hero-button-text: #ffffff;
	--hero-button-gradient-from: #4c8fb3;
	--hero-button-gradient-to: #5e97b7;
	--hero-button-gradient-hover-from: #3b82f6;
	--hero-button-gradient-hover-to: #4c8fb3;

	--hero-spotlight-bg: #2a3b46;

	/* Other generic colors */
	--teal-byte: #112731;
	--text-primary: #ffffff;
	--text-secondary: #a3a3a3;

	/* Gradient */
	--gradient-start: white;
	--gradient-end: #86d0f5;

	/* Error Page */
	--error-bg-gradient-from: #1a232b;
	--error-bg-gradient-to: #1c2a34;
	--error-text-primary: #ffffff;
	--error-text-secondary: #5e97b7;
	--error-accent: #4a8db3;
	--error-glow: #4c8fb3;
	--error-particle: #86d0f5;
	--error-button-bg: rgba(74, 141, 179, 0.2);
	--error-button-hover: rgba(74, 141, 179, 0.3);
	--error-button-text: #ffffff;
	--error-code-color: #39a9ff;
}

.cb-blue-light {
	--bg: #b7d5ff;
	--bg-secondary: #f7f7f7;
	--nav-bg: #8daedf;
	--text-primary: #121212;
	--text-secondary: #444444;
	--accent: #0091ff;

	/* Debug */
	--bg-debug: #d1d1d1;
	--text-debug: #ff6600;

	/* Button bg */
	--btn-primary: #006bb3;
	--btn-hover-primary: #005fa3;
	--btn-secondary: #8dabd9;
	--btn-hover-secondary: #7aa0d8;
	--btn-error: #d9534f;
	--btn-hover-error: #fa6a6a;
	--btn-success: #4caf50;
	--btn-hover-success: #3e8e41;
	--btn-warning: #ff9800;
	--btn-hover-warning: #e68900;
	--btn-info: #ff6600;
	--btn-hover-info: #e65c00;

	/* Cards */
	--card-container-bg: #90beff;
	--card-bg: #d5edfd;
	--card-button-bg: #006bb3;
	--card-text-primary: #102f43;
	--card-text-secondary: #4f4f4f;

	/* Miscellaneous */
	--tr-blue: #6aa6cf;
	--tr-blue-alt: #3e8bb3;

	--footer-bg: #e3e3e3;

	/* Hero section */
	--hero-bg: #f5f5f5;
	--hero-text-color: #121212;

	--hero-title-color: #007acc;
	--hero-button-text: #ffffff;
	--hero-button-gradient-from: #3e8bb3;
	--hero-button-gradient-to: #007acc;
	--hero-button-gradient-hover-from: #005fa3;
	--hero-button-gradient-hover-to: #3e8bb3;

	--hero-spotlight-bg: #e3eaf0;

	/* Other generic colors */
	--teal-byte: #e1eeff;
	--text-primary: #102f43;
	--text-secondary: #2d485a;

	/* Gradient */
	--gradient-start: #002a46;
	--gradient-end: #4db8ff;

	/* Error Page */
	--error-bg-gradient-from: #f5f5f5;
	--error-bg-gradient-to: #e3e3e3;
	--error-text-primary: #102f43;
	--error-text-secondary: #007acc;
	--error-accent: #3e8bb3;
	--error-glow: #005fa3;
	--error-particle: #4db8ff;
	--error-button-bg: rgba(62, 139, 179, 0.2);
	--error-button-hover: rgba(62, 139, 179, 0.3);
	--error-button-text: #102f43;
	--error-code-color: #ff6600;
}
