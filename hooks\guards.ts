import { building } from '$app/environment';
import { error, json, redirect, type RequestEvent } from '@sveltejs/kit';
import type { Hook, MaybePromise } from '.';
import type _App from '../src/app';
import * as _ from '$env/static/private';

type GuardResponse = {
	status: number;
	message: string;
	[key: string]: any;
};

class GuardAlwaysPass {}
class RedirectGuard {
	constructor(public url: string) {}
}

type FulfillGuard = (
	url: URL,
	input: RequestEvent & { locals: globalThis.App.Locals }
) => MaybePromise<boolean | GuardAlwaysPass | GuardResponse>;

type Guard = {
	/**
	 * The name of the guard
	 */
	name?: string;
	/**
	 * The path of the guard
	 * If regexp, it will be tested against the request path.
	 */
	path?: string | RegExp;
	/**
	 * The guard function expects a URL and the request event
	 * We use this to check if the guard is fulfilled with the following:
	 * - If the guard returns `GuardAlwaysPass`, the guard will be skipped an
	 * the request will be allowed through
	 * - If the guard returns `RedirectGuard`, the guard will redirect to the given URI
	 * - If the guard returns `true`, the request will be blocked
	 * - If the guard returns an `object`, the request will be blocked and the object will be returned
	 * - If the guard returns `false`, the request will be allowed through, but will continue to check the extends
	 */
	guard?: FulfillGuard;
	/**
	 * Extends the following guards
	 */
	extends?: string[];
	/**
	 * If api is true, the guard will return a json response
	 */
	api?: boolean;
};

/**
 * These are the guarded endpoints that require a promise to be fulfilled
 *
 * todo: Maybe move these to ../guards.ts
 */
const GUARDS: Guard[] = [
	{
		name: 'auth-guard',
		guard: async (_, input) => {
			return input.locals.session?.storage?.user !== undefined
				? false
				: { status: 401, message: 'Unauthorized' };
		}
	},
	{
		name: 'accounts',
		path: /\/account\/[\/\S]*/gi,
		guard: async (_, input) => {
			// allow /account/create
			// and allow /account/fpwd
			const reg = /\/account\/(create|fpwd|grant|grant\/not-verified)/gi;
			return reg.test(input.url.pathname) ? new GuardAlwaysPass() : false;
			// return input.url.pathname === '/account/create' ? new GuardAlwaysPass() : false;
		},
		extends: ['auth-guard']
	},
	{
		name: 'admin',
		path: /\/admin(?:\/)?[\/\S]*/gi,
		guard: async (_, input) => {
			if (input.locals.session?.storage?.permissions?.find((p) => p === '*') !== undefined) {
				return new GuardAlwaysPass();
			}

			return true;
		},
		extends: ['auth-guard']
	},
	{
		name: 'redirect-no-auth-authed',
		path: '/login',
		guard: async (_, input) => {
			return input.locals.session?.storage?.user !== undefined ? new RedirectGuard('/') : false;
		}
	}
];

export const onHandle: Hook = async ({ event, resolve }, next) => {
	if (building) {
		return resolve(event);
	}

	for (const guard of GUARDS) {
		const url = new URL(event.request.url);
		const matches =
			guard.path !== undefined
				? typeof guard.path === 'string'
					? url.pathname === guard.path
					: url.pathname.match(guard.path)
				: false;

		if (matches) {
			async function doGuard(g: Guard): Promise<Response | GuardAlwaysPass | false> {
				const result = await g.guard?.(url, event);
				const returnFn = (input: GuardResponse) => {
					if (g.api === true) {
						return json(input, { status: input.status });
					} else {
						// render the error page
						throw error(input.status, { status: input.status, message: input.message });
					}
				};
				if (result instanceof GuardAlwaysPass || result instanceof RedirectGuard) {
					return result;
				}
				if (typeof result === 'object') {
					return returnFn(result);
				} else if (result === true) {
					return returnFn({
						status: 403,
						message: 'Forbidden'
					});
				}

				return false;
			}

			// todo: this is a temporary solution
			// we really need to redirect to the error page if the guard is not fulfilled
			const response = await doGuard(guard);

			if (response) {
				if (response instanceof GuardAlwaysPass) {
					return next();
				}
				if (response instanceof RedirectGuard) {
					return redirect(302, response.url);
				}
				return response;
			}

			if (guard.extends) {
				for (const extend of guard.extends) {
					const parent = GUARDS.find((g) => g.name === extend);
					if (parent) {
						const response = await doGuard(parent);
						if (response) {
							if (response instanceof GuardAlwaysPass) {
								return next();
							}
							if (response instanceof RedirectGuard) {
								return redirect(302, response.url);
							}
							return response;
						}
					}
				}
			}
		}
	}

	return next();
};
