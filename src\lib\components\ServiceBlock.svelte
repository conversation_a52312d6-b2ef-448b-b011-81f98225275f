<script lang="ts">
	import type { HTMLAttributes } from 'svelte/elements';
	import type { WithElementRef } from 'bits-ui';

	interface Props {
		title: string;
		description: string;
		link: string;
		icon?: string;
	}

	let {
		ref = $bindable(null),
		title,
		description,
		link,
		icon,
		class: className,
		...restProps
	}: WithElementRef<HTMLAttributes<HTMLElement>> & Props = $props();

	let card: HTMLElement;

	function handleMouseMove(e: MouseEvent) {
		const rect = card.getBoundingClientRect();
		const x = e.clientX - rect.left;
		const y = e.clientY - rect.top;

		card.style.setProperty('--mouse-x', `${x}px`);
		card.style.setProperty('--mouse-y', `${y}px`);
	}
</script>

<a
	href={link}
	bind:this={card}
	onmousemove={handleMouseMove}
	class="group relative block overflow-hidden rounded-xl bg-[var(--card-bg)] transition-all duration-300 hover:bg-[var(--card-hover-bg)] {className}"
	{...restProps}
>
	<div
		class="absolute inset-0 rounded-xl opacity-0 transition-all duration-300 group-hover:opacity-100"
		style="background: radial-gradient(circle at var(--mouse-x, 50%) var(--mouse-y, 50%), rgba(59, 130, 246, 0.2), rgba(124, 58, 237, 0.2), transparent 70%)"
	></div>
	<div class="relative z-10 flex flex-col gap-4 p-6">
		<div class="flex items-center gap-3">
			{#if icon}
				<div class="h-8 w-8 text-[var(--btn-primary)]">
					<span class={icon}></span>
				</div>
			{/if}
			<h3 class="flex-1 text-xl font-bold text-[var(--card-text-primary)]">
				{title}
			</h3>
			<div
				class="flex items-center whitespace-nowrap text-sm text-[var(--text-secondary)] transition-colors group-hover:text-[var(--text-secondary-hover)]"
			>
				View
				<span
					class="icon-[mingcute--right-line] ml-1 h-4 w-4 align-middle transition-transform duration-300 group-hover:translate-x-1"
				></span>
			</div>
		</div>
		<p class="min-h-[4.5rem] text-base text-[var(--card-text-secondary)]">
			{description}
		</p>
	</div>
</a>
