import type { Handle, HandleServerError } from '@sveltejs/kit';
import { runCleanup, runHooks } from '../hooks';
import { building } from '$app/environment';

export const handle: Handle = async (ev) => {
	const { event, resolve } = ev;

	if (building) {
		return resolve(event);
	}

	const url = new URL(event.request.url);
	const response = await runHooks(ev);

	await runCleanup(ev);

	return response;
};

export const handleError: HandleServerError = async ({ error, event, status, message }) => {
	const errorId = crypto.randomUUID();

	console.error(error);
	return {
		message: getMessageForStatus(status),
		status,
		errorId
	};
};

function getMessageForStatus(status: number) {
	switch (status) {
		case 400:
			return "We don't know how to tell you, but something is wrong with your request.";
		case 404:
			return 'The page you are looking for does not exist.';
		case 403:
			return 'You are not allowed to access this page.';
		case 401:
			return 'You need to log in to access this page.';
		case 500:
			return 'Whoops! We broke something... 😅';
		default:
			return 'An error occurred.';
	}
}
