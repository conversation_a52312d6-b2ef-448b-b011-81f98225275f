import type { PageServerLoad, Actions } from './$types';
import { AuthService } from '@backend/auth';
import { redirect } from '@sveltejs/kit';
import { Session } from '$lib/server/sessions';
import { SessionStore } from '$lib/server/sessions/Store';

export const load: PageServerLoad = async ({ cookies }) => {
	const sessionId = cookies.get('sessionid');
	if (sessionId) {
		const user = await AuthService.getUserFromSession(sessionId);
		if (user) {
			throw redirect(303, '/account');
		}
	}
	return {};
};

export const actions = {
	login: async ({ cookies, request }) => {
		const data = await request.formData();
		const email = data.get('email');
		const password = data.get('password');

		// Create an errors object to track field-specific validation errors
		const errors: Record<string, string> = {};

		const usernameRegex = /^[a-zA-Z0-9_]+$/;
		const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

		// Validate email
		if (!email) {
			errors.email = 'Username or Email is required';
		} else if (!usernameRegex.test(email.toString()) && !emailRegex.test(email.toString())) {
			errors.email = 'Please enter a valid username or email address';
		}

		// Validate password
		if (!password) {
			errors.password = 'Password is required';
		}

		// Return any validation errors
		if (Object.keys(errors).length > 0) {
			// Return form data to preserve user input
			const formData = {
				email: email?.toString() || ''
			};

			return {
				success: false,
				errors,
				formData
			};
		}

		try {
			// Attempt to login
			if (!email || !password) {
				throw new Error('Email and password are required');
			}

			const result = await AuthService.login(email.toString(), password.toString());

			// Handle null result from database operation
			if (!result || !result.user || !result.token || !result.session) {
				return {
					success: false,
					errors: {
						form: 'Invalid email or password'
					},
					formData: {
						email: email?.toString() || ''
					}
				};
			}

			console.log('result', result);
			// Set session cookie
			cookies.set('session_id', result.session.id, {
				path: '/',
				httpOnly: true,
				sameSite: 'strict',
				secure: process.env.NODE_ENV === 'production',
				maxAge: 60 * 60 * 24 * 7 // 7 days
			});

			return {
				success: true,
				redirect: '/account/@me'
			};
		} catch (error) {
			console.error('Login error:', error);
			if (error instanceof Error && error.message === 'Invalid username or password') {
				return {
					success: false,
					errors: {
						form: 'Invalid username or password'
					},
					formData: {
						email: email?.toString() || ''
					}
				};
			}
			if (error instanceof Error && error.message === 'Account disabled') {
				return {
					success: false,
					errors: {
						form: 'Account disabled'
					},
					formData: {
						email: email?.toString() || ''
					}
				};
			}
			return {
				success: false,
				errors: {
					form: 'An error occurred during login. Please try again later.'
				},
				formData: {
					email: email?.toString() || ''
				}
			};
		}
	}
} satisfies Actions;
