<script lang="ts">
	import Button from './forms/Button.svelte';
	import type { HTMLAttributes } from 'svelte/elements';
	import type { WithElementRef } from 'bits-ui';
	import type { Snippet } from 'svelte';

	interface Props {
		title: string;
		description: string;
		link: string;
		image?: Snippet;
	}

	let {
		ref = $bindable(null),
		title,
		description,
		link,
		image,
		class: className,
		...restProps
	}: WithElementRef<HTMLAttributes<HTMLElement>> & Props = $props();
</script>

<div
	bind:this={ref}
	class="group relative flex flex-col overflow-hidden rounded-xl bg-[var(--card-bg)] transition-all duration-300 hover:shadow-xl {className}"
	{...restProps}
>
	<div class="relative h-48 overflow-hidden">
		{#if image}
			<div class="absolute inset-0 flex items-center justify-center">
				{@render image()}
			</div>
		{:else}
			<div class="absolute inset-0 bg-gradient-to-br from-[var(--btn-primary)] to-blue-900"></div>
		{/if}
	</div>
	<div class="flex flex-1 flex-col gap-4 p-6">
		<h3 class="text-xl font-bold text-[var(--card-text-primary)]">
			{title}
		</h3>
		<p class="text-base text-[var(--card-text-secondary)]">
			{description}
		</p>
		<Button href={link} class="mt-auto">
			{#snippet text()}
				Learn More
				<span class="icon-[mingcute--right-line] ml-2 h-4 w-4"></span>
			{/snippet}
		</Button>
	</div>
</div>
