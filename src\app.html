<!doctype html>
<html lang="en">
	<head>
		<meta charset="utf-8" />
		<link rel="icon" href="%sveltekit.assets%/favicon.png" />
		<meta name="viewport" content="width=device-width, initial-scale=1" />
		<link
			rel="stylesheet"
			href="https://fonts.googleapis.com/css2?family=Material+Symbols+Outlined:opsz,wght,FILL,GRAD@20..48,100..700,0..1,-50..200"
		/>
		<meta
			name="viewport"
			content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0"
		/>
		<!-- <link
			rel="stylesheet"
			href="./app.css"
		/> -->
		%sveltekit.head%
	</head>

	<noscript>
		<style>
			[data-sveltekit-preload] {
				display: none;
			}
		</style>
	</noscript>

	<body data-sveltekit-preload-data="hover" class="dark">
		<div style="display: contents">%sveltekit.body%</div>
	</body>

	<script defer>
		// This is a hack around the fact global selectors are SHIT
		var _strdTheme = localStorage.getItem('theme');
		console.debug('Theme is: ' + _strdTheme);
		if (!_strdTheme) {
			localStorage.setItem('theme', 'dark');
			_strdTheme = 'dark';
		} else {
			theme(_strdTheme);
		}

		function theme(type = 'dark') {
			let valid = [
				'dark',
				'light',
				'cb-grayscale-dark',
				'cb-grayscale-light',
				'cb-red-dark',
				'cb-red-light',
				'cb-green-dark',
				'cb-green-light',
				'cb-blue-dark',
				'cb-blue-light'
			];

			if (!valid.includes(type)) {
				console.debug('Invalid color');
				return;
			} else {
				valid.filter((v) => v !== type).forEach((e) => document.body.classList.remove(e));
				document.body.classList.add(type);
				window.localStorage.setItem('theme', _strdTheme);

				setTimeout(() => {
					_strdTheme = localStorage.getItem('theme');
					console.log('Swapped to: ' + _strdTheme);
				}, 2000);
			}
		}
	</script>
</html>
