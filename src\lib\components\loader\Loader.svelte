<script lang="ts">
	import { onMount } from 'svelte';
	import { fade, fly, crossfade } from 'svelte/transition';
	import Logo from '$lib/components/icon/Logo.svelte';
	import { loadingProgress, isLoading, waitForResources } from './api';

	let { transparent = false } = $props();
	let progress = $derived($loadingProgress);

	let particles = $state<{ x: number; y: number; size: number; speed: number; opacity: number }[]>(
		[]
	);

	// Create crossfade transition
	const [send, receive] = crossfade({
		duration: 400,
		fallback(node, params) {
			const style = getComputedStyle(node);
			const transform = style.transform === 'none' ? '' : style.transform;

			return {
				duration: 400,
				easing: (t) => t * (2 - t),
				css: (t) => `transform: ${transform} scale(${t}); opacity: ${t}`
			};
		}
	});

	function createParticles() {
		particles = Array.from({ length: 20 }, () => ({
			x: Math.random() * 100,
			y: Math.random() * 100,
			size: Math.random() * 4 + 2,
			speed: Math.random() * 0.5 + 0.2,
			opacity: Math.random() * 0.5 + 0.1
		}));
	}

	function updateParticles() {
		particles = particles.map((p) => ({
			...p,
			y: (p.y + p.speed) % 100,
			opacity: Math.sin(Date.now() * 0.001 + p.x) * 0.3 + 0.4
		}));
	}

	onMount(() => {
		createParticles();
		// Start resource loading
		waitForResources();

		// Continuous loading animation
		const interval = setInterval(() => {
			updateParticles();
		}, 50);

		return () => clearInterval(interval);
	});
</script>

{#if $isLoading}
	<div
		class="fixed inset-0 z-[9999] flex min-h-screen items-center justify-center overflow-hidden {transparent
			? 'bg-transparent'
			: 'bg-[var(--bg)] backdrop-blur-md'}"
		transition:fade={{ duration: 300 }}
	>
		<!-- Full screen overlay to block content -->
		{#if !transparent}
			<div class="fixed inset-0 z-[-1] bg-[var(--bg)]"></div>
		{/if}

		<!-- Animated Background -->
		<div class="absolute inset-0 -z-10 {!transparent ? 'bg-[var(--bg)]' : ''}">
			<div
				class="animate-pulse-slow absolute inset-0 bg-[radial-gradient(circle_at_center,_var(--btn-primary)_0%,_transparent_70%)] opacity-10"
			></div>
			<div
				class="absolute inset-0 bg-[url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjAiIGhlaWdodD0iNjAiIHZpZXdCb3g9IjAgMCA2MCA2MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48ZyBmaWxsPSJub25lIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPjxwYXRoIGQ9Ik0zNiAzNGMwIDIuMjA5LTEuNzkxIDQtNCA0cy00LTEuNzkxLTQtNCAxLjc5MS00IDQtNCA0IDEuNzkxIDQgNHoiIGZpbGw9IiNmZmYiIGZpbGwtb3BhY2l0eT0iLjEiLz48L2c+PC9zdmc+')] opacity-5"
			></div>

			<!-- Floating Particles -->
			{#each particles as particle}
				<div
					class="absolute rounded-full bg-[var(--btn-primary)]"
					style="
          left: {particle.x}%;
          top: {particle.y}%;
          width: {particle.size}px;
          height: {particle.size}px;
          opacity: {particle.opacity};
          filter: blur(1px);
        "
				></div>
			{/each}
		</div>

		<div class="relative w-full max-w-2xl px-4">
			<!-- Logo with Pulse Effect -->
			<div class="mb-8 flex justify-center" in:fade={{ duration: 500 }}>
				<div class="relative">
					<div
						class="animate-ping-slow absolute inset-0 rounded-full bg-[var(--btn-primary)] opacity-20"
					></div>
					<Logo class="animate-float relative h-16 w-16" fill="#ffffff" />
				</div>
			</div>

			<!-- Loading Content -->
			<div class="space-y-8" in:fade={{ duration: 500, delay: 200 }}>
				<!-- Enhanced Progress Bar -->
				<div
					class="relative h-3 w-full overflow-hidden rounded-full bg-gray-800/50 backdrop-blur-sm"
				>
					<div
						class="absolute left-0 top-0 h-full bg-gradient-to-r from-[var(--btn-primary)] to-blue-500 transition-all duration-300"
						style="width: {progress}%"
					></div>
					<div
						class="animate-shimmer absolute left-0 top-0 h-full w-full bg-gradient-to-r from-transparent via-white/30 to-transparent"
						style="transform: translateX({progress - 100}%)"
					></div>
					<div
						class="animate-pulse-slow absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent"
					></div>
				</div>

				<!-- Loading Text with Glitch Effect -->
				<div class="text-center">
					<p class="text-sm text-gray-400">
						Loading your experience<span class="animate-blink inline-block">.</span><span
							class="animate-blink inline-block"
							style="animation-delay: 200ms">.</span
						><span class="animate-blink inline-block" style="animation-delay: 400ms">.</span>
					</p>
				</div>
			</div>
		</div>
	</div>
{/if}

<style>
	@keyframes blink {
		0%,
		100% {
			opacity: 1;
		}
		50% {
			opacity: 0;
		}
	}

	@keyframes float {
		0%,
		100% {
			transform: translateY(0);
		}
		50% {
			transform: translateY(-10px);
		}
	}

	@keyframes shimmer {
		0% {
			transform: translateX(-100%);
		}
		100% {
			transform: translateX(100%);
		}
	}

	@keyframes glitch {
		0%,
		100% {
			transform: translate(0);
		}
		20% {
			transform: translate(-2px, 2px);
		}
		40% {
			transform: translate(-2px, -2px);
		}
		60% {
			transform: translate(2px, 2px);
		}
		80% {
			transform: translate(2px, -2px);
		}
	}

	.animate-blink {
		animation: blink 1s infinite;
	}

	.animate-float {
		animation: float 3s ease-in-out infinite;
	}

	.animate-shimmer {
		animation: shimmer 2s infinite;
	}

	.animate-glitch {
		animation: glitch 3s infinite;
	}

	.animate-pulse-slow {
		animation: pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite;
	}

	.animate-pulse-subtle {
		animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
	}

	.animate-ping-slow {
		animation: ping 3s cubic-bezier(0, 0, 0.2, 1) infinite;
	}

	@keyframes pulse {
		0%,
		100% {
			opacity: 1;
		}
		50% {
			opacity: 0.5;
		}
	}

	@keyframes ping {
		75%,
		100% {
			transform: scale(1.5);
			opacity: 0;
		}
	}

	/* Smooth text transitions */
	h2,
	p {
		transition:
			opacity 0.3s ease-in-out,
			transform 0.3s ease-in-out;
	}

	/* Ensure smooth icon transitions */
	[class^='icon-'] {
		transition: all 0.3s ease-in-out;
	}

	/* Add smooth transition for loader */
	:global(.loader-exit) {
		animation: fadeOut 0.3s ease-out forwards;
	}

	@keyframes fadeOut {
		from {
			opacity: 1;
		}
		to {
			opacity: 0;
		}
	}
</style>
