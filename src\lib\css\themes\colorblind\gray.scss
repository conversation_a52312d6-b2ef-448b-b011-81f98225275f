.cb-grayscale-dark {
	--bg: #212121;
	--bg-secondary: #232323;
	--nav-bg: #262626;
	--text-primary: #808080;
	--text-secondary: #a3a3a3;
	--accent: #4d4d4d;

	/* Debug */
	--bg-debug-header: #6e6e6e;
	--bg-debug: #0f1417;
	--text-debug-value: #39a9ff;
	--text-debug-key: #ffffff;
	--text-debug-primary: #ffffff;
	--text-debug-secondary: #a3a3a3;
	--bg-debug-code: rgba(0, 0, 0, 0.568);

	/* Button bg */
	--btn-primary: #333333;
	--btn-hover-primary: #4d4d4d;
	--btn-secondary: #666666;
	--btn-hover-secondary: #7f7f7f;
	--btn-tertiary: #808080;
	--btn-hover-tertiary: #999999;
	--btn-error: #8c8c8c;
	--btn-hover-error: #a3a3a3;
	--btn-success: #5e5e5e;
	--btn-hover-success: #7d7d7d;
	--btn-warning: #b3b3b3;
	--btn-hover-warning: #cccccc;
	--btn-info: #999999;
	--btn-hover-info: #b2b2b2;

	/* Cards */
	--card-container-bg: #232323;
	--card-bg: #262626;
	--card-button-bg: #333333;
	--card-text-primary: #f0f0f0;
	--card-text-secondary: #d1d1d1;

	/* Miscelaneous */
	--tr-blue: #b0b0b0;
	--tr-blue-alt: #8f8f8f;

	/* Footer */
	--footer-bg: #212121;

	/* Hero */
	--hero-bg: #1e1e1e;
	--hero-text-color: #ffffff;

	--hero-title-color: #8d8d8d;

	--hero-button-text: #ffffff;
	--hero-button-gradient-from: #7a7a7a;
	--hero-button-gradient-to: #8d8d8d;
	--hero-button-gradient-hover-from: #6a6a6a;
	--hero-button-gradient-hover-to: #7a7a7a;

	--hero-spotlight-bg: #303030;

	/** Other generic colors */
	--teal-byte: #333333;
	--text-primary: #808080;
	--text-secondary: #b5b5b5;

	/** Gradient */
	--gradient-start: #d1d1d1;
	--gradient-end: #9c9c9c;

	/* Error Page */
	--error-bg-gradient-from: #1e1e1e;
	--error-bg-gradient-to: #212121;
	--error-text-primary: #808080;
	--error-text-secondary: #8d8d8d;
	--error-accent: #8f8f8f;
	--error-glow: #7a7a7a;
	--error-particle: #9c9c9c;
	--error-button-bg: rgba(143, 143, 143, 0.2);
	--error-button-hover: rgba(143, 143, 143, 0.3);
	--error-button-text: #808080;
	--error-code-color: #b0b0b0;
}

.cb-grayscale-light {
	--bg: #b0b0b0;
	--bg-secondary: #a3a3a3;
	--nav-bg: #999999;
	--text-primary: #595959;
	--text-secondary: #777777;
	--accent: #cccccc;

	/* Debug */
	--bg-debug: #d1d1d1;
	--text-debug: #b5b5b5;

	/* Button bg */
	--btn-primary: #555555;
	--btn-hover-primary: #666666;
	--btn-secondary: #808080;
	--btn-hover-secondary: #999999;
	--btn-tertiary: #7f7f7f;
	--btn-hover-tertiary: #999999;
	--btn-error: #9c9c9c;
	--btn-hover-error: #b5b5b5;
	--btn-success: #8c8c8c;
	--btn-hover-success: #a3a3a3;
	--btn-warning: #a5a4a4;
	--btn-hover-warning: #b9b9b9;
	--btn-info: #a3a3a3;
	--btn-hover-info: #b5b5b5;

	/* Cards */
	--card-container-bg: #a3a3a3;
	--card-bg: #999999;
	--card-button-bg: #6e6e6e;
	--card-text-primary: #f0f0f0;
	--card-text-secondary: #e0e0e0;

	/* Miscelaneous */
	--tr-blue: #d9d9d9;
	--tr-blue-alt: #c8c8c8;

	/* Footer */
	--footer-bg: #b0b0b0;

	/* Hero */
	--hero-bg: #999999;
	--hero-text-color: #404040;

	--hero-title-color: #b0b0b0;

	--hero-button-text: #404040;
	--hero-button-gradient-from: #7a7a7a;
	--hero-button-gradient-to: #8d8d8d;
	--hero-button-gradient-hover-from: #6a6a6a;
	--hero-button-gradient-hover-to: #7a7a7a;

	--hero-spotlight-bg: #d1d1d1;

	/** Other generic colors */
	--teal-byte: #999999;
	--text-primary: #595959;
	--text-secondary: #595858;

	/** Gradient */
	--gradient-start: #e0e0e0;
	--gradient-end: #d1d1d1;

	/* Error Page */
	--error-bg-gradient-from: #999999;
	--error-bg-gradient-to: #b0b0b0;
	--error-text-primary: #595959;
	--error-text-secondary: #b0b0b0;
	--error-accent: #c8c8c8;
	--error-glow: #6a6a6a;
	--error-particle: #d1d1d1;
	--error-button-bg: rgba(200, 200, 200, 0.2);
	--error-button-hover: rgba(200, 200, 200, 0.3);
	--error-button-text: #595959;
	--error-code-color: #b5b5b5;
}
