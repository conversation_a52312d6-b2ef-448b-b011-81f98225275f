<script lang="ts">
	import type { HTMLAttributes } from 'svelte/elements';
	import type { WithElementRef } from './index';

	let {
		ref = $bindable(null),
		gap = '',
		class: className = '',
		children,
		...restProps
	}: WithElementRef<HTMLAttributes<HTMLDivElement>> & { gap?: string } = $props();
</script>

<div bind:this={ref} class="field-row {className}" {...restProps}>
	{@render children?.()}
</div>

<style lang="scss">
	.field-row {
		display: flex;
		justify-content: center;
		align-items: center;
		gap: 20px;
		margin-bottom: 10px;

		:global(.field) {
			width: 48%;
		}

		:global(a) {
			display: block;
		}
	}
</style>
