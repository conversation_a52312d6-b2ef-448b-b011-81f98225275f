<script lang="ts">
	import * as Form from '$lib/components/forms';
	import Button from '$lib/components/forms/Button.svelte';
	import type { Snippet } from 'svelte';

	export let formData: {
		notifications: {
			email: boolean;
			sms: boolean;
			marketing: boolean;
		};
	};

	export let handleSubmit: (event: Event) => void;
</script>

<div class="space-y-8">
	<div>
		<h2 class="mb-1 text-xl font-semibold text-[var(--text-primary)]">Notification Preferences</h2>
		<p class="text-sm text-[var(--text-secondary)]">Choose how you want to receive updates.</p>
	</div>
	<form onsubmit={handleSubmit} class="space-y-6">
		<div class="space-y-4">
			<label
				class="flex cursor-pointer items-center justify-between rounded-xl bg-[var(--card-container-bg)] p-4"
			>
				<div class="w-[400px] space-y-1">
					<p class="text-sm font-medium text-[var(--text-primary)]">Email Notifications</p>
					<p class="text-xs text-[var(--text-secondary)]">
						Receive updates about your services via email
					</p>
				</div>
				<Form.Checkbox
					name="emailNotifications"
					bind:checked={formData.notifications.email}
					width="auto"
				/>
			</label>
			<label
				class="flex cursor-pointer items-center justify-between rounded-xl bg-[var(--card-container-bg)] p-4"
			>
				<div class="w-[400px] space-y-1">
					<p class="text-sm font-medium text-[var(--text-primary)]">SMS Notifications</p>
					<p class="text-xs text-[var(--text-secondary)]">
						Receive updates about your services via SMS
					</p>
				</div>
				<Form.Checkbox
					name="smsNotifications"
					bind:checked={formData.notifications.sms}
					width="auto"
				/>
			</label>
			<label
				class="flex cursor-pointer items-center justify-between rounded-xl bg-[var(--card-container-bg)] p-4"
			>
				<div class="w-[400px] space-y-1">
					<p class="text-sm font-medium text-[var(--text-primary)]">Marketing Emails</p>
					<p class="text-xs text-[var(--text-secondary)]">
						Receive marketing emails about your services
					</p>
				</div>
				<Form.Checkbox
					name="marketingEmails"
					bind:checked={formData.notifications.marketing}
					width="auto"
				/>
			</label>
		</div>
		<div class="flex justify-end">
			<Button type="primary">
				{#snippet text()}
					Save Preferences
				{/snippet}
			</Button>
		</div>
	</form>
</div>
