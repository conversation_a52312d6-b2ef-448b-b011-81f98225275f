<script lang="ts">
	import Dropdown from './Dropdown.svelte';

	interface FaqItem {
		question: string;
		answer: string;
	}

	let faqs = $state<FaqItem[]>([
		{
			question: 'How are repairs currently serviced?',
			answer: `We currently only operate with remote device repairs. You can initiate the repair
process on your account page after creating an account, where you will receive
the time and cost of the repair as well as details of when we will arrive to
diagnose it with an innovative repair tracker.`
		},
		{
			question: 'What kind of devices do you repair?',
			answer: `We currently repair a variety of devices ranging from Televisions, Laptops,
Computers, Smart-Phones, and Home Devices. You can view a list of repairable
items by creating a repair ticket. If your device is not on this list, you can
reach out to our support team for details and information.`
		},
		{
			question: 'How does the Trade-In Program work?',
			answer: `To trade in an old device, go to your account page and click "initiate a trade-in."
You can then select the device you are attempting to trade in and the device you're
trying to receive with the trade-in. Please note that some devices are not eligible
for trade-in. If you're looking to trade in a device and it's not on the list, feel
free to create a support ticket for further help.`
		},
		{
			question: 'How do I commission TexRepairs to build a website or piece of software?',
			answer: `All business-related inquiries can be resolved through our ticket system or by
contacting <NAME_EMAIL>.`
		},
		{
			question: "I don't see my question, where do I get an answer?",
			answer: `The best place to get help is by creating a ticket through our ticket system. You
will generally receive a response within 2 hours (during business hours). If you're
having issues with that, you <NAME_EMAIL> — please note it may
take a few days for us to process emails.`
		}
	]);

	let openIndex = $state<number | null>(null);

	function toggleFaq(index: number) {
		openIndex = openIndex === index ? null : index;
	}
</script>

<div class="relative overflow-hidden">
	<div class="relative mx-auto max-w-4xl px-4 py-16">
		<div class="mb-12 text-center">
			<h2 class="mb-4 text-3xl font-bold text-[var(--text-primary)] md:text-4xl">
				Frequently Asked Questions
			</h2>
			<p class="text-lg text-[var(--text-secondary)]">
				Find answers to common questions about our services and support.
			</p>
		</div>

		<div class="space-y-4">
			{#each faqs as faq, index}
				<Dropdown
					title={faq.question}
					isOpen={openIndex === index}
					onToggle={() => toggleFaq(index)}
				>
					{faq.answer}
				</Dropdown>
			{/each}
		</div>

		<div class="mt-12 text-center">
			<p class="text-[var(--text-secondary)]">
				Still have questions?
				<a
					href="#contact"
					class="font-medium text-[var(--btn-primary)] hover:text-[var(--btn-hover-primary)]"
				>
					Contact our support team
				</a>
			</p>
		</div>
	</div>
</div>
