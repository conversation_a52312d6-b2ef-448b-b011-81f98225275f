import { writable, type Writable } from 'svelte/store';

export const loadingProgress = writable(0);
export const isLoading = writable(true);

interface ResourceElement extends HTMLElement {
	src?: string;
	dataset: {
		src?: string;
		client?: string;
		render?: string;
	};
}

/**
 * Handles loading of various resource types (images, scripts, iframes)
 * with proper error handling and timeout protection
 */
async function loadResource(element: ResourceElement): Promise<void> {
	const tagName = element.tagName.toLowerCase();
	const src = element.dataset.src || element.src;

	if (!src) return;

	try {
		switch (tagName) {
			case 'img': {
				await new Promise<void>((resolve) => {
					const img = element as HTMLImageElement;
					const timeout = setTimeout(() => {
						console.warn(`Image load timeout: ${src}`);
						resolve();
					}, 10000);

					img.onload = () => {
						clearTimeout(timeout);
						resolve();
					};
					img.onerror = () => {
						clearTimeout(timeout);
						console.warn(`Failed to load image: ${src}`);
						resolve();
					};
					img.src = src;
				});
				break;
			}
			case 'script': {
				await new Promise<void>((resolve) => {
					const script = element as HTMLScriptElement;
					fetch(src)
						.then(async (response) => {
							if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
							const text = await response.text();
							script.textContent = text;
							resolve();
						})
						.catch((error) => {
							console.warn(`Failed to load script: ${src}`, error);
							resolve();
						});
				});
				break;
			}
			case 'iframe': {
				await new Promise<void>((resolve) => {
					const iframe = element as HTMLIFrameElement;
					const timeout = setTimeout(() => {
						console.warn(`Iframe load timeout: ${src}`);
						resolve();
					}, 10000);

					iframe.onload = () => {
						clearTimeout(timeout);
						resolve();
					};
					iframe.onerror = () => {
						clearTimeout(timeout);
						console.warn(`Failed to load iframe: ${src}`);
						iframe.innerHTML = `<p>Failed to load iframe: ${src}</p>`;
						resolve();
					};
					iframe.src = src;
				});
				break;
			}
			case 'video':
			case 'audio': {
				await new Promise<void>((resolve) => {
					const media = element as HTMLMediaElement;
					const timeout = setTimeout(() => {
						console.warn(`${tagName} load timeout: ${src}`);
						resolve();
					}, 10000);

					media.oncanplaythrough = () => {
						clearTimeout(timeout);
						resolve();
					};
					media.onerror = () => {
						clearTimeout(timeout);
						console.warn(`Failed to load ${tagName}: ${src}`);
						resolve();
					};
					media.src = src;
				});
				break;
			}
		}
	} catch (error) {
		console.warn(`Error loading ${tagName}: ${src}`, error);
	}
}

/**
 * Monitors and loads all resources marked with data-client attribute
 * Updates loading progress as resources complete
 */
export async function waitForResources(): Promise<void> {
	if (typeof window === 'undefined') {
		throw new Error('waitForResources() is only available in the browser');
	}

	const elements = Array.from(document.querySelectorAll<ResourceElement>('[data-client]'));
	const totalResources = elements.length;
	let loadedResources = 0;

	if (totalResources === 0) {
		loadingProgress.set(100);
		isLoading.set(false);
		return;
	}

	const updateProgress = () => {
		loadedResources++;
		const progress = Math.round((loadedResources / totalResources) * 100);
		loadingProgress.set(progress);

		if (loadedResources === totalResources) {
			isLoading.set(false);
		}
	};

	// Handle elements marked for rendering
	document.querySelectorAll('[data-render]').forEach((element) => {
		console.log(
			`Render requested for: ${element.tagName}${element.id ? `#${element.id}` : ''}${
				element.className ? `.${element.className}` : ''
			}`
		);
	});

	// Load all resources in parallel
	await Promise.all(
		elements.map(async (element) => {
			await loadResource(element);
			updateProgress();
		})
	);
}

// Helper function to check if all resources are loaded
export function checkResourcesLoaded(): boolean {
	const elements = document.querySelectorAll<ResourceElement>('[data-client]');
	return Array.from(elements).every((element) => {
		const tagName = element.tagName.toLowerCase();
		const src = element.dataset.src || element.src;

		if (!src) return true;

		switch (tagName) {
			case 'img':
				return (element as HTMLImageElement).complete;
			case 'script':
				return element.hasAttribute('data-loaded');
			case 'iframe':
				return (element as HTMLIFrameElement).contentWindow?.document.readyState === 'complete';
			case 'video':
			case 'audio':
				return (element as HTMLMediaElement).readyState >= 3;
			default:
				return true;
		}
	});
}
