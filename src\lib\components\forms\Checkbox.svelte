<script lang="ts">
	import type { HTMLAttributes } from 'svelte/elements';
	import type { WithElementRef } from './index';
	import type { Snippet } from 'svelte';

	let {
		ref = $bindable(null),
		class: className = '',
		children,
		id = crypto.randomUUID(),
		name = '',
		label,
		checked = $bindable(false),
		error,
		width = 'auto',
		...restProps
	}: WithElementRef<HTMLAttributes<HTMLElement>> & {
		name: string;
		label?: Snippet;
		checked?: boolean;
		error?: string;
		width?: string;
	} = $props();
</script>

<div class="checkbox-wrapper" style="width: {width}">
	<div class="checkbox-container">
		<input
			{id}
			type="checkbox"
			{name}
			bind:checked
			class="checkbox-input {className}"
			{...restProps}
		/>
		<span class="checkbox-custom"></span>
	</div>

	<div class="checkbox-content">
		{#if label}
			<label for={id} class="checkbox-label">
				{@render label()}
			</label>
		{/if}
		{#if error}
			<p class="checkbox-error">{error}</p>
		{/if}
	</div>
</div>

<style lang="scss">
	.checkbox-wrapper {
		display: flex;
		align-items: flex-start;
		gap: 0.75rem;
	}

	.checkbox-container {
		position: relative;
		display: inline-block;
		width: 1.25rem;
		height: 1.25rem;
		margin-top: 0.125rem;
	}

	.checkbox-input {
		position: absolute;
		top: 0;
		left: 0;
		width: 1.25rem;
		height: 1.25rem;
		opacity: 0;
		cursor: pointer;
		z-index: 2;

		&:checked + .checkbox-custom {
			background-color: var(--btn-primary);
			border-color: var(--btn-primary);

			&:after {
				display: block;
			}
		}

		&:focus + .checkbox-custom {
			box-shadow: 0 0 0 2px rgba(var(--btn-primary-rgb), 0.2);
		}
	}

	.checkbox-custom {
		position: absolute;
		top: 0;
		left: 0;
		height: 1.25rem;
		width: 1.25rem;
		background-color: rgba(255, 255, 255, 0.05);
		border: 1px solid rgba(255, 255, 255, 0.1);
		border-radius: 0.25rem;
		transition: all 0.2s ease;
		pointer-events: none;

		&:after {
			content: '';
			position: absolute;
			display: none;
			left: 0.4rem;
			top: 0.2rem;
			width: 0.4rem;
			height: 0.7rem;
			border: solid white;
			border-width: 0 2px 2px 0;
			transform: rotate(45deg);
		}

		&:hover {
			border-color: var(--btn-primary);
		}
	}

	.checkbox-content {
		display: flex;
		flex-direction: column;
		flex: 1;
	}

	.checkbox-label {
		font-size: 0.875rem;
		font-weight: 500;
		color: #d1d5db;
		cursor: pointer;
		user-select: none;
		transition: color 0.2s ease;
		line-height: 1.5;

		&:hover {
			color: #e5e7eb;
		}
	}

	.checkbox-error {
		color: #ef4444;
		font-size: 0.75rem;
		margin-top: 0.25rem;
	}
</style>
