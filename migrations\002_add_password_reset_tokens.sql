-- Create PasswordResetTokens table
CREATE TABLE IF NOT EXISTS PasswordResetTokens (
    user_id TEXT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    token TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    PRIMARY KEY (user_id, token)
);

-- Add index for faster token lookups
CREATE INDEX IF NOT EXISTS idx_password_reset_tokens_token ON PasswordResetTokens(token);

-- Add index for faster user lookups
CREATE INDEX IF NOT EXISTS idx_password_reset_tokens_user_id ON PasswordResetTokens(user_id);

-- Add index for faster expiration checks
CREATE INDEX IF NOT EXISTS idx_password_reset_tokens_expires_at ON PasswordResetTokens(expires_at); 