import { execSync } from 'child_process';
import type { DebugItem } from '$lib/shared/Debug';
import type { ServerLoadEvent } from '@sveltejs/kit';
import { env } from '$env/dynamic/private';
import { Time } from '$lib/texrepairs/utils/index.js';

interface DebugCacheItem {
	v: string;
	t: number;
	ttl: number;
}
let gitCache: Map<string, DebugCacheItem> = new Map();

export async function getDebugItems(ev: ServerLoadEvent): Promise<{
	production: boolean;
	debugInfo?: DebugItem[];
	newerCommits?: boolean;
	commit?: string;
}> {
	if (env.NODE_ENV !== 'production' && env.ENABLE_DEBUGGER === 'true') {
		try {
			const branch = getOrSet('git.branch', () =>
				execSync('git rev-parse --abbrev-ref HEAD').toString().trim()
			);
			const commit = getOrSet('git.lastCommit', () =>
				execSync('git rev-parse HEAD').toString().trim()
			);
			const originCommitTime = getOrSet(
				'git.lastCommit.time',
				execSync('git show -s --format=%ct origin/' + branch)
					.toString()
					.trim()
			);
			const lastCommitOrigin = getOrSet(
				'git.lastCommit.commit',
				execSync('git rev-parse origin/' + branch)
					.toString()
					.trim()
			);

			// use cache, but update every minute
			getOrSet(
				'git.lastFetch',
				() => execSync('git fetch').toString(),
				new Time().seconds(60).asMillis()
			);

			let newerCommits = false;

			// check if origin is more recent than local
			if (
				parseInt(originCommitTime) >
				parseInt(execSync('git show -s --format=%ct').toString().trim())
			) {
				newerCommits = true;
			}

			const debugInfo: DebugItem[] = [
				{
					title: 'Branch',
					type: 'html',
					value: { data: branch },
					code: true
				},
				{
					title: 'Last Local Commit',
					type: 'html',
					value: {
						data: `<a href="https://github.com/Texrepairs/Website/commit/${commit}">${commit.split('').slice(0, 8).join('')}</a>`
					},
					code: true,
					classes: 'col-span-2'
				},
				{
					title: 'Last Local Author',
					type: 'html',
					value: { data: execSync("git show -s --format='%an'").toString().trim() },
					code: true
				},
				{
					title: 'Should Pull?',
					type: 'html',
					value: {
						data: newerCommits
							? `<span class="text-red-700 font-bold">YES</span>`
							: `<span class="text-green-500 font-bold">Nope 😊</span>`
					}
				}
			];

			return { production: false, debugInfo, newerCommits, commit: lastCommitOrigin };
		} catch (e) {
			return {
				production: false,
				debugInfo: [
					{
						title: 'Error',
						type: 'html',
						value: { data: 'You are probably not running linux or something.' },
						code: true
					}
				]
			};
		}
	} else {
		return { production: env.NODE_ENV === 'production' };
	}
}

function getOrSet(
	key: string,
	value: string | CallableFunction,
	ttl: number = new Time().seconds(1).asMillis()
): string {
	const cache = gitCache.get(key);

	function setKey() {
		if (typeof value === 'function') {
			try {
				value = value();
			} catch (e) {
				value = 'Error';
			}
		}

		// for ts
		let ret = value as string;
		gitCache.set(key, { v: ret, t: Time.now(), ttl });
		return ret;
	}

	if (cache) {
		if (Time.elaspsed(cache.t) >= ttl) {
			gitCache.delete(key);
			return setKey();
		} else {
			return cache.v;
		}
	} else {
		return setKey();
	}
}
