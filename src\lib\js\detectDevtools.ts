const WARNINGS = [
	{
		text: 'Wait!',
		styles: ['font-size: 70px; color: #3889b8; -webkit-text-stroke: 1px black; font-weight: bold;']
	},
	{
		text: 'If you know what you are doing, please view our terms of service and privacy policy.\n\n 👉 https://texrepairs.com/legal/terms\n 👉 https://texrepairs.com/legal/privacy\n\n',
		styles: ['font-size: 16px; color: white;']
	},
	// warning about reverse engineering and security research
	{
		text:
			'%cWARNING: %cATTEMPTING TO: REVERSE ENGINEER, ALTER, EXPLOIT, or ABUSE OUR SERVICES IS AGAINST OUR TERMS AND MAY RESULT IN LEGAL ACTION' +
			'\n\n%cIf you are a security researcher, please responsibly disclose any vulnerabilities to <NAME_EMAIL>',
		styles: [
			'font-size: 12px; font-weight: bold; color: #f5a623;',
			'font-size: 12px; color: #f5a623;',
			'font-size: 12px; color: #f5a623;',
			'font-size: 12px; color: #4d78f8;'
		]
	},
	{
		text: 'If you\'ve read all the above, and still want to proceed type "%cacceptTerms()%c" in console also, consider working for us at https://texrepairs.com/careers',
		styles: [
			'font-size: 12px; color: white;',
			'font-size: 12px; color: #a57de8; font-weight: bold;',
			'font-size: 12px; color: white;'
		]
	},
	{
		text: '✋ STOP!',
		styles: ['font-size: 70px; color: #f83838; -webkit-text-stroke: 1px black; font-weight: bold;']
	},
	{
		text: 'This is a browser feature intended for developers. If someone told you to copy-paste something here it more than likely is a scam and will give them access to your account.',
		styles: ['font-size: 16px; color: white;']
	},
	{
		text: "If you don't know what you are doing, close this window and stay safe.",
		styles: ['font-size: 16px; color: #fe6d6d;']
	}
];

let understand = false;
let interval: any = null;
let isOpen = false;

function printWarnings() {
	console.clear();
	WARNINGS.forEach((warning) => {
		console.log(`%c${warning.text}`, ...warning.styles);
	});
}

function printWarning(id: number) {
	if (!WARNINGS[id]) return;
	console.log(`%c${WARNINGS[id].text}`, ...WARNINGS[id].styles);
}

function acceptTerms() {
	understand = true;
	if (!localStorage.getItem('devtools.terms.accepted')) {
		localStorage.setItem('devtools.terms.accepted', 'yes');
		localStorage.setItem('devtools.terms.accepted.time', Date.now().toString());
		window.location.reload();
	}
	console.clear();
	console.log(
		'%cTexRepairs LLC',
		'font-size: 50px; color: #3889b8; -webkit-text-stroke: 1px black; font-weight: bold;'
	);
	printWarning(2);
	console.log(
		"%cYou have acknowledged our terms and privacy policy; so we've disabled our anti-devtools.",
		'font-size: 12px; color: #7eee82;'
	);
	stopChecking();
	// @ts-ignore
	delete window['acceptTerms'];
}

export function detectDevtoolsOld() {
	const devtools = /./;
	// @ts-ignore
	devtools.toString = () => {
		// @ts-ignore
		this.isOpen = true;
	};
	console.log('%c', devtools);
	// @ts-ignore
	return this.isOpen;
}

export function detectDevtools() {
	// if this is an ssr component, ignore
	// if (typeof window === 'undefined') {
	//     console.log('window is undefined')
	//     return;
	// }

	var threshold = 160; // Threshold for height/width difference to consider DevTools open

	if (localStorage.getItem('devtools.terms.accepted') === 'yes') {
		if (localStorage.getItem('devtools.terms.accepted.time')) {
			// @ts-ignore
			let time = parseInt(localStorage.getItem('devtools.terms.accepted.time'));
			if (Number.isNaN(time)) {
				localStorage.removeItem('devtools.terms.accepted');
				localStorage.removeItem('devtools.terms.accepted.time');
			} else if (Date.now() - time >= 1000 * 60 * 30) {
				localStorage.removeItem('devtools.terms.accepted');
				localStorage.removeItem('devtools.terms.accepted.time');
			} else {
				acceptTerms();
				return;
			}
		} else {
			localStorage.removeItem('devtools.terms.accepted');
			localStorage.removeItem('devtools.terms.accepted.time');
		}
	}

	function checkDevTools() {
		if (
			window.outerWidth - window.innerWidth > threshold ||
			window.outerHeight - window.innerHeight > threshold
		) {
			if (!isOpen) {
				printWarnings();
				isOpen = true;
			}
			// be a little cunt
			try {
				document.styleSheets[0].insertRule('* { overflow: hidden !important; }', 0);
				debugger;
				document.styleSheets[0].deleteRule(0);
			} catch {
				debugger;
			}
		} else {
			isOpen = false;
		}
	}

	// @ts-ignore
	window['acceptTerms'] = acceptTerms;

	window.addEventListener('resize', checkDevTools);
	checkDevTools();
}

export function initalizeChecking() {
	// return;// disabnle in dev
	if (!interval) {
		interval = setInterval(() => {
			detectDevtools();
		}, 150); // 500ms wasn't fast enoguh
	}
}

export function stopChecking() {
	if (interval) {
		clearInterval(interval);
		interval = null;
	}
}
