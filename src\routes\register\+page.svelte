<script lang="ts">
	import Logo from '$lib/components/icon/Logo.svelte';
	import * as Form from '$lib/components/forms';
	import { enhance } from '$app/forms';
	import type { ActionResult } from '@sveltejs/kit';
	import cityBg from '@assets/images/images/vector-city-temp.jpg';

	export let form: {
		success?: boolean;
		errors?: Record<string, string>;
		formData?: Record<string, string>;
	} = {};

	const hasError = (field: string) => form?.errors && field in form.errors;
	const getValue = (field: string) => form?.formData?.[field] || '';
	let submitting = false;

	function handleSubmit() {
		return async ({ result, update }: { result: ActionResult; update: () => Promise<void> }) => {
			submitting = true;
			try {
				await update();

				// Handle redirect if registration was successful
				if (result.type === 'success' && result.data?.redirect) {
					window.location.href = result.data.redirect;
				}
			} finally {
				submitting = false;
			}
		};
	}
</script>

<div class="relative min-h-screen">
	<!-- Background image with overlay -->
	<div class="absolute inset-0 z-0">
		<img src={cityBg} alt="City background" class="h-full w-full object-cover blur-sm filter" />
		<div class="absolute inset-0 bg-[#0f172a] bg-opacity-80"></div>
	</div>

	<!-- Content -->
	<div
		class="relative z-10 flex min-h-screen flex-col items-center justify-center px-4 py-4 md:px-8"
	>
		<div
			class="w-full max-w-5xl overflow-hidden rounded-2xl bg-[#0a0e10d9] shadow-2xl backdrop-blur-sm"
		>
			<!-- Two-column layout for larger screens -->
			<div class="flex flex-col md:flex-row">
				<!-- Left column (accent/brand section) -->
				<div
					class="flex flex-col justify-between bg-gradient-to-br from-[var(--btn-primary)] to-blue-900 p-6 md:w-2/5 md:p-8"
				>
					<div>
						<div class="mb-4 flex justify-center md:mb-8 md:justify-start">
							<a href="/" data-sveltekit-reload>
								<Logo fill="#fff" />
							</a>
						</div>
						<h2
							class="mb-3 text-center text-2xl font-bold text-white md:mb-4 md:text-left md:text-3xl"
						>
							Join our community
						</h2>
						<p class="mb-4 text-center text-gray-200 md:mb-8 md:text-left">
							Create an account to access our software solutions and phone repair services.
						</p>
					</div>
					<!-- Features list - shown for both mobile and desktop with appropriate styling -->
					<div class="mb-6 md:hidden">
						<ul class="space-y-2 text-gray-200">
							<li class="flex items-center justify-center md:justify-start">
								<svg class="mr-1 h-4 w-4 text-green-400" fill="currentColor" viewBox="0 0 20 20">
									<path
										fill-rule="evenodd"
										d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
										clip-rule="evenodd"
									/>
								</svg>
								<span class="text-xs">Personalized service</span>
							</li>
							<li class="flex items-center justify-center md:justify-start">
								<svg class="mr-1 h-4 w-4 text-green-400" fill="currentColor" viewBox="0 0 20 20">
									<path
										fill-rule="evenodd"
										d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
										clip-rule="evenodd"
									/>
								</svg>
								<span class="text-xs">Track repair history</span>
							</li>
							<li class="flex items-center justify-center md:justify-start">
								<svg class="mr-1 h-4 w-4 text-green-400" fill="currentColor" viewBox="0 0 20 20">
									<path
										fill-rule="evenodd"
										d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
										clip-rule="evenodd"
									/>
								</svg>
								<span class="text-xs">Priority support</span>
							</li>
						</ul>
					</div>
					<div class="hidden md:block">
						<ul class="space-y-2 text-gray-200">
							<li class="flex items-center">
								<svg class="mr-2 h-5 w-5 text-green-400" fill="currentColor" viewBox="0 0 20 20">
									<path
										fill-rule="evenodd"
										d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
										clip-rule="evenodd"
									/>
								</svg>
								<span>Personalized service requests</span>
							</li>
							<li class="flex items-center">
								<svg class="mr-2 h-5 w-5 text-green-400" fill="currentColor" viewBox="0 0 20 20">
									<path
										fill-rule="evenodd"
										d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
										clip-rule="evenodd"
									/>
								</svg>
								<span>Track service history and status</span>
							</li>
							<li class="flex items-center">
								<svg class="mr-2 h-5 w-5 text-green-400" fill="currentColor" viewBox="0 0 20 20">
									<path
										fill-rule="evenodd"
										d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
										clip-rule="evenodd"
									/>
								</svg>
								<span>User friendly and transparent</span>
							</li>
						</ul>
					</div>
				</div>

				<!-- Right column (form) -->
				<div class="p-6 md:w-3/5 md:p-8">
					<div class="mb-4 md:mb-6">
						<h3 class="text-xl font-bold text-white md:text-2xl">Create your account</h3>
						<p class="mt-1 text-sm text-gray-400">Fill in your details to get started</p>
					</div>

					{#if form?.errors?.form}
						<div
							class="mb-4 rounded-lg border border-red-500/50 bg-red-500/10 p-4 text-sm text-red-400"
						>
							{form.errors.form}
						</div>
					{/if}

					<form
						method="POST"
						action="?/register"
						use:enhance={handleSubmit}
						class="space-y-4 md:space-y-5"
					>
						<Form.FieldGrid>
							<Form.TextField
								label="First Name"
								name="firstName"
								value={getValue('firstName')}
								placeholder="John"
								error={form?.errors?.firstName}
							/>

							<Form.TextField
								label="Last Name"
								name="lastName"
								value={getValue('lastName')}
								placeholder="Doe"
								error={form?.errors?.lastName}
							/>
						</Form.FieldGrid>

						<Form.TextField
							label="Username"
							name="username"
							value={getValue('username')}
							placeholder="johndoe"
							error={form?.errors?.username}
						/>

						<Form.TextField
							label="Email"
							type="email"
							name="email"
							value={getValue('email')}
							placeholder="<EMAIL>"
							error={form?.errors?.email}
						/>

						<Form.TextField
							label="Phone"
							name="phone"
							value={getValue('phone')}
							error={form?.errors?.phone}
						/>

						<!-- <Form.PhoneInput
							label="Phone"
							name="phone"
							value={getValue('phone')}
							error={form?.errors?.phone}
						/> -->

						<Form.FieldGrid>
							<Form.TextField
								label="Password"
								type="password"
								name="password"
								placeholder="••••••••"
								error={form?.errors?.password}
							/>

							<Form.TextField
								label="Confirm Password"
								type="password"
								name="confirmPassword"
								placeholder="••••••••"
								error={form?.errors?.confirmPassword}
							/>
						</Form.FieldGrid>

						<Form.Checkbox id="terms" name="terms">
							{#snippet label()}
								I agree to the
								<a
									href="/legal/terms"
									class="text-[var(--text-link)] hover:text-[var(--text-link-hover)]"
								>
									Terms of Service
								</a>
								and
								<a
									href="/legal/privacy"
									class="text-[var(--text-link)] hover:text-[var(--text-link-hover)]"
								>
									Privacy Policy
								</a>
							{/snippet}
						</Form.Checkbox>

						<div class="mt-4 md:mt-6">
							<button
								type="submit"
								disabled={submitting}
								class="w-full rounded-lg bg-[var(--btn-primary)] px-4 py-2 text-sm font-medium text-white transition-colors duration-200 hover:bg-[var(--btn-hover-primary)] focus:outline-none focus:ring-2 focus:ring-[var(--btn-primary)] focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-70 md:py-3 md:text-base"
							>
								{#if submitting}
									<div class="flex items-center justify-center">
										<svg
											class="-ml-1 mr-3 h-5 w-5 animate-spin text-white"
											xmlns="http://www.w3.org/2000/svg"
											fill="none"
											viewBox="0 0 24 24"
										>
											<circle
												class="opacity-25"
												cx="12"
												cy="12"
												r="10"
												stroke="currentColor"
												stroke-width="4"
											></circle>
											<path
												class="opacity-75"
												fill="currentColor"
												d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
											></path>
										</svg>
										Creating account...
									</div>
								{:else}
									Create Account
								{/if}
							</button>
						</div>

						<div class="mt-4 text-center text-xs md:mt-6 md:text-sm">
							<p class="text-gray-400">
								Already have an account?
								<a
									href="/login"
									class="ml-1 font-medium text-[var(--text-link)] hover:text-[var(--text-link-hover)]"
								>
									Sign in
								</a>
							</p>
						</div>
					</form>
				</div>
			</div>
		</div>
	</div>
</div>

<style lang="scss">
	/* Mobile viewport adjustments */
	@media (max-width: 640px) {
		input,
		button {
			font-size: 16px; /* Prevents iOS zoom on input focus */
		}
	}

	/* Ensure the page doesn't bounce on iOS */
	html,
	body {
		position: fixed;
		overflow: hidden;
		width: 100%;
		height: 100%;
	}

	.tt {
		filter: blur(10px);
		top: 0;
		left: 0;
		z-index: -1;
		object-fit: cover;
		object-position: center;
		overflow: hidden;
		height: 100%;
		width: 100%;
	}
</style>
