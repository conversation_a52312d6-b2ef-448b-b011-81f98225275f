// import { mdsvex } from 'mdsvex';
import adapter from '@sveltejs/adapter-node';
import autoprefixer from 'autoprefixer';
import tailwindcss from 'tailwindcss';
import { sveltePreprocess } from 'svelte-preprocess';
//mdsvex()
// const dirname = path.dirname(fileURLToPath(import.meta.url));
const preprocessOpts = {
	postcss: {
		plugins: [autoprefixer, tailwindcss]
	},
	scss: {
		handleMixedImports: true
	}
};

/** @type {import('@sveltejs/kit').Config} */
const config = {
	preprocess: [sveltePreprocess(preprocessOpts)],
	onwarn: (warning, handler) => {
		const { code, frame } = warning;
		if (code === 'css_unused_selector') return;
		handler(warning);
	},

	kit: {
		adapter: adapter({
			out: 'build',
			precompress: true
		}),
		env: {
			dir: process.cwd()
		},
		alias: {
			'@backend/*': './src/backend/*',
			'@assets/*': 'assets/*'
		}
	},

	extensions: ['.svelte', '.svx']
};

export default config;
