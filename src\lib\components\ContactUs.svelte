<script lang="ts">
	import CallIcon from '$lib/components/icon/CallIcon.svelte';
	import Button from './forms/Button.svelte';
	import EmailIcon from './icon/EmailIcon.svelte';
	import { onMount } from 'svelte';
	import logo from '@assets/images/logo/1.png';

	let isVisible = false;

	onMount(() => {
		isVisible = true;
	});
</script>

<div
	class="relative overflow-hidden bg-gradient-to-br from-[var(--contact-gradient-from)] to-[var(--contact-gradient-to)] py-16 md:py-24"
>
	<!-- Animated background elements -->
	<div class="absolute inset-0 overflow-hidden">
		<div
			class="absolute -right-40 -top-40 h-80 w-80 rounded-full bg-[var(--contact-blur-bg)] blur-3xl"
		></div>
		<div
			class="absolute -bottom-40 -left-40 h-80 w-80 rounded-full bg-[var(--contact-blur-bg)] blur-3xl"
		></div>
		<div
			class="bg-[var(--contact-blur-bg)]/50 absolute left-1/2 top-1/2 h-96 w-96 -translate-x-1/2 -translate-y-1/2 rounded-full blur-3xl"
		></div>
	</div>

	<div class="container relative mx-auto px-4">
		<div class="grid gap-12 md:grid-cols-2">
			<!-- Left content -->
			<div class="flex flex-col justify-center space-y-8">
				<div class="space-y-4">
					<h1 class="text-4xl font-bold text-[var(--text-primary)] md:text-5xl">
						<span
							class="inline-block transition-all duration-500 {isVisible
								? 'translate-y-0 opacity-100'
								: 'translate-y-4 opacity-0'}"
						>
							Let's Connect
						</span>
					</h1>
					<p
						class="text-lg text-[var(--text-secondary)] transition-all delay-200 duration-500 {isVisible
							? 'translate-y-0 opacity-100'
							: 'translate-y-4 opacity-0'}"
					>
						Have a question or need assistance? We're here to help.
					</p>
				</div>

				<!-- Contact methods -->
				<div
					class="space-y-4 transition-all delay-300 duration-500 {isVisible
						? 'translate-y-0 opacity-100'
						: 'translate-y-4 opacity-0'}"
				>
					<a
						href="tel:+14698908569"
						class="group flex items-center space-x-3 rounded-lg bg-[var(--contact-card-bg)] p-4 backdrop-blur-sm transition-all hover:bg-[var(--contact-card-hover)]"
					>
						<div class="rounded-full bg-[var(--contact-icon-bg)] p-2">
							<CallIcon fill="var(--tr-blue-alt)" />
						</div>
						<span class="font-medium text-[var(--contact-text)]">+****************</span>
					</a>
					<a
						href="mailto:<EMAIL>"
						class="group flex items-center space-x-3 rounded-lg bg-[var(--contact-card-bg)] p-4 backdrop-blur-sm transition-all hover:bg-[var(--contact-card-hover)]"
					>
						<div class="rounded-full bg-[var(--contact-icon-bg)] p-2">
							<EmailIcon fill="var(--tr-blue-alt)" />
						</div>
						<span class="font-medium text-[var(--contact-text)]"><EMAIL></span>
					</a>
				</div>

				<!-- CTA Button -->
				<div
					class="delay-400 transition-all duration-500 {isVisible
						? 'translate-y-0 opacity-100'
						: 'translate-y-4 opacity-0'}"
				>
					<Button
						type="primary"
						width="full"
						class="hover:bg-[var(--tr-blue-alt)]/90 bg-[var(--tr-blue-alt)] text-[var(--text-primary)]"
					>
						{#snippet text()}
							Get in Touch
						{/snippet}
					</Button>
				</div>
			</div>

			<!-- Right content -->
			<div
				class="relative flex items-center justify-center transition-all delay-500 duration-500 {isVisible
					? 'translate-y-0 opacity-100'
					: 'translate-y-4 opacity-0'}"
			>
				<img src={logo} alt="tex-repairs" class="h-48 w-auto" />
			</div>
		</div>
	</div>
</div>

<style lang="scss">
	/* Add any additional styles here if needed */
</style>
