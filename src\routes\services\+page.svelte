<script lang="ts">
	import { fade } from 'svelte/transition';
	import SoftwareIcon from '$lib/components/icon/SoftwareIcon.svelte';
	import EmbeddedIcon from '$lib/components/icon/EmbeddedIcon.svelte';
	import WebsiteIcon from '$lib/components/icon/WebsiteIcon.svelte';
	import RepairsIcon from '$lib/components/icon/RepairsIcon.svelte';

	const services = [
		{
			title: 'Software Development',
			description:
				'Custom software solutions tailored to your business needs, from point-of-sale systems to enterprise applications.',
			icon: SoftwareIcon,
			href: '/services/software',
			gradient: 'from-blue-600 to-indigo-600',
			hoverGradient: 'from-blue-600/30 to-indigo-600/30'
		},
		{
			title: 'Embedded Systems',
			description:
				'Specialized embedded solutions including PLC development, machine learning integration, and vision systems.',
			icon: EmbeddedIcon,
			href: '/services/embedded',
			gradient: 'from-violet-600 to-cyan-600',
			hoverGradient: 'from-violet-600/30 to-cyan-600/30'
		},
		{
			title: 'Website Development',
			description:
				'Modern, responsive websites built with cutting-edge technology and best practices in design and user experience.',
			icon: WebsiteIcon,
			href: '/services/web',
			gradient: 'from-indigo-600 to-violet-600',
			hoverGradient: 'from-indigo-600/30 to-violet-600/30'
		},
		{
			title: 'Device Repairs',
			description:
				'Professional repair services for smartphones, computers, and printers with expert technicians and quick turnaround.',
			icon: RepairsIcon,
			href: '/services/repairs',
			gradient: 'from-cyan-600 to-purple-600',
			hoverGradient: 'from-cyan-600/30 to-purple-600/30'
		}
	];
</script>

<div class="space-y-16" in:fade>
	<section
		class="relative overflow-hidden rounded-2xl bg-gradient-to-r from-[var(--accent-primary)] to-[var(--accent-secondary)] py-8 md:py-12"
	>
		<div class="relative z-10">
			<h1 class="mb-4 text-4xl font-bold md:text-5xl">Our Services</h1>
			<p class="max-w-2xl text-xl text-white/90">
				Comprehensive technology solutions to help your business thrive. From custom software to
				device repairs, we've got you covered.
			</p>
		</div>
	</section>

	<section class="grid gap-8 md:grid-cols-2">
		{#each services as service}
			<a
				href={service.href}
				class="group relative overflow-hidden rounded-xl bg-gradient-to-br {service.gradient} hover:shadow-[var(--accent-primary)]/20 p-8 transition-all hover:shadow-lg"
			>
				<div
					class="absolute inset-0 bg-gradient-to-br {service.hoverGradient} opacity-0 transition-opacity duration-300 group-hover:opacity-100"
				></div>
				<div class="relative">
					<div class="mb-6 flex items-center gap-4">
						<div
							class="flex h-12 w-12 items-center justify-center rounded-lg bg-white/10 backdrop-blur-sm"
						>
							<svelte:component this={service.icon} class_name="h-6 w-6 text-white" />
						</div>
						<h3 class="text-2xl font-semibold text-white">{service.title}</h3>
					</div>
					<p class="mb-6 text-white/80">{service.description}</p>
					<div
						class="flex items-center gap-2 text-white/90 transition-transform group-hover:translate-x-2"
					>
						<span class="font-medium">Learn more</span>
						<svg
							xmlns="http://www.w3.org/2000/svg"
							fill="none"
							viewBox="0 0 24 24"
							stroke-width="2"
							stroke="currentColor"
							class="h-5 w-5"
						>
							<path
								stroke-linecap="round"
								stroke-linejoin="round"
								d="M13.5 4.5L21 12m0 0l-7.5 7.5M21 12H3"
							/>
						</svg>
					</div>
				</div>
			</a>
		{/each}
	</section>
</div>
