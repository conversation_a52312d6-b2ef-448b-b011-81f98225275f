import { writable } from 'svelte/store';
import { goto } from '$app/navigation';

export const isNavigating = writable(false);
let navigationStartTime: number | null = null;
const MIN_LOAD_TIME = 300; // 300ms minimum to prevent flickering

function handleLinkClick(event: MouseEvent) {
	const target = event.target as HTMLElement;
	const link = target.closest('a');

	// Only handle internal links
	if (
		!link ||
		link.target ||
		link.rel === 'external' ||
		link.hostname !== window.location.hostname
	) {
		return;
	}

	// Prevent default to handle navigation ourselves
	event.preventDefault();
	const href = link.href;

	// Start navigation
	isNavigating.set(true);
	navigationStartTime = Date.now();

	// Use SvelteKit's goto for navigation
	goto(href, { keepFocus: true, noScroll: true })
		.then(() => {
			// After navigation completes, ensure minimum time has passed
			const elapsedTime = Date.now() - navigationStartTime!;
			const remainingTime = Math.max(0, MIN_LOAD_TIME - elapsedTime);

			// If navigation was very fast, still show a brief loading state
			if (remainingTime > 0) {
				setTimeout(() => {
					isNavigating.set(false);
					navigationStartTime = null;
				}, remainingTime);
			} else {
				// If navigation took longer than MIN_LOAD_TIME, clear immediately
				isNavigating.set(false);
				navigationStartTime = null;
			}
		})
		.catch((error) => {
			// Ensure navigation state is cleared even if navigation fails
			isNavigating.set(false);
			navigationStartTime = null;
			console.error('Navigation failed:', error);
		});
}

// Initialize navigation handling
export function initNavigation() {
	if (typeof window === 'undefined') return;

	// Add click handler to document
	document.addEventListener('click', handleLinkClick);

	// Cleanup function
	return () => {
		document.removeEventListener('click', handleLinkClick);
	};
}
