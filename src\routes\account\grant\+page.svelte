<script lang="ts">
	import { goto } from '$app/navigation';
	import Logo from '$lib/components/icon/Logo.svelte';
	import cityBg from '@assets/images/images/vector-city-temp.jpg';
	import { onDestroy } from 'svelte';

	const { data } = $props<{ data: any }>();

	let error = $derived(data?.error);
	let success = $derived(data?.success);
	let message = $derived(data?.message);

	let countdown = $state(5);
	let interval: ReturnType<typeof setInterval>;

	$effect(() => {
		if (success) {
			interval = setInterval(() => {
				if (countdown > 1) {
					countdown -= 1;
				} else {
					clearInterval(interval);
				}
			}, 1000);
			setTimeout(() => {
				goto('/login', { replaceState: true });
			}, 5000);
		}
	});

	onDestroy(() => {
		if (interval) clearInterval(interval);
	});
</script>

<div class="relative min-h-screen">
	<!-- Background image with overlay -->
	<div class="absolute inset-0 z-0">
		<img src={cityBg} alt="City background" class="h-full w-full object-cover blur-sm filter" />
		<div class="absolute inset-0 bg-[#0f172a] bg-opacity-80"></div>
	</div>

	<!-- Content -->
	<div
		class="relative z-10 flex min-h-screen flex-col items-center justify-center px-4 py-8 md:py-16"
	>
		<div
			class="w-full max-w-md overflow-hidden rounded-2xl bg-[#0a0e10d9] p-8 shadow-2xl backdrop-blur-sm"
		>
			<div class="mb-6 flex justify-center">
				<a href="/" data-sveltekit-reload>
					<Logo fill="#fff" />
				</a>
			</div>

			{#if error}
				<div class="mb-6 text-center">
					<div class="mt-4 rounded-lg border border-red-600 bg-red-900/30 p-4 text-red-300">
						<div class="flex flex-row items-center gap-3 text-left">
							<span class="icon-[mingcute--close-line] h-5 w-5"></span>
							<div class="flex-1">
								<p>{message}</p>
							</div>
						</div>
					</div>
					<div class="mt-6">
						<a
							href="/login"
							class="inline-block w-full rounded-lg bg-[var(--btn-primary)] px-4 py-2.5 text-center font-medium text-white transition-colors duration-200 hover:bg-[var(--btn-hover-primary)] focus:outline-none focus:ring-2 focus:ring-[var(--btn-primary)] focus:ring-offset-2"
						>
							Return to Login
						</a>
					</div>
				</div>
			{:else if success}
				<div class="mb-6 text-center">
					<h2 class="text-2xl font-bold text-white">Email Verified!</h2>
					<div class="mt-4 rounded-lg border border-green-600 bg-green-900/30 p-4 text-green-300">
						<div class="flex items-center gap-3 text-left">
							<span class="icon-[mingcute--check-line] h-5 w-5"></span>
							<div class="flex-1">
								<p class="text-white">{message}</p>
							</div>
						</div>
					</div>
					<p class="mt-4 text-sm text-gray-400">
						Redirecting to login page in {countdown} second{countdown === 1 ? '' : 's'}...
					</p>
				</div>
			{/if}
		</div>
	</div>
</div>

<style>
	.container {
		max-width: 600px;
		margin: 4rem auto;
		padding: 2rem;
		text-align: center;
	}

	.error {
		background-color: #ffebee;
		border: 1px solid #ffcdd2;
		border-radius: 8px;
		padding: 2rem;
	}

	.success {
		background-color: #e8f5e9;
		border: 1px solid #c8e6c9;
		border-radius: 8px;
		padding: 2rem;
	}

	h1 {
		color: #2c3e50;
		margin-bottom: 1rem;
	}

	.redirect {
		font-size: 0.9rem;
		color: #7f8c8d;
	}

	.button {
		display: inline-block;
		background-color: #3498db;
		color: white;
		padding: 0.75rem 1.5rem;
		border-radius: 4px;
		text-decoration: none;
		transition: background-color 0.2s;
	}

	.button:hover {
		background-color: #2980b9;
	}
</style>
