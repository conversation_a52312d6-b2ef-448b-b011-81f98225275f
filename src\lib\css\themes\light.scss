.light {
	--bg: #b7d5ff;
	--bg-secondary: #f7f7f7;
	--nav-bg: #8daedf;
	--text-primary: #000000;
	--text-secondary: #1a1a1a;
	--accent: #0091ff;

	/* Debug */
	--bg-debug-header: #6e6e6e;
	--bg-debug: #0f1417;
	--text-debug-value: #39a9ff;
	--text-debug-key: #ffffff;
	--text-debug-primary: #ffffff;
	--text-debug-secondary: #a3a3a3;
	--bg-debug-code: rgba(0, 0, 0, 0.568);

	/* Button bg */
	--btn-primary: #007acc;
	--btn-hover-primary: #005fa3;
	--btn-secondary: #8dabd9;
	--btn-hover-secondary: #7aa0d8;
	--btn-error: #da2e2e;
	--btn-hover-error: #fa6a6a;
	--btn-success: #19a347;
	--btn-hover-success: #15913f;
	--btn-warning: #ff9800;
	--btn-hover-warning: #e68900;
	--btn-info: #0099cc;
	--btn-hover-info: #0086b3;

	/* Cards */
	--card-container-bg: #90beff;
	--card-bg: #d5edfd;
	--card-button-bg: #007acc;
	--card-text-primary: #102f43;
	--card-text-secondary: #4f4f4f;
	--card-hover-bg: #e8f4fd;

	/* Miscellaneous */
	--tr-blue: #6aa6cf;
	--tr-blue-alt: #3e8bb3;

	--footer-bg: #e3e3e3;

	--hero-bg: #f5f5f5;
	--hero-text-color: #121212;

	--hero-title-color: #007acc;

	--hero-button-text: #ffffff;
	--hero-button-gradient-from: #3e8bb3;
	--hero-button-gradient-to: #007acc;
	--hero-button-gradient-hover-from: #005fa3;
	--hero-button-gradient-hover-to: #3e8bb3;

	--hero-spotlight-bg: #e3eaf0;

	/** Other generic colors */
	--teal-byte: #e1eeff;
	--text-primary: #000000;
	--text-secondary: #1a1a1a;

	/** gradient */
	--gradient-start: #002a46;
	--gradient-end: #3bb0ff;

	/* Contact Section */
	--contact-gradient-from: #3e8bb3;
	--contact-gradient-to: #007acc;
	--contact-card-bg: rgba(255, 255, 255, 0.7);
	--contact-card-hover: rgba(255, 255, 255, 0.9);
	--contact-icon-bg: rgba(62, 139, 179, 0.1);
	--contact-text: #2d485a;
	--contact-blur-bg: rgba(62, 139, 179, 0.15);

	/* Error Page */
	--error-bg-gradient-from: #e8f0f7;
	--error-bg-gradient-to: #d5e3ed;
	--error-text-primary: #2d485a;
	--error-text-secondary: #3e8bb3;
	--error-accent: #5a90b2;
	--error-glow: #4a8db3;
	--error-particle: #3e8bb3;
	--error-button-bg: rgba(90, 144, 178, 0.15);
	--error-button-hover: rgba(90, 144, 178, 0.25);
	--error-button-text: #2d485a;
	--error-code-color: #3e8bb3;

	/* Error Colors */
	--error-color: #dc2626;
	--error-color-hover: #ef4444;
	--error-color-light: #fee2e2;
	--error-color-dark: #991b1b;
	--error-color-text: #ffffff;
	--error-color-text-secondary: #fecaca;

	/* Forms */
	--input-disabled-bg: #f0f0f0;
	--input-disabled-text: #808080;
	--input-readonly-bg: #f8f8f8;
	--input-readonly-text: #909090;
	--input-disabled-cursor: not-allowed;
	--input-readonly-cursor: default;
}
