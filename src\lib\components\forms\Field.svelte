<script lang="ts">
	import type { HTMLAttributes } from 'svelte/elements';
	import type { WithElementRef } from './index';
	let {
		ref = $bindable(null),
		class: className = '',
		children,
		...restProps
	}: WithElementRef<HTMLAttributes<HTMLElement>> = $props();
</script>

<div bind:this={ref} class="tex-field {className}" {...restProps}>
	{@render children?.()}
</div>

<style lang="scss">
	.tex-field {
		display: flex;
		flex-direction: column;
		padding: 10px 0;

		& label {
			margin-bottom: 0 10px;
			padding-bottom: 5px;
			font-size: 14px;
			font-weight: 600;
			color: var(--text-form-field-label);
			text-transform: uppercase;
		}

		& input {
			padding: 10px 15px;
			border: 1px solid var(--border-form-field-input);
			border-radius: 4px;
			background-color: var(--bg-form-field-input);
			color: var(--text-form-field);
			font-size: 14px;
			font-weight: 100;
			transition: all 0.2s ease-in-out;

			&:focus,
			&:hover {
				outline: none;
				border: 1px solid var(--border-form-field-input-hover);
				background-color: var(--bg-form-field-input-hover);
				color: var(--text-form-field-input-hover);
			}
		}
	}
</style>
