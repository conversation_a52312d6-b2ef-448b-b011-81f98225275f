<script lang="ts">
	import Footer from '$lib/components/Footer.svelte';
	import Navbar from '$lib/components/nav/Navbar.svelte';
	import MdWindow from './components/MDWindow.svelte';
	import { onMount } from 'svelte';
	import { Loader, isLoading } from '$lib/components/loader';
	import { goto } from '$app/navigation';

	interface Props {
		data: { error?: { status?: number; message?: string }; docId: string };
	}

	let { data }: Props = $props();
	let terms = $state<string | null>(null);
	let error = $state<{ status: number; message: string } | null>(null);
	let currentPath = $state<string>('');

	onMount(() => {
		// Store initial path
		currentPath = window.location.pathname;

		// Add location change listener
		const handleLocationChange = () => {
			if (window.location.pathname !== currentPath) {
				window.location.reload();
			}
		};
	});

	$effect(() => {
		if (data.docId) {
			// reset states
			terms = null;
			error = null;
			isLoading.set(true);

			fetchData();
		}
	});

	function generateErrorContent() {
		if (!error) return '';
		return `# Error ${error.status}\n<p class="text-sm text-center">${error.message}</p>`;
	}

	async function fetchData() {
		try {
			const response = await fetch(`/api/legal/${data.docId}`, {
				headers: {
					Authorization: `Bearer ${
						document.cookie
							.split('; ')
							.find((row) => row.startsWith('session_id='))
							?.split('=')[1] || ''
					}`,
					'X-Domain-Intent': 'agent'
				}
			});
			const result = await response.json();

			if (!response.ok) {
				error = result.error;
			} else {
				terms = result.content;
			}
		} catch (e) {
			error = { status: 500, message: 'Failed to fetch document' };
		} finally {
			isLoading.set(false);
		}
	}
</script>

<div class="min-h-screen">
	<Navbar />

	{#if data.error}
		<div class="flex flex-col items-center gap-2 py-20 max-md:min-h-screen">
			<h1 class="text-3xl">Error {data.error.status}</h1>
			<p class="text-center text-sm">{data.error.message}</p>
		</div>

		<div class="w-full md:absolute md:bottom-0 md:left-0">
			<Footer />
		</div>
	{:else if $isLoading}
		<div class="fixed inset-0 z-[100000] overflow-hidden">
			<div class="h-full w-full overflow-hidden">
				<Loader transparent={true} />
			</div>
		</div>
	{:else if error}
		<div class="flex flex-col items-center gap-2 py-20 max-md:min-h-screen">
			<h1 class="text-3xl">Error {error.status}</h1>
			<p class="text-center text-sm">{error.message}</p>
		</div>

		<div class="w-full md:absolute md:bottom-0 md:left-0">
			<Footer />
		</div>
	{:else if terms}
		<div class="flex min-h-screen flex-col overflow-x-hidden">
			<MdWindow content={terms} />
		</div>
		<Footer />
	{:else}
		<div class="flex flex-col items-center gap-2 py-20 max-md:min-h-screen">
			<h1 class="text-3xl">Error</h1>
			<p class="text-center text-sm">Document not found</p>
		</div>
		<Footer />
	{/if}
</div>
