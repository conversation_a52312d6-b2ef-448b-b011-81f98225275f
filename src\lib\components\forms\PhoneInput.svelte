<script lang="ts">
	import { onMount } from 'svelte';
	import type { WithElementRef } from './index';
	import type { HTMLAttributes } from 'svelte/elements';
	import { Field } from './index';
	import FieldRow from './FieldRow.svelte';

	interface Props {
		value?: string;
		name?: string;
		label?: string;
		error?: string;
		disabled?: boolean;
		required?: boolean;
		onVerify?: (phone: string) => Promise<boolean>;
	}

	let {
		ref = $bindable(null),
		value = $bindable(''),
		name = 'phone',
		label = 'Phone',
		error,
		disabled = false,
		required = false,
		onVerify,
		class: className,
		...restProps
	}: WithElementRef<HTMLAttributes<HTMLElement>> & Props = $props();

	let part1 = $state('');
	let part2 = $state('');
	let part3 = $state('');
	let input1: HTMLInputElement;
	let input2: HTMLInputElement;
	let input3: HTMLInputElement;
	let isVerifying = $state(false);
	let verificationMessage = $state('');
	let verificationSuccess = $state(false);

	// Update the combined value when any part changes
	$effect(() => {
		if (part1 || part2 || part3) {
			value = `${part1}${part2}${part3}`;
		} else {
			value = '';
		}
	});

	// Split incoming value into parts
	$effect(() => {
		if (value && value.length >= 10) {
			part1 = value.slice(0, 3);
			part2 = value.slice(3, 6);
			part3 = value.slice(6, 10);
		}
	});

	function handleInput(
		event: KeyboardEvent,
		input: HTMLInputElement,
		nextInput?: HTMLInputElement,
		prevInput?: HTMLInputElement
	) {
		// Allow only numbers, backspace, delete, and arrow keys
		if (
			!/^\d$/.test(event.key) &&
			!['Backspace', 'Delete', 'ArrowLeft', 'ArrowRight', 'Tab'].includes(event.key)
		) {
			event.preventDefault();
			return;
		}

		// Handle backspace when input is empty
		if (event.key === 'Backspace' && input.value.length === 0 && prevInput) {
			prevInput.focus();
			return;
		}

		// Move to next input when max length is reached
		if (input.value.length >= input.maxLength && nextInput && event.key.match(/\d/)) {
			nextInput.focus();
		}
	}

	async function handleVerify() {
		if (!isComplete()) {
			verificationMessage = 'Please enter a complete phone number';
			verificationSuccess = false;
			return;
		}

		isVerifying = true;
		verificationMessage = '';

		try {
			if (onVerify) {
				const success = await onVerify(value);
				verificationSuccess = success;
				verificationMessage = success ? 'Phone number verified!' : 'Failed to verify phone number';
			}
		} catch (err) {
			verificationSuccess = false;
			verificationMessage = 'Error verifying phone number';
		} finally {
			isVerifying = false;
		}
	}

	function isComplete() {
		return part1.length === 3 && part2.length === 3 && part3.length === 4;
	}

	onMount(() => {
		// Initialize from value if provided
		if (value) {
			part1 = value.slice(0, 3);
			part2 = value.slice(3, 6);
			part3 = value.slice(6, 10);
		}
	});
</script>

<div>
	<label for={name} class="mb-1 block text-sm font-medium text-gray-300">{label}</label>
	<input type="hidden" {name} {value} />
	<FieldRow>
		<input
			type="tel"
			id={`${name}-1`}
			bind:this={input1}
			bind:value={part1}
			maxlength="3"
			placeholder="123"
			{disabled}
			{required}
			onkeydown={(e) => handleInput(e, input1, input2)}
			class="field rounded-lg border border-[#ffffff1a] bg-[#ffffff0f] px-3 py-2 text-center text-sm text-white placeholder-gray-500 transition-colors focus:border-[var(--btn-primary)] focus:ring focus:ring-[var(--btn-primary)] focus:ring-opacity-20 md:text-base"
		/>
		<input
			type="tel"
			id={`${name}-2`}
			bind:this={input2}
			bind:value={part2}
			maxlength="3"
			placeholder="456"
			{disabled}
			{required}
			onkeydown={(e) => handleInput(e, input2, input3, input1)}
			class="field rounded-lg border border-[#ffffff1a] bg-[#ffffff0f] px-3 py-2 text-center text-sm text-white placeholder-gray-500 transition-colors focus:border-[var(--btn-primary)] focus:ring focus:ring-[var(--btn-primary)] focus:ring-opacity-20 md:text-base"
		/>
		<input
			type="tel"
			id={`${name}-3`}
			bind:this={input3}
			bind:value={part3}
			maxlength="4"
			placeholder="7890"
			{disabled}
			{required}
			onkeydown={(e) => handleInput(e, input3, undefined, input2)}
			class="field rounded-lg border border-[#ffffff1a] bg-[#ffffff0f] px-3 py-2 text-center text-sm text-white placeholder-gray-500 transition-colors focus:border-[var(--btn-primary)] focus:ring focus:ring-[var(--btn-primary)] focus:ring-opacity-20 md:text-base"
		/>
	</FieldRow>
	{#if error}
		<p class="mt-1 text-xs text-red-500">{error}</p>
	{/if}
	{#if verificationMessage}
		<p class="text-sm {verificationSuccess ? 'text-green-500' : 'text-red-500'} mt-1">
			{verificationMessage}
		</p>
	{/if}
	{#if onVerify}
		<button
			type="button"
			class="mt-2 rounded-lg bg-[var(--btn-primary)] px-4 py-2 text-white hover:bg-[var(--btn-hover-primary)] disabled:opacity-50"
			onclick={handleVerify}
			disabled={!isComplete() || isVerifying || disabled}
		>
			{isVerifying ? 'Verifying...' : 'Verify Phone'}
		</button>
	{/if}
</div>

<style>
	/* Remove any conflicting styles */
</style>
