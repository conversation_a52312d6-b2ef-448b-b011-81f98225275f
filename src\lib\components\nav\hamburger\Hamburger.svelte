<script lang="ts">
	import type { Snippet } from 'svelte';
	import Logo from '../../icon/Logo.svelte';

	interface Props {
		checked: boolean;
		content: Snippet;
		sessionUser: any;
	}

	let { checked = $bindable(), content, sessionUser }: Props = $props();

	$effect.pre(() => {
		// check if the checked prop is a boolean
		if (checked) {
			document.body.style.overflow = 'hidden';
		} else {
			document.body.style.overflow = '';
		}
	});
</script>

{@render content()}

<!-- Mobile Menu -->
<div class="pointer-events-none fixed inset-0 z-50" class:pointer-events-auto={checked}>
	<!-- Backdrop -->
	<button
		class="fixed inset-0 bg-black/30 backdrop-blur-sm transition-opacity duration-300"
		class:opacity-0={!checked}
		class:pointer-events-none={!checked}
		onclick={() => {
			checked = false;
		}}
		onkeydown={(e) => e.key === 'Escape' && (checked = false)}
		aria-label="Close menu"
	></button>

	<!-- Menu Panel -->
	<div
		class="fixed left-0 top-0 z-50 flex h-[100dvh] w-[280px] transform flex-col overflow-hidden bg-[#1c1c1c] shadow-2xl transition-transform duration-300 ease-out"
		class:translate-x-[-100%]={!checked}
	>
		<!-- Header with close button -->
		<div class="flex h-[60px] shrink-0 items-center justify-between border-b border-white/10 px-6">
			<div class="flex w-full origin-center scale-[0.65] items-center justify-center">
				<a href="/" class="flex items-center text-white" data-sveltekit-reload>
					<Logo fill="#fff" />
				</a>
			</div>
			<button
				class="absolute right-6 flex h-8 w-8 items-center justify-center rounded-full text-white/60 transition-all duration-300 hover:rotate-90 hover:text-white"
				onclick={() => (checked = false)}
				aria-label="Close navigation menu"
			>
				<span class="icon-[mingcute--close-line] h-5 w-5"></span>
			</button>
		</div>

		<!-- Navigation Links -->
		<div class="flex flex-col py-4">
			<a
				href="/"
				class="group mx-4 flex items-center gap-3 rounded-xl px-4 py-2.5 text-[15px] font-medium text-white/60 transition-all duration-200 hover:bg-white/[0.06] hover:text-white"
				data-sveltekit-reload
			>
				<div class="flex h-8 w-8 items-center justify-center rounded-lg bg-white/5">
					<span class="icon-[f7--house] h-5 w-5"></span>
				</div>
				<span>Home</span>
			</a>
			<a
				href="/services"
				class="group mx-4 flex items-center gap-3 rounded-xl px-4 py-2.5 text-[15px] font-medium text-white/60 transition-all duration-200 hover:bg-white/[0.06] hover:text-white"
				data-sveltekit-reload
			>
				<div class="flex h-8 w-8 items-center justify-center rounded-lg bg-white/5">
					<span class="icon-[ic--baseline-miscellaneous-services] h-5 w-5"></span>
				</div>
				<span>Services</span>
			</a>
			<a
				href="/about"
				class="group mx-4 flex items-center gap-3 rounded-xl px-4 py-2.5 text-[15px] font-medium text-white/60 transition-all duration-200 hover:bg-white/[0.06] hover:text-white"
				data-sveltekit-reload
			>
				<div class="flex h-8 w-8 items-center justify-center rounded-lg bg-white/5">
					<span class="icon-[mingcute--information-line] h-5 w-5"></span>
				</div>
				<span>About</span>
			</a>

			{#if sessionUser?.id !== undefined}
				<div class="mx-4 my-2 border-t border-white/5"></div>

				<a
					href="/account/@me"
					class="group mx-4 flex items-center gap-3 rounded-xl px-4 py-2.5 text-[15px] font-medium text-white/60 transition-all duration-200 hover:bg-white/[0.06] hover:text-white"
					data-sveltekit-reload
				>
					<div class="flex h-8 w-8 items-center justify-center rounded-lg bg-white/5">
						<span class="icon-[mingcute--user-4-line] h-5 w-5"></span>
					</div>
					<span>My Account</span>
				</a>
				<a
					href="/account/orders"
					class="group mx-4 flex items-center gap-3 rounded-xl px-4 py-2.5 text-[15px] font-medium text-white/60 transition-all duration-200 hover:bg-white/[0.06] hover:text-white"
					data-sveltekit-reload
				>
					<div class="flex h-8 w-8 items-center justify-center rounded-lg bg-white/5">
						<span class="icon-[mingcute--time-line] h-5 w-5"></span>
					</div>
					<span>Order History</span>
				</a>
				<a
					href="/account/settings"
					class="group mx-4 flex items-center gap-3 rounded-xl px-4 py-2.5 text-[15px] font-medium text-white/60 transition-all duration-200 hover:bg-white/[0.06] hover:text-white"
					data-sveltekit-reload
				>
					<div class="flex h-8 w-8 items-center justify-center rounded-lg bg-white/5">
						<span class="icon-[mingcute--settings-3-line] h-5 w-5"></span>
					</div>
					<span>Settings</span>
				</a>
			{/if}

			<div class="mx-4 my-2 border-t border-white/5"></div>

			<a
				href="/support"
				class="group mx-4 flex items-center gap-3 rounded-xl px-4 py-2.5 text-[15px] font-medium text-white/60 transition-all duration-200 hover:bg-white/[0.06] hover:text-white"
				data-sveltekit-reload
			>
				<div class="flex h-8 w-8 items-center justify-center rounded-lg bg-white/5">
					<span class="icon-[mingcute--headphone-line] h-5 w-5"></span>
				</div>
				<span>Support</span>
			</a>
			<a
				href="/faq"
				class="group mx-4 flex items-center gap-3 rounded-xl px-4 py-2.5 text-[15px] font-medium text-white/60 transition-all duration-200 hover:bg-white/[0.06] hover:text-white"
				data-sveltekit-reload
			>
				<div class="flex h-8 w-8 items-center justify-center rounded-lg bg-white/5">
					<span class="icon-[mingcute--question-line] h-5 w-5"></span>
				</div>
				<span>FAQ</span>
			</a>
		</div>

		<!-- Bottom section with login/account -->
		<div class="mt-auto border-t border-white/10 px-6 py-4">
			{#if sessionUser?.id !== undefined}
				<div class="flex items-center gap-3">
					<div class="flex h-8 w-8 items-center justify-center rounded-full bg-white/10">
						<span class="icon-[mingcute--user-4-line] h-4 w-4 text-white"></span>
					</div>
					<div class="flex flex-col">
						<span class="text-sm font-medium text-white">{sessionUser.username}</span>
						<a href="/logout" class="text-xs text-white/60 hover:text-white">Sign out</a>
					</div>
				</div>
			{:else}
				<a
					href="/login"
					class="flex items-center gap-3 text-[15px] font-medium text-white/60 transition-colors hover:text-white"
				>
					<span class="icon-[mingcute--user-4-line] h-5 w-5"></span>
					<span>Sign in</span>
				</a>
			{/if}
		</div>
	</div>
</div>
