-- First create the enum type for token types
CREATE TYPE token_type AS ENUM ('email_verification', 'password_reset');

-- Modify the EmailTokens table to include the type field
ALTER TABLE EmailTokens 
    ADD COLUMN type token_type NOT NULL DEFAULT 'email_verification',
    DROP CONSTRAINT IF EXISTS EmailTokens_pkey,
    ADD PRIMARY KEY (user_id, type), 
    ADD CONSTRAINT unique_token UNIQUE (token);

-- Update existing tokens to be email verification tokens
UPDATE EmailTokens SET type = 'email_verification' WHERE type IS NULL;
CREATE INDEX IF NOT EXISTS idx_email_tokens_expires_at ON EmailTokens(expires_at);
CREATE INDEX IF NOT EXISTS idx_email_tokens_token ON EmailTokens(token); 