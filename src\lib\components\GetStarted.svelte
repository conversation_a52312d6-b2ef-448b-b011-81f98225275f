<script lang="ts">
	import { createRawSnippet, type Snippet } from 'svelte';
	import PropError from './util/PropError.svelte';

	interface Props {
		description?: Snippet;
	}

	let { description }: Props = $props();
</script>

<section
	class="to-[var(--card-container-bg)]/50 relative flex min-h-[80vh] items-center justify-center overflow-hidden bg-gradient-to-br from-[var(--card-container-bg)]"
>
	<div class="bg-grid-pattern absolute inset-0 opacity-5"></div>
	<div class="container relative z-10 mx-auto px-6 sm:px-8">
		<div class="grid gap-8 lg:grid-cols-2">
			<div class="flex flex-col justify-center space-y-4 sm:space-y-6">
				<h1 class="text-3xl font-bold tracking-tight text-text-primary sm:text-4xl md:text-6xl">
					Tech Solutions and Repairs,
					<span class="bg-gradient-to-r from-blue-500 to-purple-500 bg-clip-text text-transparent"
						>All in one.</span
					>
				</h1>
				<p class="text-base text-text-secondary sm:text-lg">
					We repair devices, build custom software solutions, and create embedded systems to help
					your business grow and succeed.
				</p>
				<div class="flex flex-col gap-3 sm:flex-row sm:gap-4">
					<a
						href="/contact"
						class="rounded-lg bg-blue-600 px-6 py-3 text-center text-white transition-all hover:bg-blue-700"
					>
						Get Started
					</a>
					<a
						href="/services"
						class="rounded-lg border border-gray-700 px-6 py-3 text-center text-text-primary transition-all hover:bg-gray-800"
					>
						Explore Services
					</a>
				</div>
			</div>
			<div class="relative hidden lg:block">
				<div
					class="absolute inset-0 rounded-2xl bg-gradient-to-br from-blue-500/20 to-purple-500/20 blur-3xl"
				></div>
				<div class="relative rounded-2xl border border-gray-800 bg-[var(--card-container-bg)] p-6">
					<div class="space-y-6">
						<div class="flex items-center space-x-4">
							<div class="h-12 w-12 rounded-full bg-blue-500/20 p-2">
								<svg
									xmlns="http://www.w3.org/2000/svg"
									class="h-8 w-8 text-blue-500"
									fill="none"
									viewBox="0 0 24 24"
									stroke="currentColor"
								>
									<path
										stroke-linecap="round"
										stroke-linejoin="round"
										stroke-width="2"
										d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4"
									/>
								</svg>
							</div>
							<div>
								<h3 class="text-xl font-semibold text-text-primary">Custom Software & Websites</h3>
								<p class="text-text-secondary">Tailored solutions for your unique needs</p>
							</div>
						</div>
						<div class="flex items-center space-x-4">
							<div class="h-12 w-12 rounded-full bg-purple-500/20 p-2">
								<svg
									xmlns="http://www.w3.org/2000/svg"
									class="h-8 w-8 text-purple-500"
									fill="none"
									viewBox="0 0 24 24"
									stroke="currentColor"
								>
									<path
										stroke-linecap="round"
										stroke-linejoin="round"
										stroke-width="2"
										d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z"
									/>
								</svg>
							</div>
							<div>
								<h3 class="text-xl font-semibold text-text-primary">Embedded Systems</h3>
								<p class="text-text-secondary">Smart devices & industrial automation</p>
							</div>
						</div>
						<div class="flex items-center space-x-4">
							<div class="h-12 w-12 rounded-full bg-indigo-500/20 p-2">
								<svg
									xmlns="http://www.w3.org/2000/svg"
									class="h-8 w-8 text-indigo-500"
									fill="none"
									viewBox="0 0 24 24"
									stroke="currentColor"
								>
									<path
										stroke-linecap="round"
										stroke-linejoin="round"
										stroke-width="2"
										d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9m-9 9a9 9 0 019-9"
									/>
								</svg>
							</div>
							<div>
								<h3 class="text-xl font-semibold text-text-primary">Device Repairs & Revival</h3>
								<p class="text-text-secondary">Bringing your devices back from the dead</p>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</section>
