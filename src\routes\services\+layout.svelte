<script lang="ts">
	import Navbar from '$lib/components/nav/Navbar.svelte';
	import Footer from '$lib/components/Footer.svelte';
	import { page } from '$app/state';
</script>

<Navbar />

<div class="min-h-screen bg-[var(--bg-primary)] text-white">
	<div class="container mx-auto px-4 py-8">
		<nav class="mb-8">
			<ul class="flex flex-wrap gap-4">
				{#each [{ href: '/services/software', label: 'Software Development' }, { href: '/services/embedded', label: 'Embedded Systems' }, { href: '/services/web', label: 'Website Development' }, { href: '/services/repairs', label: 'Device Repairs' }] as link}
					<li>
						<a
							href={link.href}
							class="group relative inline-flex items-center rounded-lg px-5 py-2.5 text-sm font-medium transition-all duration-300 ease-out
								{page.url.pathname === link.href
								? 'shadow-[var(--accent-primary)]/20 bg-gradient-to-r from-[var(--accent-primary)] to-[var(--accent-secondary)] text-white shadow-lg'
								: 'hover:from-[var(--accent-primary)]/90 hover:to-[var(--accent-secondary)]/90 hover:shadow-[var(--accent-primary)]/10 bg-[var(--card-container-bg)] text-[var(--text-secondary)] hover:bg-gradient-to-r hover:text-white hover:shadow-md'}"
						>
							<span class="relative z-10">{link.label}</span>
							{#if page.url.pathname === link.href}
								<span
									class="absolute inset-0 rounded-lg bg-gradient-to-r from-[var(--accent-primary)] to-[var(--accent-secondary)] opacity-100 transition-opacity duration-300"
								></span>
							{:else}
								<span
									class="absolute inset-0 rounded-lg bg-gradient-to-r from-[var(--accent-primary)] to-[var(--accent-secondary)] opacity-0 transition-opacity duration-300 group-hover:opacity-100"
								></span>
							{/if}
						</a>
					</li>
				{/each}
			</ul>
		</nav>

		<slot />
	</div>
</div>

<Footer />
