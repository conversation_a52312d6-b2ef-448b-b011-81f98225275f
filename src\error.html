<!doctype html>
<html lang="en">
	<head>
		<meta charset="UTF-8" />
		<meta name="viewport" content="width=device-width, initial-scale=1.0" />
		<title>Error Page</title>
		<style>
			:root {
				--error-bg-gradient-from: #1a1a1a;
				--error-bg-gradient-to: #2d2d2d;
				--error-particle: rgba(255, 255, 255, 0.5);
				--error-code-color: #ffffff;
				--error-text-primary: #ffffff;
				--error-text-secondary: #cccccc;
				--error-button-bg: rgba(255, 255, 255, 0.1);
				--error-button-text: #ffffff;
				--error-button-hover: rgba(255, 255, 255, 0.2);
				--error-glow: rgba(255, 255, 255, 0.3);
			}

			body {
				margin: 0;
				font-family:
					system-ui,
					-apple-system,
					BlinkMacSystemFont,
					'Segoe UI',
					Robot<PERSON>,
					sans-serif;
			}

			.error-container {
				min-height: 100vh;
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: center;
				background: linear-gradient(
					135deg,
					var(--error-bg-gradient-from),
					var(--error-bg-gradient-to)
				);
				position: relative;
				overflow: hidden;
				padding: 2rem;
			}

			.particle {
				position: absolute;
				background: var(--error-particle);
				border-radius: 50%;
				opacity: 0.6;
				pointer-events: none;
				filter: blur(1px);
			}

			.error-content {
				text-align: center;
				z-index: 1;
				max-width: 800px;
			}

			.error-code {
				font-size: 8rem;
				font-weight: 900;
				color: var(--error-code-color);
				margin: 0;
				line-height: 1;
				opacity: 0.9;
			}

			.error-message {
				font-size: 2rem;
				color: var(--error-text-primary);
				margin: 1rem 0 2rem;
			}

			.error-description {
				color: var(--error-text-secondary);
				font-size: 1.2rem;
				margin-bottom: 3rem;
				max-width: 600px;
				margin-left: auto;
				margin-right: auto;
			}

			.home-button {
				background: var(--error-button-bg);
				color: var(--error-button-text);
				border: none;
				padding: 1rem 2rem;
				font-size: 1.1rem;
				border-radius: 50px;
				cursor: pointer;
				transition: all 0.3s ease;
				backdrop-filter: blur(10px);
				border: 1px solid rgba(255, 255, 255, 0.1);
			}

			.home-button:hover {
				background: var(--error-button-hover);
				transform: translateY(-2px);
				box-shadow: 0 0 20px var(--error-glow);
			}
		</style>
	</head>
	<body>
		<div class="error-container" id="container">
			<div class="error-content">
				<h1 class="error-code">%sveltekit.status%</h1>
				<h2 class="error-message">%sveltekit.error.message%</h2>
				<p class="error-description" id="random-message"></p>
				<button class="home-button" onclick="window.location.href='/'">Return Home</button>
			</div>
		</div>

		<script>
			const container = document.getElementById('container');
			const particles = [];
			const numParticles = 50;

			for (let i = 0; i < numParticles; i++) {
				const particle = document.createElement('div');
				particle.className = 'particle';
				container.appendChild(particle);

				particles.push({
					element: particle,
					x: Math.random() * 100,
					y: Math.random() * 100,
					size: Math.random() * 3 + 1,
					speedX: (Math.random() - 0.5) * 0.2,
					speedY: (Math.random() - 0.5) * 0.2
				});
			}

			function animateParticles() {
				particles.forEach((p) => {
					p.x = (p.x + p.speedX + 100) % 100;
					p.y = (p.y + p.speedY + 100) % 100;
					p.element.style.left = p.x + '%';
					p.element.style.top = p.y + '%';
					p.element.style.width = p.size + 'px';
					p.element.style.height = p.size + 'px';
				});
				requestAnimationFrame(animateParticles);
			}

			const messages = [
				"You've stumbled into a void.",
				'This is not the page you are looking for (probably).',
				'Uh oh, whoopsie how did we get here?',
				'You opened the wrong door, try the other one.',
				"Oh hey! You've let the light in by finding this page, please click home page so it can be dark and I can sleep.",
				"I didn't spend forever testing this website just for you to find the 404 page. Are you really undermining all my hard work?",
				"I'm not even mad, I'm just disappointed.",
				'Why are you even reading this? You should have clicked return home by now.',
				"I didn't spend a bunch of time designing this page just for you click away.",
				"Sorry, I'm not as cool as google, there's no game here.",
				"This is why you don't go mining at night.",
				"Something tells me you haven't drank enough water today.",
				'What do you call a twitchy cow?',
				'Beef jerky.'
			];

			document.getElementById('random-message').textContent =
				messages[Math.floor(Math.random() * messages.length)];
			animateParticles();
		</script>
	</body>
</html>
