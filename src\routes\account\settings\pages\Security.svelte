<script lang="ts">
	import * as Form from '$lib/components/forms';
	import Button from '$lib/components/forms/Button.svelte';
	import type { Snippet } from 'svelte';

	export let handleSubmit: (event: Event) => void;
</script>

<div class="space-y-8">
	<div>
		<h2 class="mb-1 text-xl font-semibold text-[var(--text-primary)]">Security Settings</h2>
		<p class="text-sm text-[var(--text-secondary)]">Manage your password and account security.</p>
	</div>
	<form onsubmit={handleSubmit} class="space-y-6">
		<div class="space-y-4">
			<div class="space-y-2">
				<Form.TextField
					label="Current Password"
					name="currentPassword"
					type="password"
					placeholder="Enter your current password"
				/>
			</div>
			<div class="space-y-2">
				<Form.TextField
					label="New Password"
					name="newPassword"
					type="password"
					placeholder="Enter your new password"
				/>
			</div>
			<div class="space-y-2">
				<Form.TextField
					label="Confirm New Password"
					name="confirmPassword"
					type="password"
					placeholder="Confirm your new password"
				/>
			</div>
		</div>
		<div class="flex justify-end">
			<Button type="primary">
				{#snippet text()}
					Update Password
				{/snippet}
			</Button>
		</div>
	</form>
</div>
