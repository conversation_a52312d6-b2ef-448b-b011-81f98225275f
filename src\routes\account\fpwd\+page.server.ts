import { fail } from '@sveltejs/kit';
import { z } from 'zod';
import type { Actions } from './$types';
import { AuthService } from '@backend/auth';

// todo: this should be in our types file
const emailSchema = z.object({
	email: z.string().email('Please enter a valid email address')
});

export const actions = {
	reset: async ({ request }) => {
		const formData = await request.formData();
		const email = formData.get('email') as string;

		// Validate the email
		const result = emailSchema.safeParse({ email });
		if (!result.success) {
			return fail(400, {
				formData: { email },
				errors: { email: result.error.errors[0].message }
			});
		}

		try {
			// Attempt to send password reset email
			const resetResult = await AuthService.sendPasswordResetEmail(email);

			if (!resetResult) {
				return fail(500, {
					formData: { email },
					errors: {
						form: 'Failed to send password reset email. Please try again later.'
					}
				});
			}

			return {
				success: true,
				message:
					'If an account exists with this email, you will receive password reset instructions shortly.'
			};
		} catch (error) {
			console.error('Password reset error:', error);
			return fail(500, {
				formData: { email },
				errors: {
					form: 'An error occurred while processing your request. Please try again later.'
				}
			});
		}
	}
} satisfies Actions;
