<script lang="ts">
	import type { Snippet } from 'svelte';
	import type { Action } from 'svelte/action';
	import PropError from './PropError.svelte';

	interface Props {
		component: Snippet;
		target?: string;
		class?: string;
		style?: string;
		retain?: boolean;
	}

	let {
		component,
		target = undefined,
		class: className = '',
		style: styles = '',
		retain = true
	}: Props = $props();

	const draggable: Action = (node) => {
		let windowWidth = $state(window.innerWidth);
		let windowHeight = $state(window.innerHeight);

		let left = $state(node.getBoundingClientRect().left);
		let top = $state(node.getBoundingClientRect().top);
		let isDragging = $state(false);

		$effect(() => {
			const handleMousedown = (event: MouseEvent) => {
				// event.preventDefault();
				// check if intersection
				if (target) {
					if (
						!event
							.composedPath()
							.some((el) =>
								el instanceof HTMLElement ? (el as HTMLElement)?.classList?.contains(target) : false
							)
					) {
						return;
					}
				}
				isDragging = true;
			};

			const handleMousemove = (event: MouseEvent) => {
				if (!isDragging) return;

				// make sure we dont go off the screen.
				if (
					retain &&
					(left + event.movementX < 0 ||
						left + event.movementX + node.offsetWidth > windowWidth ||
						top + event.movementY < 0 ||
						top + event.movementY + node.offsetHeight > windowHeight)
				) {
					return;
				}

				left += event.movementX;
				top += event.movementY;
				node.style.left = `${left}px`;
				node.style.top = `${top}px`;
			};

			const handleMouseup = (_event: MouseEvent) => {
				isDragging = false;
			};

			node.addEventListener('mousedown', handleMousedown);
			window.addEventListener('mousemove', handleMousemove);
			window.addEventListener('mouseup', handleMouseup);
			window.addEventListener('resize', () => {
				windowWidth = window.innerWidth;
				windowHeight = window.innerHeight;
			});

			return () => {
				node.removeEventListener('mousedown', handleMousedown);
				window.removeEventListener('mousemove', handleMousemove);
				window.removeEventListener('mouseup', handleMouseup);
			};
		});
	};
</script>

<div class="absolute {className}" use:draggable style={styles}>
	{#if !component}
		<PropError prop="component" component="Draggable.svelte" />
	{:else}
		{@render component()}
	{/if}
</div>
