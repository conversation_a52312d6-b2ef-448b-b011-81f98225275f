import { building } from '$app/environment';
import { execSync } from 'child_process';
import { Time } from '$lib/texrepairs/utils/time.js';
import { json, redirect } from '@sveltejs/kit';
import type { Hook } from './index.js';
import { Session } from '../src/lib/server/sessions/index.js';

const TEST_USER = {
	id: '0',
	username: 'john_doe',
	email: '<EMAIL>',
	verified: false,
	user_info: {
		first_name: '<PERSON>',
		last_name: '<PERSON><PERSON>',
		phone_number: '******-355-4545'
	}
};

/**
 * Handles all routes from /dbg
 *
 * Making them NOT available in prod, loads AFTER session
 */

export const onHandle: Hook = async ({ event, resolve }, next) => {
	if (building) {
		return resolve(event);
	}

	const url = new URL(event.request.url);

	if (url.pathname.startsWith('/dbg') && process.env.NODE_ENV === 'production') {
		return new Response('Not Found', { status: 404 });
	}

	if (url.pathname === '/dbg/s/reset') {
		// we reset the session and redirect to home
		const ALWAYS_EXPIRED = new Date(0);
		event.locals.session?.destroy();
		return new Response(null, {
			status: 303,
			headers: {
				Location: '/',
				'Set-Cookie': `session_id=; path=/; expires=${ALWAYS_EXPIRED.toUTCString()}; secure=${process.env.NODE_ENV === 'production'}`
			}
		});
	}

	if (url.pathname === '/dbg/s/test-user') {
		if (event.locals.session?.storage) {
			event.locals.session.storage.user = TEST_USER;
			await event.locals.session.save(true);
		}

		const ALWAYS_ALLOWED = new Date(new Time().years(99).fromNow());

		return new Response(null, {
			status: 303,
			headers: {
				Location: '/',
				'Set-Cookie': `session_id=${event.locals.session?.id}; path=/; expires=${ALWAYS_ALLOWED.toUTCString()} secure=${process.env.NODE_ENV === 'production'}`
			}
		});
	}

	if (url.pathname === '/dbg/s/update') {
		if (event.locals.session?.storage) {
			// delete the current session.
			event.locals.session?.destroy();

			// create a new one with the request json
			const rawData = await event.request.formData();
			const data = Object.fromEntries(rawData.entries()) as any;

			console.log(data);

			// Create a new session with the provided data
			const newSession = new Session();
			newSession.storage = {
				user: {
					id: data['user.id'] || '',
					username: data['user.username'] || '',
					email: data['user.email'] || '',
					user_info: {
						first_name: data['user.first_name'] || '',
						last_name: data['user.last_name'] || '',
						phone_number: data['user.phone_number'] || '',
						billing_address: data['user.billing_address'] || undefined,
						billing_city: data['user.billing_city'] || undefined,
						billing_state: data['user.billing_state'] || undefined,
						billing_country: data['user.billing_country'] || undefined,
						billing_postal_code: data['user.billing_postal_code'] || undefined
					},
					verified: data['user.verified'] || false
				},
				permissions: data.permissions
					? data.permissions.split(',').map((p: string) => p.trim())
					: [],
				createdAt: Date.now()
			};

			await newSession.save(true);

			// Set the new session ID in cookies
			const ALWAYS_ALLOWED = new Date(new Time().years(99).fromNow());
			return new Response(JSON.stringify({ success: true }), {
				status: 303,
				headers: {
					Location: '/',
					'Set-Cookie': `session_id=${newSession.id}; path=/; expires=${ALWAYS_ALLOWED.toUTCString()}; secure=${process.env.NODE_ENV === 'production'}`
				}
			});
		} else {
			return json({ success: false, message: 'No active session' }, { status: 401 });
		}
	}

	if (url.pathname === '/dbg/git/pull') {
		// exec git stash then git pull then git stash pop
		let err = false;
		try {
			execSync('git stash');
			execSync('git pull');
			execSync('git stash pop');
		} catch (e) {
			console.log(e);
			err = true;
		}

		if (err) {
			return redirect(303, '/?git-pull-status=failed');
		} else {
			return redirect(303, '/?git-pull-status=success');
		}
	}

	return next();
};
