import { Pool } from 'pg';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import dotenv from 'dotenv';
import chalk from 'chalk';
import crypto from 'crypto';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

dotenv.config();

const {
	DATABASE_USER,
	DATABASE_PASSWORD,
	DATABASE_NAME,
	DATABASE_HOST = 'localhost',
	DATABASE_PORT = '5432'
} = process.env;

if (!DATABASE_USER || !DATABASE_PASSWORD || !DATABASE_NAME) {
	console.error(
		chalk.red(chalk.bold('ERR!')),
		'Missing required environment variables. Please check your .env file'
	);
	console.error(
		chalk.yellow('Required variables:'),
		chalk.cyan('DATABASE_USER, DATABASE_PASSWORD, DATABASE_NAME')
	);
	process.exit(1);
}

const pool = new Pool({
	user: DATABASE_USER,
	password: DATABASE_PASSWORD,
	host: DATABASE_HOST,
	port: DATABASE_PORT,
	database: DATABASE_NAME
});

async function executeSchemaFile() {
	const schemaPath = path.join(__dirname, '..', 'resources', 'schema.sql');

	if (!fs.existsSync(schemaPath)) {
		console.error(
			chalk.red(chalk.bold('ERR!')),
			'Schema file not found at:',
			chalk.yellow(schemaPath)
		);
		process.exit(1);
	}

	try {
		process.stdout.write(`${chalk.blue(chalk.bold('RUN!'))} Reading schema file...\r`);
		const schemaContent = fs.readFileSync(schemaPath, 'utf8');
		console.log(`${chalk.green(chalk.bold('OK!'))} Schema file read successfully`);

		const createTableRegex = /CREATE\s+TABLE\s+(?:IF\s+NOT\s+EXISTS\s+)?([^\s(]+)/gi;
		const tables = [...schemaContent.matchAll(createTableRegex)].map((match) => match[1]);

		process.stdout.write(`${chalk.blue(chalk.bold('RUN!'))} Dropping existing tables...\r`);

		// Drop tables in reverse order to handle dependencies
		for (const table of tables.reverse()) {
			try {
				await pool.query(`DROP TABLE IF EXISTS ${table} CASCADE`);
			} catch (dropError) {
				// Ignore errors if tables don't exist
				if (!dropError.message.includes('does not exist')) {
					throw dropError;
				}
			}
		}
		console.log(`${chalk.green(chalk.bold('OK!'))} All tables dropped successfully`);

		process.stdout.write(`${chalk.blue(chalk.bold('RUN!'))} Creating tables...\r`);

		// Split the schema file into individual statements
		const statements = schemaContent
			.split(';')
			.map((stmt) => stmt.trim())
			.filter((stmt) => stmt.length > 0);

		// Execute each statement in a transaction
		const client = await pool.connect();
		try {
			await client.query('BEGIN');

			for (const statement of statements) {
				await client.query(statement);
			}

			await client.query('COMMIT');

			console.log(`${chalk.green(chalk.bold('OK!'))} Tables created successfully`);
			console.log(`${chalk.green(chalk.bold('\nOK!'))} Successfully created the following tables:`);
			tables.forEach((table) => console.log(chalk.cyan(`  └─ ${table}`)));

			console.log(`${chalk.green(chalk.bold('\nDONE!'))} Database setup completed successfully!`);
		} catch (error) {
			await client.query('ROLLBACK');
			throw error;
		} finally {
			client.release();
		}
	} catch (error) {
		console.error(
			chalk.red(chalk.bold('\nERR!')),
			'Error executing schema file:',
			chalk.yellow(error.message)
		);
		process.exit(1);
	} finally {
		await pool.end();
	}
}

async function main() {
	try {
		// Test the connection
		process.stdout.write(`${chalk.blue(chalk.bold('RUN!'))} Testing database connection...\r`);
		const client = await pool.connect();
		client.release();
		console.log(`${chalk.green(chalk.bold('OK!'))} Database connection successful`);

		await executeSchemaFile();

		// check if the env has `DATABASE_SECRET`, if not, generate a random one
		if (!process.env.DATABASE_SECRET) {
			process.env.DATABASE_SECRET = crypto.randomBytes(32).toString('hex');
			// make sure to append it to the .env file
			fs.appendFileSync('.env', `DATABASE_SECRET=${process.env.DATABASE_SECRET}\n`);
			console.log(
				`${chalk.green(chalk.bold('OK!'))} Random DATABASE_SECRET generated successfully`
			);
		}
	} catch (error) {
		console.error(
			chalk.red(chalk.bold('ERR!')),
			'Database connection failed:',
			chalk.yellow(error.message)
		);
		process.exit(1);
	}
}

try {
	await main();
} catch (error) {
	console.error(
		chalk.red(chalk.bold('ERR!')),
		'An unexpected error occurred:',
		chalk.yellow(error.message)
	);
	process.exit(1);
}
