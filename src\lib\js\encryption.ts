import { building } from '$app/environment';
import crypto from 'crypto';
import { config } from 'dotenv';
config();

if (!building) {
	if (!process.env.DATABASE_SECRET) {
		throw new Error('DATABASE_SECRET environment variable is required for encryption');
	}
}

const SECRET = process.env.DATABASE_SECRET;
const ALGORITHM = 'aes-256-gcm';
const IV_LENGTH = 16;
const SALT_LENGTH = 64;
const TAG_LENGTH = 16;
const KEY_LENGTH = 32;

function deriveKey(salt: Buffer): Buffer {
	if (!SECRET) {
		throw new Error('DATABASE_SECRET environment variable is required for encryption');
	}
	return crypto.pbkdf2Sync(SECRET, salt, 100000, KEY_LENGTH, 'sha256');
}

export function encryptField(value: string | null | undefined, userId: string): string | null {
	if (value === null || value === undefined) {
		return null;
	}

	try {
		const salt = crypto.randomBytes(SALT_LENGTH);
		const iv = crypto.randomBytes(IV_LENGTH);
		const key = deriveKey(salt);
		const cipher = crypto.createCipheriv(ALGORITHM, key, iv);
		const encrypted = Buffer.concat([cipher.update(value, 'utf8'), cipher.final()]);
		const tag = cipher.getAuthTag();
		const result = Buffer.concat([salt, iv, tag, encrypted]);
		return result.toString('base64');
	} catch (error) {
		console.error('Error encrypting field:', error);
		return null;
	}
}

export function decryptField(
	encryptedValue: string | null | undefined,
	userId: string
): string | null {
	if (encryptedValue === null || encryptedValue === undefined) {
		return null;
	}

	try {
		const buffer = Buffer.from(encryptedValue, 'base64');
		const salt = buffer.subarray(0, SALT_LENGTH);
		const iv = buffer.subarray(SALT_LENGTH, SALT_LENGTH + IV_LENGTH);
		const tag = buffer.subarray(SALT_LENGTH + IV_LENGTH, SALT_LENGTH + IV_LENGTH + TAG_LENGTH);
		const encrypted = buffer.subarray(SALT_LENGTH + IV_LENGTH + TAG_LENGTH);
		const key = deriveKey(salt);
		const decipher = crypto.createDecipheriv(ALGORITHM, key, iv);
		decipher.setAuthTag(tag);
		const decrypted = Buffer.concat([decipher.update(encrypted), decipher.final()]);
		return decrypted.toString('utf8');
	} catch (error) {
		console.error('Error decrypting field:', error);
		return null;
	}
}

export function encryptUserData(
	data: Record<string, any>,
	userId: string
): Record<string, string | null> {
	const encryptedData: Record<string, string | null> = {};

	for (const [key, value] of Object.entries(data)) {
		if (typeof value === 'string' || value === null || value === undefined) {
			encryptedData[key] = encryptField(value, userId);
		}
	}

	return encryptedData;
}

export function decryptUserData(
	data: Record<string, string | null>,
	userId: string
): Record<string, string | null> {
	const decryptedData: Record<string, string | null> = {};

	for (const [key, value] of Object.entries(data)) {
		decryptedData[key] = decryptField(value, userId);
	}

	return decryptedData;
}
