<script lang="ts">
	import type { PageData } from './$types';
	import Navbar from '$lib/components/nav/Navbar.svelte';

	interface Props {
		data: PageData;
	}

	let { data } = $props();
</script>

<Navbar />

<div class="w-full px-10 pt-10">
	<div class="mb-8 flex items-center gap-4">
		<a
			href="/account/@me"
			class="flex items-center text-[var(--text-secondary)] transition-colors hover:text-[var(--text-secondary-hover)]"
			aria-label="Go back to account"
		>
			<span class="icon-[mingcute--arrow-left-line] h-7 w-7"></span>
		</a>
		<h1 class="text-2xl font-bold text-[var(--text-primary)]">Custom Software Development</h1>
	</div>
</div>
