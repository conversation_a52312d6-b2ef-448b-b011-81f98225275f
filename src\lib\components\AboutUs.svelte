<script lang="ts">
	import cityBg from '@assets/images/images/vector-city-temp.jpg';

	interface Props {
		title?: string;
		description?: string;
		buttonText?: string;
		buttonLink?: string;
	}

	export let title = 'About Us';
	export let description = `TexRepairs LLC is a software company that specializes in repairs and subcontracting for any workflow. Initiated in 2024, our mission is to provide our clients with the best possible service we can; whether it\'s our repair service, or contracting service, we do our best to ensure the highest quality work is given.`;
	export let buttonText = 'Learn more';
	export let buttonLink = '#';
</script>

<div class="relative overflow-hidden">
	<!-- Background -->
	<div class="from-[var(--btn-primary)]/5 absolute inset-0 bg-gradient-to-br to-transparent"></div>

	<div class="relative mx-auto max-w-6xl px-4 py-16">
		<div class="grid gap-12 lg:grid-cols-2 lg:items-center">
			<!-- Image Section -->
			<div class="relative">
				<div class="relative aspect-square overflow-hidden rounded-2xl">
					<img src={cityBg} alt="About Us" class="h-full w-full object-cover" />
					<div
						class="absolute inset-0 bg-gradient-to-br from-[var(--btn-primary)] to-blue-900 opacity-20"
					></div>
				</div>
				<!-- Decorative Elements -->
				<div
					class="absolute -bottom-6 -right-6 h-24 w-24 rounded-full bg-[var(--btn-primary)] opacity-10"
				></div>
			</div>

			<!-- Content Section -->
			<div class="flex flex-col gap-6">
				<div class="space-y-4">
					<h2 class="text-3xl font-bold text-[var(--text-primary)] md:text-4xl">{title}</h2>
					<p class="text-lg leading-relaxed text-[var(--text-secondary)]">{description}</p>
				</div>

				<!-- Features Grid -->
				<div class="grid gap-4 sm:grid-cols-2">
					<div class="flex items-center gap-3 rounded-lg bg-[var(--card-bg)] p-4">
						<div
							class="flex h-10 w-10 items-center justify-center rounded-full bg-[var(--btn-primary)] bg-opacity-10"
						>
							<svg
								class="h-5 w-5 text-[var(--btn-primary)]"
								fill="none"
								stroke="currentColor"
								viewBox="0 0 24 24"
							>
								<path
									stroke-linecap="round"
									stroke-linejoin="round"
									stroke-width="2"
									d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
								/>
							</svg>
						</div>
						<div>
							<h3 class="font-semibold text-[var(--text-primary)]">Quality Service</h3>
							<p class="text-sm text-[var(--text-secondary)]">Best-in-class solutions</p>
						</div>
					</div>

					<div class="flex items-center gap-3 rounded-lg bg-[var(--card-bg)] p-4">
						<div
							class="flex h-10 w-10 items-center justify-center rounded-full bg-[var(--btn-primary)] bg-opacity-10"
						>
							<svg
								class="h-5 w-5 text-[var(--btn-primary)]"
								fill="none"
								stroke="currentColor"
								viewBox="0 0 24 24"
							>
								<path
									stroke-linecap="round"
									stroke-linejoin="round"
									stroke-width="2"
									d="M13 10V3L4 14h7v7l9-11h-7z"
								/>
							</svg>
						</div>
						<div>
							<h3 class="font-semibold text-[var(--text-primary)]">Fast Delivery</h3>
							<p class="text-sm text-[var(--text-secondary)]">Quick turnaround time</p>
						</div>
					</div>
				</div>

				<a
					href={buttonLink}
					class="group mt-4 inline-flex items-center gap-2 rounded-lg bg-[var(--btn-primary)] px-6 py-3 text-white transition-all duration-300 hover:bg-[var(--btn-hover-primary)] hover:shadow-lg"
				>
					<span>{buttonText}</span>
					<svg
						class="h-4 w-4 transition-transform duration-300 group-hover:translate-x-1"
						fill="none"
						stroke="currentColor"
						viewBox="0 0 24 24"
					>
						<path
							stroke-linecap="round"
							stroke-linejoin="round"
							stroke-width="2"
							d="M9 5l7 7-7 7"
						/>
					</svg>
				</a>
			</div>
		</div>
	</div>
</div>

<style lang="scss">
	.about-us-button {
		display: inline-block;
		width: 210px;
		height: 46px;
		line-height: 46px;
		font-size: 16px;
		font-weight: bold;
		color: #ffffff;
		background: linear-gradient(to right, #0d2a4c, #1e62b2);
		background-size: 200% 100%;
		background-position: 100% 0;
		border-radius: 12px;
		text-decoration: none;
		text-align: center;
		box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.1);
		transition:
			200ms ease-in-out,
			background-position 500ms,
			transform 0.2s;

		&:hover {
			background-position: 0 0;
		}
	}
</style>
