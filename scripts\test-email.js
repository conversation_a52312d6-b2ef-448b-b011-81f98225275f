import dotenv from 'dotenv';
import { readFileSync } from 'fs';
import { join } from 'path';
import { fileURLToPath } from 'url';
import { dirname } from 'path';
import sgMail from '@sendgrid/mail';

// Load environment variables
dotenv.config();

// Get the directory name
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Initialize SendGrid with API key
sgMail.setApiKey(process.env.SENDGRID_API_KEY);

async function sendTestEmail() {
	try {
		// Read the HTML template
		const templatePath = join(__dirname, '..', 'resources', 'confirm-email.html');
		let htmlContent = readFileSync(templatePath, 'utf8');

		// Replace the SVG with a hosted image
		// Using a CDN-hosted version of the logo
		const logoUrl = 'https://texrepairs.com/icon-white.png'; // Replace this with your actual hosted logo URL
		htmlContent = htmlContent.replace(
			/<svg[^>]*>[\s\S]*?<\/svg>/,
			`<img src="${logoUrl}" alt="Texrepairs Logo" width="171" height="78" style="max-width: 100%; height: auto; display: block; margin: 0 auto;">`
		);

		// Replace placeholders with test values
		const testConfirmUrl = 'https://texrepairs.com/confirm-email?token=test-token-123';
		const testEmail = '<EMAIL>';

		htmlContent = htmlContent
			.replace(/{{confirmUrl}}/g, testConfirmUrl)
			.replace(/{{email}}/g, testEmail);

		// Prepare the email
		const msg = {
			to: testEmail,
			from: '<EMAIL>', // This should be a verified sender
			subject: 'Test Email - Confirm Your Email',
			html: htmlContent
		};

		// Send the email
		await sgMail.send(msg);
		console.log('Test email sent successfully!');
	} catch (error) {
		console.error('Error sending test email:', error);
		if (error.response) {
			console.error(error.response.body);
		}
	}
}

// Run the function
sendTestEmail();
