<h1 align="center">
  <a href="https://github.com/Texrepairs"><img src="https://i.imgur.com/zyFO86u.png" height="200" alt="Img"></a>
</h1>

## Development

We track issues using Jira, you can find the issues [here](https://texrepairs-sprints.atlassian.net/jira/software/projects/WEBDEV/boards/1).

> [!IMPORTANT]
> We use Figma for our design, you can find the design [here](https://www.figma.com/design/U9Uz1vFvOxOWrhdFGoVL6a/TexRepairsLLC?node-id=129-24&t=aU41EeEHSixUFW17-1) and you will need to have a Figma account to view it.

### Building

This will show you how to build the texrepairs website, first there are some prerequisites:

**Prerequisites:**

- You are running a `Linux` machine or have Node already installed
- You have `redis` installed.
- You have pnpm installed `npm i -g pnpm`

**Debugging:**

We recommend you enable our built in debugger when developing, you can do this by adding the following to your `.env` file:

```env
NODE_ENV=development
ENABLE_DEBUGGER=true
```

### Steps to development:

1. `git clone https://github.com/Texrepairs/Website.git`
2. `git checkout next`
3. `git submodule update --init -f --remote`
4. `pnpm i`
5. `pnpm run dev`

**I can't pull because local changes**

1. `git stash`
2. `git pull`
3. `git stash pop`
4. `git add folder/* folder2/*`
5. `git commit -m "message"`

## Building

We use `docker` to build the website into production, you can build and run the website locally by running the following commands:

1. `sudo ./scripts/build.sh` - (sudos great!)

### Installing Redis on windows

> [!WARNING]
> You MUST have WSL2 installed to run Redis on Windows

1. Open WSL2 (powershell and run `wsl`)
2. Run

   ```bash
   curl -fsSL https://packages.redis.io/gpg | sudo gpg --dearmor -o /usr/share/keyrings/redis-archive-keyring.gpg

   echo "deb [signed-by=/usr/share/keyrings/redis-archive-keyring.gpg] https://packages.redis.io/deb $(lsb_release -cs) main" | sudo tee /etc/apt/sources.list.d/redis.list

   sudo apt-get update
   sudo apt-get install redis -y
   sudo apt-get install screen -y
   ```

3. Run `redis-server` to start the server
   ```bash
   screen redis-server start -S "redis"
   ```

> [!TIP]
> You can detach from the screen by pressing `Ctrl + A + D`

## Finding Icons

We use [iconify](https://icon-sets.iconify.design/) for our icons. You should always use the `iconify` package for icons.
