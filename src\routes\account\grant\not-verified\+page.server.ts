import { fail } from '@sveltejs/kit';
import type { Actions } from './$types';
import { AuthService } from '@backend/auth';

export const actions = {
	resend: async ({ request, locals }) => {
		try {
			// Get the user's email from the session
			const email = locals.session?.user?.email;

			if (!email) {
				return fail(400, {
					success: false,
					message: 'No email address found. Please try logging in again.'
				});
			}

			// Attempt to resend verification email
			const result = await AuthService.resendVerificationEmail(email);

			if (!result) {
				return fail(500, {
					success: false,
					message: 'Failed to resend verification email. Please try again later.'
				});
			}

			return {
				success: true,
				message: 'Verification email has been resent. Please check your inbox.'
			};
		} catch (error) {
			console.error('Resend verification email error:', error);
			return fail(500, {
				success: false,
				message: 'An error occurred while resending the verification email. Please try again later.'
			});
		}
	}
} satisfies Actions;
