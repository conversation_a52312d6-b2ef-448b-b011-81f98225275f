import { logDebug, logError, logInfo, logModule } from './logger';

const PHASE_REGEX = /phaser-\[(\d+ms|\d+s)]/;
const PHASE_REGEX_OPTIONAL = /phaser(-\[(\d+ms|\d+s)])?/;

/**
 * Checks viewport visibility of elements and manages animation classes
 * based on data-animate attribute
 */
export function checkAllElements(): void {}

/**
 * Manages animation for elements with phaser classes
 */
export function animatePhasers(root: HTMLElement | null = null, threshold: number): void {
	const observerOptions = {
		root,
		threshold: threshold / 100
	};

	let attempts = new WeakMap<HTMLElement, [number, number]>();

	function checkSpamEntry(target: HTMLElement): boolean {
		if (attempts.has(target)) {
			let x = attempts.get(target) as [number, number];
			x[1] += 1;

			if (x[1] > 10 && Date.now() - x[0] < 200) {
				x[0] = Date.now();
				return true;
			}

			x[0] = Date.now();

			if (Date.now() - x[0] > 200) {
				attempts.delete(target);
			}
			return false;
		} else {
			attempts.set(target, [Date.now(), 1]);
			return false;
		}
	}

	const observerCallback: IntersectionObserverCallback = (entries, observer) => {
		entries.forEach((entry) => {
			if (checkSpamEntry(entry.target as HTMLElement)) {
				return;
			}

			if (entry.isIntersecting) {
				if (entry.target.getAttribute('data-phase-stage') === 'after') return;

				if (!PHASE_REGEX_OPTIONAL.test(entry.target.classList.toString())) {
					logModule(
						logError,
						'animate-js',
						`T-IN: Element using legacy phase css, please update to new phase css!`,
						entry.target
					);
				}

				const duration = (entry.target as HTMLElement).style.getPropertyValue('--phase-duration');
				const distance = (entry.target as HTMLElement).style.getPropertyValue('--phase-distance');

				const classNameToAdd = entry.target.getAttribute('data-in-view') as string;
				entry.target.classList.add(classNameToAdd);
				const noReset = entry.target.getAttribute('data-no-reset-view');
				const phaseOnce = entry.target.getAttribute('data-phase-once');

				entry.target.setAttribute('data-phase-stage', 'after');
				entry.target.classList.add('phase-in');
				if (noReset === '' || phaseOnce === '') {
					const classNameToRemove = entry.target.getAttribute('data-in-view');
					observer.unobserve(entry.target);
					return;
				}
			} else {
				if (!PHASE_REGEX_OPTIONAL.test(entry.target.classList.toString())) {
					logModule(
						logError,
						'animate-js',
						`T-OUT: Element using legacy phase css, please update to new phase css!`,
						entry.target
					);
				}
				const classNameToRemove = entry.target.getAttribute('data-in-view');
				if (entry.target.getAttribute('data-phase-stage') === 'before') return;
				entry.target.classList.remove(classNameToRemove as string);
				entry.target.classList.remove('phase-in');
				entry.target.setAttribute('data-phase-stage', 'before');
			}
		});
	};

	const observer = new IntersectionObserver(observerCallback, observerOptions);

	document.querySelectorAll('[class*="phaser-["],[class*="phaser"]').forEach((element) => {
		element.setAttribute('data-phase-stage', 'before');
		const match = element.className.match(/phaser-\[(\d+ms|\d+s)]/);
		if (match) {
			const duration = match[1];
			//@ts-ignore
			element.style.setProperty('--phase-duration', duration);
			logModule(logDebug, 'animate-js', `Setting phase duration for element`, element);
		}
		const match2 = element.className.match(/phaser-distance-\[(\d+px|\d+%|\d+pem|\d|rem)]/);
		if (match2) {
			const duration = match2[1];
			//@ts-ignore
			element.style.setProperty('--phase-distance', duration);
			logModule(logDebug, 'animate-js', `Setting phase distance for element`, element);
		}
		observer.observe(element);
	});

	window.onresize = () => {
		observer.disconnect();
		document.querySelectorAll('[class*="phaser-["],[class*="phaser"]').forEach((element) => {
			observer.observe(element);
		});
	};
}

/**
 * This function takes the old functionality of the phaser function and isolates an old behavior.
 * This behavior allows you to add/remove classes to elements when they are in the viewport.
 * @param root - The root element to observe.
 * @param threshold - How much in %, should this element be in the viewport before it triggers
 */
export function animateInViewport(root: HTMLElement | null = null, threshold: number): void {
	const observerOptions = {
		root, // relative to the viewport
		threshold: threshold / 100
	};

	const observerCallback = (entries: any, _: any) => {
		entries.forEach((entry: any) => {
			if (entry.isIntersecting) {
				const classNameToAdd = entry.target.getAttribute('data-in-view');
				entry.target.classList.add(classNameToAdd);
			} else {
				const classNameToRemove = entry.target.getAttribute('data-in-view');
				entry.target.classList.remove(classNameToRemove);
			}
		});
	};
	const observer = new IntersectionObserver(observerCallback, observerOptions);

	document.querySelectorAll('[data-in-view]').forEach((element) => {
		observer.observe(element);
	});

	window.onresize = () => {
		observer.disconnect();
		document.querySelectorAll('[data-in-view]').forEach((element) => {
			observer.observe(element);
		});
	};
}
