<script lang="ts">
	import Logo from '$lib/components/icon/Logo.svelte';
	import cityBg from '@assets/images/images/vector-city-temp.jpg';
	import { enhance } from '$app/forms';
	import type { ActionResult } from '@sveltejs/kit';

	export let form: {
		success?: boolean;
		message?: string;
	} = {};

	function handleSubmit() {
		return async ({ result, update }: { result: ActionResult; update: () => Promise<void> }) => {
			await update();
		};
	}
</script>

<div class="relative min-h-screen">
	<!-- Background image with overlay -->
	<div class="absolute inset-0 z-0">
		<img src={cityBg} alt="City background" class="h-full w-full object-cover blur-sm filter" />
		<div class="absolute inset-0 bg-[#0f172a] bg-opacity-80"></div>
	</div>

	<!-- Content -->
	<div
		class="relative z-10 flex min-h-screen flex-col items-center justify-center px-4 py-8 md:py-16"
	>
		<div
			class="w-full max-w-md overflow-hidden rounded-2xl bg-[#0a0e10d9] p-8 shadow-2xl backdrop-blur-sm"
		>
			<div class="mb-6 flex justify-center">
				<a href="/" data-sveltekit-reload>
					<Logo fill="#fff" />
				</a>
			</div>

			<div class="mb-6 text-center">
				<h2 class="text-2xl font-bold text-white">Email Verification Required</h2>
				<p class="mt-2 text-gray-400">
					Please check your email inbox for a verification link. You'll need to verify your email
					address before you can access your account.
				</p>
			</div>

			<div class="space-y-6">
				{#if form?.success}
					<div class="rounded-lg border border-green-600 bg-green-900/30 p-4 text-green-300">
						<div class="flex items-center gap-3">
							<span class="icon-[mingcute--check-line] h-5 w-5"></span>
							<p>{form.message}</p>
						</div>
					</div>

					<div class="rounded-lg border border-gray-600 bg-gray-800/30 p-4">
						<h3 class="mb-2 font-medium text-white">Didn't receive the email?</h3>
						<ul class="space-y-2 text-sm text-gray-400">
							<li class="flex items-center gap-2">
								<span class="icon-[mingcute--check-line] h-4 w-4 text-[var(--btn-primary)]"></span>
								Check your spam or junk folder
							</li>
							<li class="flex items-center gap-2">
								<span class="icon-[mingcute--check-line] h-4 w-4 text-[var(--btn-primary)]"></span>
								Make sure you entered the correct email address
							</li>
							<li class="flex items-center gap-2">
								<span class="icon-[mingcute--check-line] h-4 w-4 text-[var(--btn-primary)]"></span>
								Wait a few minutes and try again
							</li>
						</ul>
					</div>
				{:else if form?.message}
					<div class="rounded-lg border border-red-600 bg-red-900/30 p-4 text-red-300">
						<div class="flex items-center gap-3">
							<span class="icon-[mingcute--close-line] h-5 w-5"></span>
							<p>{form.message}</p>
						</div>
					</div>
				{:else}
					<div class="rounded-lg border border-gray-600 bg-gray-800/30 p-4 text-gray-300">
						<div class="flex items-center gap-3">
							<span class="icon-[mingcute--mail-line] h-8 w-8 text-[var(--btn-primary)]"></span>
							<p class="text-sm">
								We've sent a verification email to your registered email address.
							</p>
						</div>
					</div>
				{/if}

				<div class="space-y-4">
					<div class="flex flex-col gap-3">
						<form method="POST" action="?/resend" use:enhance={handleSubmit} class="w-full">
							<button
								type="submit"
								class="w-full rounded-lg bg-[var(--btn-primary)] px-4 py-2.5 font-medium text-white transition-colors duration-200 hover:bg-[var(--btn-hover-primary)] focus:outline-none focus:ring-2 focus:ring-[var(--btn-primary)] focus:ring-offset-2"
							>
								Resend Verification Email
							</button>
						</form>

						<a
							href="/logout"
							class="w-full rounded-lg border border-gray-600 bg-transparent px-4 py-2.5 text-center font-medium text-white transition-colors duration-200 hover:bg-gray-800/50 focus:outline-none focus:ring-2 focus:ring-gray-600 focus:ring-offset-2"
						>
							Logout
						</a>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>
