<script lang="ts">
	import Logo from '$lib/components/icon/Logo.svelte';
	import * as Form from '$lib/components/forms';
	import { enhance } from '$app/forms';
	import type { ActionResult } from '@sveltejs/kit';
	import cityBg from '@assets/images/images/vector-city-temp.jpg';

	export let form: {
		success?: boolean;
		errors?: Record<string, string>;
		formData?: Record<string, string>;
	} = {};

	const getValue = (field: string) => form?.formData?.[field] || '';
	let submitting = false;

	function handleSubmit() {
		return async ({ result, update }: { result: ActionResult; update: () => Promise<void> }) => {
			submitting = true;
			try {
				await update();

				// Handle redirect if login was successful
				if (result.type === 'success' && result.data?.redirect) {
					window.location.href = result.data.redirect;
				}
			} finally {
				submitting = false;
			}
		};
	}
</script>

<div class="relative min-h-screen">
	<!-- Background image with overlay -->
	<div class="absolute inset-0 z-0">
		<img src={cityBg} alt="City background" class="h-full w-full object-cover blur-sm filter" />
		<div class="absolute inset-0 bg-[#0f172a] bg-opacity-80"></div>
	</div>

	<!-- Content -->
	<div
		class="relative z-10 flex min-h-screen flex-col items-center justify-center px-4 py-8 md:py-16"
	>
		<div
			class="w-full max-w-4xl overflow-hidden rounded-2xl bg-[#0a0e10d9] shadow-2xl backdrop-blur-sm"
		>
			<!-- Two-column layout for larger screens -->
			<div class="flex flex-col md:flex-row-reverse">
				<!-- Right column (accent/brand section) -->
				<div
					class="flex flex-col justify-between bg-gradient-to-bl from-[var(--btn-primary)] to-blue-900 p-6 md:w-2/5 md:p-8"
				>
					<div>
						<div class="mb-4 flex justify-center md:mb-8 md:justify-start">
							<a href="/" data-sveltekit-reload>
								<Logo fill="#fff" />
							</a>
						</div>
						<h2
							class="mb-3 text-center text-2xl font-bold text-white md:mb-4 md:text-left md:text-3xl"
						>
							Welcome back!
						</h2>
						<p class="mb-4 text-center text-gray-200 md:mb-8 md:text-left">
							Sign in to access your account and manage your repair services.
						</p>
					</div>

					<div class="hidden md:block">
						<ul class="space-y-3 text-gray-200">
							<li class="flex items-center">
								<svg class="mr-2 h-5 w-5 text-green-400" fill="currentColor" viewBox="0 0 20 20">
									<path
										fill-rule="evenodd"
										d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
										clip-rule="evenodd"
									/>
								</svg>
								<span>Quick access to your service requests</span>
							</li>
							<li class="flex items-center">
								<svg class="mr-2 h-5 w-5 text-green-400" fill="currentColor" viewBox="0 0 20 20">
									<path
										fill-rule="evenodd"
										d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
										clip-rule="evenodd"
									/>
								</svg>
								<span>View service status updates</span>
							</li>
							<li class="flex items-center">
								<svg class="mr-2 h-5 w-5 text-green-400" fill="currentColor" viewBox="0 0 20 20">
									<path
										fill-rule="evenodd"
										d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
										clip-rule="evenodd"
									/>
								</svg>
								<span>Manage your preferences</span>
							</li>
						</ul>
					</div>
				</div>

				<!-- Left column (form) -->
				<div class="p-6 md:w-3/5 md:p-8">
					<div class="mb-4 md:mb-6">
						<h3 class="text-xl font-bold text-white md:text-2xl">Sign in to your account</h3>
						<p class="mt-1 text-sm text-gray-400">Enter your credentials to continue</p>
					</div>

					{#if form?.errors?.form}
						<div
							class="mb-4 rounded-lg border border-red-500/50 bg-red-500/10 p-4 text-sm text-red-400"
						>
							{form.errors.form}
						</div>
					{/if}

					<form
						method="POST"
						action="?/login"
						use:enhance={handleSubmit}
						class="space-y-4 md:space-y-5"
					>
						<Form.TextField
							label="Username or Email"
							type="username"
							name="email"
							value={getValue('email')}
							placeholder="johndoe"
							error={form?.errors?.email}
						/>

						<Form.TextField
							label="Password"
							type="password"
							name="password"
							placeholder="••••••••"
							error={form?.errors?.password}
						/>

						<div class="flex items-center justify-between">
							<Form.Checkbox id="remember" name="remember">
								{#snippet label()}
									Remember me
								{/snippet}
							</Form.Checkbox>
							<a
								href="/account/fpwd"
								class="text-sm font-medium text-[var(--text-link)] hover:text-[var(--text-link-hover)]"
							>
								Forgot password?
							</a>
						</div>

						<div class="mt-6">
							<button
								type="submit"
								disabled={submitting}
								class="w-full rounded-lg bg-[var(--btn-primary)] px-4 py-2.5 font-medium text-white transition-colors duration-200 hover:bg-[var(--btn-hover-primary)] focus:outline-none focus:ring-2 focus:ring-[var(--btn-primary)] focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-70"
							>
								{#if submitting}
									<div class="flex items-center justify-center">
										<svg
											class="-ml-1 mr-3 h-5 w-5 animate-spin text-white"
											xmlns="http://www.w3.org/2000/svg"
											fill="none"
											viewBox="0 0 24 24"
										>
											<circle
												class="opacity-25"
												cx="12"
												cy="12"
												r="10"
												stroke="currentColor"
												stroke-width="4"
											></circle>
											<path
												class="opacity-75"
												fill="currentColor"
												d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
											></path>
										</svg>
										Signing in...
									</div>
								{:else}
									Sign in
								{/if}
							</button>
						</div>

						<div class="mt-6 text-center text-sm">
							<p class="text-gray-400">
								Don't have an account?
								<a
									href="/register"
									class="ml-1 font-medium text-[var(--text-link)] hover:text-[var(--text-link-hover)]"
								>
									Create one
								</a>
							</p>
						</div>
					</form>
				</div>
			</div>
		</div>
	</div>
</div>

<style lang="scss">
	.tt {
		filter: blur(10px);
		top: 0;
		left: 0;
		z-index: -1;
		object-fit: cover;
		object-position: center;
		overflow: hidden;
		height: 100%;
		width: 100%;
	}
</style>
