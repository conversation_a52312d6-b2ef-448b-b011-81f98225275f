import { type Request<PERSON><PERSON><PERSON> } from '@sveltejs/kit';
import { AuthService } from '@backend/auth';

export const POST: RequestHandler = async ({ request, locals }) => {
	const data = await request.json();
	const confirmDelete = data.confirmDelete === true;
	const confirmDisable = data.confirmDisable === true;

	if (!confirmDelete && !confirmDisable) {
		return new Response(
			JSON.stringify({
				success: false,
				errors: { form: 'You must confirm the deletion of your account' }
			}),
			{ status: 400 }
		);
	}

	if (confirmDelete) {
		console.log(locals.session?.user?.id);

		try {
			const result = await AuthService.deleteAccount(locals.session?.user?.id ?? '');

			if (!result) {
				return new Response(
					JSON.stringify({
						success: false,
						errors: { form: 'Failed to delete account. Please try again later.' }
					}),
					{ status: 500 }
				);
			}
		} catch (error) {
			console.error(error);
			return new Response(
				JSON.stringify({
					success: false,
					errors: {
						form:
							error instanceof Error
								? error.message
								: 'Failed to delete account. Please try again later.'
					}
				}),
				{ status: 500 }
			);
		}
	}

	if (confirmDisable) {
		const result = await AuthService.disableAccount(locals.session?.user?.id ?? '');

		if (!result) {
			return new Response(
				JSON.stringify({
					success: false,
					errors: { form: 'Failed to disable account. Please try again later.' }
				}),
				{ status: 500 }
			);
		}
	}

	return new Response(JSON.stringify({ success: true }), { status: 200 });
};
