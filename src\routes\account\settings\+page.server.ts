import { fail } from '@sveltejs/kit';
import type { Actions } from './$types';
import { AuthService } from '@backend/auth';

export const actions: Actions = {
	updateProfile: async ({ request, locals }) => {
		try {
			const formData = await request.formData();
			const firstName = formData.get('firstName')?.toString();
			const lastName = formData.get('lastName')?.toString();
			const phone = formData.get('phone')?.toString();

			if (!firstName || !lastName) {
				return fail(400, {
					success: false,
					errors: {
						form: 'First name and last name are required'
					}
				});
			}

			// const result = await AuthService.updateProfile(locals.session?.user?.id ?? '', {
			// 	firstName,
			// 	lastName,
			// 	phone
			// });

			// if (!result) {
			// 	return fail(500, {
			// 		success: false,
			// 		errors: {
			// 			form: 'Failed to update profile. Please try again later.'
			// 		}
			// 	});
			// }

			return { success: true };
		} catch (error) {
			console.error('Profile update error:', error);
			return fail(500, {
				success: false,
				errors: {
					form: 'An error occurred while updating your profile. Please try again later.'
				}
			});
		}
	},

	updatePassword: async ({ request, locals }) => {
		try {
			const formData = await request.formData();
			const currentPassword = formData.get('currentPassword')?.toString();
			const newPassword = formData.get('newPassword')?.toString();
			const confirmPassword = formData.get('confirmPassword')?.toString();

			if (!currentPassword || !newPassword || !confirmPassword) {
				return fail(400, {
					success: false,
					errors: {
						form: 'All password fields are required'
					}
				});
			}

			if (newPassword !== confirmPassword) {
				return fail(400, {
					success: false,
					errors: {
						form: 'New passwords do not match'
					}
				});
			}

			const result = await AuthService.updatePassword(
				locals.session?.user?.id ?? '',
				currentPassword,
				newPassword
			);

			if (!result) {
				return fail(500, {
					success: false,
					errors: {
						form: 'Failed to update password. Please try again later.'
					}
				});
			}

			return { success: true };
		} catch (error) {
			console.error('Password update error:', error);
			return fail(500, {
				success: false,
				errors: {
					form: 'An error occurred while updating your password. Please try again later.'
				}
			});
		}
	},

	updateNotifications: async ({ request, locals }) => {
		try {
			const formData = await request.formData();
			const emailNotifications = formData.get('emailNotifications') === 'true';
			const serviceUpdates = formData.get('serviceUpdates') === 'true';

			const result = await AuthService.updateNotificationPreferences(locals.session?.user?.id, {
				emailNotifications,
				serviceUpdates
			});

			if (!result) {
				return fail(500, {
					success: false,
					errors: {
						form: 'Failed to update notification preferences. Please try again later.'
					}
				});
			}

			return { success: true };
		} catch (error) {
			console.error('Notification preferences update error:', error);
			return fail(500, {
				success: false,
				errors: {
					form: 'An error occurred while updating your notification preferences. Please try again later.'
				}
			});
		}
	},

	addPaymentMethod: async ({ request, locals }) => {
		try {
			const formData = await request.formData();
			const paymentMethod = formData.get('paymentMethod')?.toString();

			if (!paymentMethod) {
				return fail(400, {
					success: false,
					errors: {
						form: 'Payment method is required'
					}
				});
			}

			const result = await AuthService.addPaymentMethod(locals.session?.user?.id, paymentMethod);

			if (!result) {
				return fail(500, {
					success: false,
					errors: {
						form: 'Failed to add payment method. Please try again later.'
					}
				});
			}

			return { success: true };
		} catch (error) {
			console.error('Payment method update error:', error);
			return fail(500, {
				success: false,
				errors: {
					form: 'An error occurred while adding your payment method. Please try again later.'
				}
			});
		}
	},

	changeEmail: async ({ request, locals }) => {
		try {
			const data = await request.json();
			const newEmail = data.newEmail?.toString();

			if (!newEmail) {
				return fail(400, {
					success: false,
					errors: {
						form: 'New email address is required'
					}
				});
			}

			// TODO: Add email validation
			if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(newEmail)) {
				return fail(400, {
					success: false,
					errors: {
						form: 'Invalid email address format'
					}
				});
			}

			// TODO: Add check if email is already in use
			// const emailExists = await AuthService.checkEmailExists(newEmail);
			// if (emailExists) {
			// 	return fail(400, {
			// 		success: false,
			// 		errors: {
			// 			form: 'This email address is already in use'
			// 		}
			// 	});
			// }

			// TODO: Implement email change in AuthService
			// const result = await AuthService.changeEmail(locals.session?.user?.id ?? '', newEmail);
			// if (!result) {
			// 	return fail(500, {
			// 		success: false,
			// 		errors: {
			// 			form: 'Failed to change email. Please try again later.'
			// 		}
			// 	});
			// }

			// For now, just return success
			return { success: true };
		} catch (error) {
			console.error('Email change error:', error);
			return fail(500, {
				success: false,
				errors: {
					form: 'An error occurred while changing your email. Please try again later.'
				}
			});
		}
	}
};
