.cb-red-dark {
	--bg: #181b26;
	--bg-secondary: #161720;
	--nav-bg: #1d1d25;
	--text-primary: #ffffff;
	--text-secondary: #ffffff;
	--accent: #1d1d25;

	/* Debug */
	--bg-debug-header: #6e6e6e;
	--bg-debug: #786a6f;
	--text-debug-value: #68a2ff;
	--text-debug-key: #ffffff;
	--text-debug-primary: #ffffff;
	--text-debug-secondary: #a3a3a3;
	--bg-debug-code: #00000091;

	/* Button bg */
	--btn-primary: #152e4d;
	--btn-hover-primary: #003e6c;
	--btn-secondary: #3c373c;
	--btn-hover-secondary: #4c4449;
	--btn-tertiary: #3c373c;
	--btn-hover-tertiary: #4c4449;
	--btn-error: #906c24;
	--btn-hover-error: #b89061;
	--btn-success: #eab360;
	--btn-hover-success: #e0b386;
	--btn-warning: #e7ac00;
	--btn-hover-warning: #f6b834;
	--btn-info: #e7e1ff;
	--btn-hover-info: #e9e2ff;

	/* Cards */
	--card-container-bg: #161720;
	--card-bg: #1d1d25;
	--card-button-bg: #152e4d;
	--card-text-primary: #ffebf1;
	--card-text-secondary: #e5cddc;

	/* Miscelaneous */
	--tr-blue: #b4b0d7;
	--tr-blue-alt: #8091ca;

	--footer-bg: #181b26;

	--hero-bg: #17171e;
	--hero-text-color: #ffffff;

	--hero-title-color: #868cb9;

	--hero-button-text: #ffffff;
	--hero-button-gradient-from: #7885b5;
	--hero-button-gradient-to: #868cb9;
	--hero-button-gradient-hover-from: #0087ea;
	--hero-button-gradient-hover-to: #7885b5;

	--hero-spotlight-bg: #282d3f;

	/** Other generic colors */
	--teal-byte: #262836;
	--text-primary: #ffffff;
	--text-secondary: #b19ca3;
	/** gradient */
	--gradient-start: #ffffff;
	--gradient-end: #bcc1fa;

	/* Error Page */
	--error-bg-gradient-from: #17171e;
	--error-bg-gradient-to: #181b26;
	--error-text-primary: #ffffff;
	--error-text-secondary: #868cb9;
	--error-accent: #8091ca;
	--error-glow: #7885b5;
	--error-particle: #bcc1fa;
	--error-button-bg: rgba(128, 145, 202, 0.2);
	--error-button-hover: rgba(128, 145, 202, 0.3);
	--error-button-text: #ffffff;
	--error-code-color: #68a2ff;
}

.cb-red-light {
	--bg: #cecdff;
	--bg-secondary: #fff4f8;
	--nav-bg: #9da8e0;
	--text-primary: #1b1819;
	--text-secondary: #4c4446;
	--accent: #0090fc;

	/* Debug */
	--bg-debug: #e4c9d2;
	--text-debug: #2b77cc;

	/* Button bg */
	--btn-primary: #2b77cc;
	--btn-hover-primary: #1c5ea2;
	--btn-secondary: #9da5da;
	--btn-hover-secondary: #889bd8;
	--btn-error: #906c24;
	--btn-hover-error: #b89061;
	--btn-success: #ac8652;
	--btn-hover-success: #99774a;
	--btn-warning: #e0a600;
	--btn-hover-warning: #c99500;
	--btn-info: #6e8cd0;
	--btn-hover-info: #617bb6;

	/* Cards */
	--card-container-bg: #a3b8ff;
	--card-bg: #f4e3ff;
	--card-button-bg: #2b77cc;
	--card-text-primary: #273046;
	--card-text-secondary: #584e51;

	/* Miscellaneous */
	--tr-blue: #909cd1;
	--tr-blue-alt: #7081b5;

	--footer-bg: #f8dbe4;

	--hero-bg: #fff1f6;
	--hero-text-color: #1b1819;

	--hero-title-color: #2b77cc;

	--hero-button-text: #ffffff;
	--hero-button-gradient-from: #7081b5;
	--hero-button-gradient-to: #2b77cc;
	--hero-button-gradient-hover-from: #1c5ea2;
	--hero-button-gradient-hover-to: #7081b5;

	--hero-spotlight-bg: #e3eaf0;

	/** Other generic colors */
	--teal-byte: #f8e6ff;
	--text-primary: #273046;
	--text-secondary: #2d485a;

	/** gradient */
	--gradient-start: #182c48;
	--gradient-end: #77a7ff;

	/* Error Page */
	--error-bg-gradient-from: #fff1f6;
	--error-bg-gradient-to: #f8dbe4;
	--error-text-primary: #273046;
	--error-text-secondary: #2b77cc;
	--error-accent: #7081b5;
	--error-glow: #1c5ea2;
	--error-particle: #77a7ff;
	--error-button-bg: rgba(112, 129, 181, 0.2);
	--error-button-hover: rgba(112, 129, 181, 0.3);
	--error-button-text: #273046;
	--error-code-color: #2b77cc;
}
