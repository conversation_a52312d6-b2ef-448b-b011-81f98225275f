<script lang="ts">
	import { fade } from 'svelte/transition';
</script>

<div class="space-y-16 py-8" in:fade>
	<!-- Hero Section -->
	<section
		class="relative overflow-hidden rounded-2xl bg-gradient-to-r from-indigo-600 to-violet-600 p-8 md:p-12"
	>
		<div class="relative z-10 max-w-3xl">
			<h1 class="mb-4 text-4xl font-bold md:text-5xl">Modern Web Development</h1>
			<p class="text-xl text-violet-100">
				Creating stunning, responsive websites through collaborative design and cutting-edge
				technology.
			</p>
		</div>
		<div class="absolute right-0 top-0 -z-10 h-full w-full opacity-20">
			<svg
				xmlns="http://www.w3.org/2000/svg"
				fill="none"
				viewBox="0 0 24 24"
				stroke-width="1.5"
				stroke="currentColor"
				class="h-full w-full"
			>
				<path
					stroke-linecap="round"
					stroke-linejoin="round"
					d="M12 21a9.004 9.004 0 008.716-6.747M12 21a9.004 9.004 0 01-8.716-6.747M12 21c2.485 0 4.5-4.03 4.5-9S14.485 3 12 3m0 18c-2.485 0-4.5-4.03-4.5-9S9.515 3 12 3m0 0a8.997 8.997 0 017.843 4.582M12 3a8.997 8.997 0 00-7.843 4.582m15.686 0A11.953 11.953 0 0112 10.5c-2.998 0-5.74-1.1-7.843-2.918m15.686 0A8.959 8.959 0 0121 12c0 .778-.099 1.533-.284 2.253m0 0A17.919 17.919 0 0112 16.5c-3.162 0-6.133-.815-8.716-2.247m0 0A9.015 9.015 0 013 12c0-1.605.42-3.113 1.157-4.418"
				/>
			</svg>
		</div>
	</section>

	<!-- Features Grid -->
	<section
		class="relative rounded-2xl bg-gradient-to-br from-indigo-600/10 via-violet-600/5 to-transparent p-8"
	>
		<div class="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
			<div
				class="rounded-xl bg-gradient-to-br from-indigo-600/20 via-violet-600/10 to-transparent p-6 backdrop-blur-sm transition-all hover:from-indigo-600/30 hover:via-violet-600/20 hover:to-transparent hover:shadow-lg hover:shadow-indigo-500/10"
			>
				<svg
					xmlns="http://www.w3.org/2000/svg"
					fill="none"
					viewBox="0 0 24 24"
					stroke-width="1.5"
					stroke="currentColor"
					class="mb-4 h-12 w-12 text-[var(--accent-primary)]"
				>
					<path
						stroke-linecap="round"
						stroke-linejoin="round"
						d="M9 17.25v1.007a3 3 0 01-.879 2.122L7.5 21h9l-.621-.621A3 3 0 0115 18.257V17.25m6-12V15a2.25 2.25 0 01-2.25 2.25H5.25A2.25 2.25 0 013 15V5.25m18 0A2.25 2.25 0 0018.75 3H5.25A2.25 2.25 0 003 5.25m18 0V12a2.25 2.25 0 01-2.25 2.25H5.25A2.25 2.25 0 013 12V5.25"
					/>
				</svg>
				<h3 class="mb-2 text-xl font-semibold">Responsive Design</h3>
				<p class="text-gray-300">
					Beautiful websites that work flawlessly across all devices and screen sizes.
				</p>
			</div>
			<div
				class="rounded-xl bg-gradient-to-br from-violet-600/20 via-purple-600/10 to-transparent p-6 backdrop-blur-sm transition-all hover:from-violet-600/30 hover:via-purple-600/20 hover:to-transparent hover:shadow-lg hover:shadow-violet-500/10"
			>
				<svg
					xmlns="http://www.w3.org/2000/svg"
					fill="none"
					viewBox="0 0 24 24"
					stroke-width="1.5"
					stroke="currentColor"
					class="mb-4 h-12 w-12 text-[var(--accent-primary)]"
				>
					<path
						stroke-linecap="round"
						stroke-linejoin="round"
						d="M3.75 13.5l10.5-11.25L12 10.5h8.25L9.75 21.75 12 13.5H3.75z"
					/>
				</svg>
				<h3 class="mb-2 text-xl font-semibold">Performance Optimized</h3>
				<p class="text-gray-300">Lightning-fast loading times and smooth user experiences.</p>
			</div>
			<div
				class="rounded-xl bg-gradient-to-br from-purple-600/20 via-fuchsia-600/10 to-transparent p-6 backdrop-blur-sm transition-all hover:from-purple-600/30 hover:via-fuchsia-600/20 hover:to-transparent hover:shadow-lg hover:shadow-purple-500/10"
			>
				<svg
					xmlns="http://www.w3.org/2000/svg"
					fill="none"
					viewBox="0 0 24 24"
					stroke-width="1.5"
					stroke="currentColor"
					class="mb-4 h-12 w-12 text-[var(--accent-primary)]"
				>
					<path
						stroke-linecap="round"
						stroke-linejoin="round"
						d="M9 12.75L11.25 15 15 9.75m-3-7.036A11.959 11.959 0 013.598 6 11.99 11.99 0 003 9.749c0 5.592 3.824 10.29 9 11.623 5.176-1.332 9-6.03 9-11.622 0-1.31-.21-2.571-.598-3.751h-.152c-3.196 0-6.1-1.248-8.25-3.285z"
					/>
				</svg>
				<h3 class="mb-2 text-xl font-semibold">Secure & Reliable</h3>
				<p class="text-gray-300">Built with security best practices and modern web standards.</p>
			</div>
		</div>
	</section>

	<!-- Detailed Service Description -->
	<section
		class="relative rounded-2xl bg-gradient-to-br from-indigo-600/5 via-violet-600/5 to-transparent p-8"
	>
		<div class="relative">
			<h2 class="mb-12 text-center text-3xl font-bold">
				<span class="relative inline-block">
					Our Web Development Services
					<span
						class="absolute -bottom-2 left-0 h-1 w-full bg-gradient-to-r from-[var(--accent-primary)] to-[var(--accent-secondary)]"
					></span>
				</span>
			</h2>

			<div class="grid gap-12 md:grid-cols-2">
				<!-- Frontend Development -->
				<div
					class="group relative overflow-hidden rounded-xl bg-gradient-to-br from-indigo-600/10 to-transparent p-8 transition-all hover:shadow-lg hover:shadow-indigo-500/10"
				>
					<div class="bg-[var(--card-container-bg)]/80 absolute inset-0 backdrop-blur-sm"></div>
					<div class="relative">
						<div class="mb-6 flex items-center gap-4">
							<div
								class="flex h-12 w-12 items-center justify-center rounded-lg bg-gradient-to-br from-indigo-500 to-violet-500"
							>
								<svg
									xmlns="http://www.w3.org/2000/svg"
									fill="none"
									viewBox="0 0 24 24"
									stroke-width="1.5"
									stroke="white"
									class="h-6 w-6"
								>
									<path
										stroke-linecap="round"
										stroke-linejoin="round"
										d="M17.25 6.75L22.5 12l-5.25 5.25m-10.5 0L1.5 12l5.25-5.25m7.5-3l-4.5 16.5"
									/>
								</svg>
							</div>
							<h3 class="text-2xl font-semibold text-[var(--accent-primary)]">
								Frontend Development
							</h3>
						</div>
						<p class="mb-6 text-gray-300">
							We create modern, responsive user interfaces using the latest web technologies. Our
							frontend solutions are designed to be fast, accessible, and engaging.
						</p>
						<ul class="space-y-3">
							{#each [{ icon: 'M17.25 6.75L22.5 12l-5.25 5.25m-10.5 0L1.5 12l5.25-5.25m7.5-3l-4.5 16.5', text: 'Modern JavaScript frameworks' }, { icon: 'M9.53 16.122a3 3 0 00-5.78 1.128 2.25 2.25 0 01-2.4 2.245 4.5 4.5 0 008.4-2.245c0-.399-.078-.78-.22-1.128zm0 0a15.998 15.998 0 003.388-1.62m-5.043-.025a15.994 15.994 0 011.622-3.395m3.42 3.42a15.995 15.995 0 004.764-4.648l3.876-5.814a1.151 1.151 0 00-1.597-1.597L14.146 6.32a15.996 15.996 0 00-4.649 4.763m3.42 3.42a6.776 6.776 0 00-3.42-3.42', text: 'Responsive design with Tailwind CSS' }, { icon: 'M12 6v6h4.5m4.5 0a9 9 0 11-18 0 9 9 0 0118 0z', text: 'Performance optimization' }, { icon: 'M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z', text: 'Accessibility compliance' }] as item}
								<li class="flex items-center gap-3 text-gray-300">
									<svg
										xmlns="http://www.w3.org/2000/svg"
										fill="none"
										viewBox="0 0 24 24"
										stroke-width="1.5"
										stroke="currentColor"
										class="h-5 w-5 text-[var(--accent-primary)]"
									>
										<path stroke-linecap="round" stroke-linejoin="round" d={item.icon} />
									</svg>
									{item.text}
								</li>
							{/each}
						</ul>
					</div>
				</div>

				<!-- Backend Development -->
				<div
					class="group relative overflow-hidden rounded-xl bg-gradient-to-br from-violet-600/10 to-transparent p-8 transition-all hover:shadow-lg hover:shadow-violet-500/10"
				>
					<div class="bg-[var(--card-container-bg)]/80 absolute inset-0 backdrop-blur-sm"></div>
					<div class="relative">
						<div class="mb-6 flex items-center gap-4">
							<div
								class="flex h-12 w-12 items-center justify-center rounded-lg bg-gradient-to-br from-violet-500 to-purple-500"
							>
								<svg
									xmlns="http://www.w3.org/2000/svg"
									fill="none"
									viewBox="0 0 24 24"
									stroke-width="1.5"
									stroke="white"
									class="h-6 w-6"
								>
									<path
										stroke-linecap="round"
										stroke-linejoin="round"
										d="M5.25 14.25h13.5m-13.5 0a3 3 0 01-3-3m3 3a3 3 0 100 6h13.5a3 3 0 100-6m-16.5-3a3 3 0 013-3h13.5a3 3 0 013 3m-19.5 0a4.5 4.5 0 01.9-2.7L5.737 5.1a3.375 3.375 0 012.7-1.35h7.126c1.062 0 2.062.5 2.7 1.35l8.163 10.7a4.5 4.5 0 01.9 2.7m0 0a3 3 0 01-3 3m0 3h.008v.008h-.008v-.008zm0-6h.008v.008h-.008v-.008zm-3-6h.008v.008h-.008v-.008z"
									/>
								</svg>
							</div>
							<h3 class="text-2xl font-semibold text-[var(--accent-primary)]">
								Backend Development
							</h3>
						</div>
						<p class="mb-6 text-gray-300">
							Robust backend solutions that power your web applications with scalability, security,
							and performance in mind.
						</p>
						<ul class="space-y-3">
							{#each [{ icon: 'M5.25 14.25h13.5m-13.5 0a3 3 0 01-3-3m3 3a3 3 0 100 6h13.5a3 3 0 100-6m-16.5-3a3 3 0 013-3h13.5a3 3 0 013 3m-19.5 0a4.5 4.5 0 01.9-2.7L5.737 5.1a3.375 3.375 0 012.7-1.35h7.126c1.062 0 2.062.5 2.7 1.35l8.163 10.7a4.5 4.5 0 01.9 2.7m0 0a3 3 0 01-3 3m0 3h.008v.008h-.008v-.008zm0-6h.008v.008h-.008v-.008zm-3-6h.008v.008h-.008v-.008z', text: 'Node.js and Express.js' }, { icon: 'M20.25 6.375c0 2.278-3.694 4.125-8.25 4.125S3.75 8.653 3.75 6.375m16.5 0c0-2.278-3.694-4.125-8.25-4.125S3.75 4.097 3.75 6.375m16.5 0v11.25c0 2.278-3.694 4.125-8.25 4.125s-8.25-1.847-8.25-4.125V6.375m16.5 0v3.75m-16.5-3.75v3.75m16.5 0v3.75C20.25 16.153 16.556 18 12 18s-8.25-1.847-8.25-4.125v-3.75m16.5 0c0 2.278-3.694 4.125-8.25 4.125s-8.25-1.847-8.25-4.125', text: 'Database design and optimization' }, { icon: 'M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z', text: 'API development and integration' }, { icon: 'M12 6v6h4.5m4.5 0a9 9 0 11-18 0 9 9 0 0118 0z', text: 'Cloud deployment and scaling' }] as item}
								<li class="flex items-center gap-3 text-gray-300">
									<svg
										xmlns="http://www.w3.org/2000/svg"
										fill="none"
										viewBox="0 0 24 24"
										stroke-width="1.5"
										stroke="currentColor"
										class="h-5 w-5 text-[var(--accent-primary)]"
									>
										<path stroke-linecap="round" stroke-linejoin="round" d={item.icon} />
									</svg>
									{item.text}
								</li>
							{/each}
						</ul>
					</div>
				</div>
			</div>
		</div>
	</section>

	<!-- Call to Action -->
	<section
		class="relative overflow-hidden rounded-2xl bg-gradient-to-br from-indigo-600/20 via-violet-600/20 to-transparent p-12"
	>
		<div class="bg-[var(--card-container-bg)]/40 absolute inset-0 backdrop-blur-sm"></div>
		<div class="relative mx-auto max-w-3xl text-center">
			<h2 class="mb-4 text-4xl font-bold">Ready to Build Your Digital Presence?</h2>
			<p class="mb-8 text-xl text-violet-100">
				Let's create something amazing together. We're here to turn your vision into a powerful web
				presence.
			</p>
			<a
				href="/account/services/web"
				class="hover:shadow-[var(--accent-primary)]/20 group relative inline-flex items-center gap-2 overflow-hidden rounded-lg bg-gradient-to-r from-[var(--accent-primary)] to-[var(--accent-secondary)] px-10 py-4 text-lg font-semibold text-white transition-all hover:shadow-lg"
			>
				<span class="relative z-10">Start Your Project</span>
				<svg
					xmlns="http://www.w3.org/2000/svg"
					fill="none"
					viewBox="0 0 24 24"
					stroke-width="2"
					stroke="currentColor"
					class="h-5 w-5 transition-transform group-hover:translate-x-1"
				>
					<path
						stroke-linecap="round"
						stroke-linejoin="round"
						d="M13.5 4.5L21 12m0 0l-7.5 7.5M21 12H3"
					/>
				</svg>
			</a>
		</div>
	</section>
</div>
