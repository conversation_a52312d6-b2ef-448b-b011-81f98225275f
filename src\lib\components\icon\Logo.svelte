<script lang="ts">
	interface Props {
		/**
		 * The fill color of the logo
		 */
		fill?: string;

		class?: string;
	}

	let { fill = 'black', class: className = '' }: Props = $props();
</script>

<svg width="87" height="39" viewBox="0 0 87 39" fill="none" xmlns="http://www.w3.org/2000/svg">
	<path
		d="M0 9.88733L7.80769 0L63.4398 0.0380096C68.7082 -3.56138e-06 83.7606 0.0380087 83.7606 14.833C83.7606 20.8048 81.6782 25.3441 72.5 27.4648L87 39H69.7115L58.5577 29.1127H49.0769V39H37.3654V19.2254H56.0515C57.4625 21.2199 59.8083 22.5251 62.4636 22.5251C65.3542 22.5251 67.8781 20.9782 69.2284 18.679H63.4398C61.2832 18.679 59.5349 16.9571 59.5349 14.833C59.5349 12.7088 61.2832 10.9869 63.4398 10.9869H69.2284C67.8781 8.68772 65.3542 7.14084 62.4636 7.14084C60.0624 7.14084 57.9144 8.20813 56.4818 9.88733H29V39H16.1731V9.88733H0Z"
		{fill}
	/>
</svg>
