<script lang="ts">
	import type { PageData } from './$types';
	import Navbar from '$lib/components/nav/Navbar.svelte';
	import ServiceBlock from '$lib/components/ServiceBlock.svelte';
	import AccountCard from '$lib/components/AccountCard.svelte';
	import { fade, fly, scale } from 'svelte/transition';
	import { quintOut } from 'svelte/easing';

	interface Props {
		data: PageData;
	}

	let { data } = $props();

	const statusColors: Record<string, string> = {
		success: 'bg-green-400',
		info: 'bg-blue-400',
		warning: 'bg-yellow-400',
		error: 'bg-red-400'
	};

	const services = [
		{
			title: 'Custom Software Development',
			description:
				'Tailored solutions for your business needs, from inventory systems to custom applications that drive efficiency and growth.',
			link: '/account/services/software',
			icon: 'icon-[mingcute--code-line] h-8 w-8',
			gradient: 'from-blue-500 to-cyan-500'
		},
		{
			title: 'Embedded Systems',
			description:
				'Smart devices and industrial automation solutions that integrate seamlessly with your existing infrastructure.',
			link: '/account/services/embedded',
			icon: 'icon-[mingcute--chip-line] h-8 w-8',
			gradient: 'from-purple-500 to-pink-500'
		},
		{
			title: 'Website Development',
			description:
				'Modern, responsive websites and web applications that engage your audience and drive results.',
			link: '/account/services/web',
			icon: 'icon-[qlementine-icons--file-html-16] h-8 w-8',
			gradient: 'from-orange-500 to-red-500'
		},
		{
			title: 'Device Repairs',
			description:
				'Professional repair services for smartphones, tablets, laptops, and other electronic devices with quick turnaround times.',
			link: '/account/services/repairs',
			icon: 'icon-[la--globe-americas] h-8 w-8',
			gradient: 'from-green-500 to-emerald-500'
		}
	];

	const quickActions = [
		{
			title: 'Account Settings',
			description: 'Manage your profile and preferences',
			link: '/account/settings',
			icon: 'icon-[mingcute--settings-3-line] h-6 w-6',
			gradient: 'from-blue-500 to-indigo-500'
		},
		{
			title: 'Billing',
			description: 'View and manage your subscriptions',
			link: '/account/billing',
			icon: 'icon-[mingcute--wallet-line] h-6 w-6',
			gradient: 'from-green-500 to-emerald-500'
		},
		{
			title: 'Support',
			description: 'Get help with your services',
			link: '/account/support',
			icon: 'icon-[mingcute--user-question-line] h-6 w-6',
			gradient: 'from-purple-500 to-pink-500'
		}
	];
</script>

<Navbar />

<div class="min-h-screen bg-[var(--bg)] text-[var(--text-primary)]">
	<div class="container mx-auto px-4 py-8">
		<!-- Hero Section -->
		<section class="mb-16" in:fade={{ duration: 500 }}>
			<div class="relative overflow-hidden rounded-3xl bg-[var(--card-bg)] p-8 backdrop-blur-xl">
				<div
					class="from-[var(--tr-blue)]/20 via-[var(--tr-blue-alt)]/10 absolute inset-0 bg-gradient-to-br to-transparent"
				></div>
				<div class="relative flex flex-col gap-8 md:flex-row md:items-center md:justify-between">
					<div class="flex flex-col items-center gap-4 text-center md:items-start md:text-left">
						<span class="text-sm font-medium text-[var(--text-secondary)]">Welcome back</span>
						<h1 class="text-4xl font-bold tracking-tight">
							<span
								class="bg-gradient-to-r from-[var(--tr-blue)] to-[var(--tr-blue-alt)] bg-clip-text text-transparent"
							>
								{data.sessionUser?.user_info?.first_name}
								{data.sessionUser?.user_info?.last_name[0]}.
							</span>
						</h1>
						<p class="max-w-md text-[var(--text-secondary)]">
							Manage your services, view recent activity, and access your account settings all in
							one place.
						</p>
					</div>
					<div class="flex justify-center gap-4 md:justify-end">
						<button
							class="hover:shadow-[var(--tr-blue)]/25 rounded-lg bg-[var(--btn-primary)] px-6 py-2 font-medium text-[var(--text-primary)] transition-all hover:bg-[var(--btn-hover-primary)] hover:shadow-lg"
						>
							View Profile
						</button>
						<button
							class="rounded-lg border border-[var(--card-hover-bg)] bg-[var(--card-bg)] px-6 py-2 font-medium transition-all hover:bg-[var(--card-hover-bg)]"
						>
							Settings
						</button>
					</div>
				</div>
			</div>
		</section>

		<!-- Quick Actions -->
		<section class="mb-16" in:fade={{ duration: 500, delay: 100 }}>
			<div class="mb-8 flex items-center justify-between">
				<h2 class="text-2xl font-bold text-[var(--text-primary)]">Quick Actions</h2>
				<a
					href="/account/settings"
					class="text-sm text-[var(--tr-blue)] hover:text-[var(--tr-blue-alt)]">View All →</a
				>
			</div>
			<div class="grid grid-cols-1 gap-6 md:grid-cols-3">
				{#each quickActions as action, i}
					<div in:fly={{ y: 20, duration: 500, delay: i * 100, easing: quintOut }}>
						<a
							href={action.link}
							class="hover:shadow-[var(--card-container-bg)]/50 group block h-full rounded-2xl bg-[var(--card-bg)] p-6 transition-all duration-300 hover:-translate-y-1 hover:bg-[var(--card-hover-bg)] hover:shadow-xl"
						>
							<div class="mb-4 inline-flex rounded-xl bg-[var(--btn-primary)] p-3">
								<span class="{action.icon} text-[var(--text-primary)]"></span>
							</div>
							<h3 class="mb-2 text-xl font-semibold text-[var(--text-primary)]">{action.title}</h3>
							<p class="text-sm text-[var(--text-secondary)]">{action.description}</p>
						</a>
					</div>
				{/each}
			</div>
		</section>

		<!-- Services Grid -->
		<section class="mb-16" in:fade={{ duration: 500, delay: 200 }}>
			<div class="mb-8 flex items-center justify-between">
				<h2 class="text-2xl font-bold text-[var(--text-primary)]">Your Services</h2>
				<a
					href="/account/services"
					class="text-sm text-[var(--tr-blue)] hover:text-[var(--tr-blue-alt)]">View All →</a
				>
			</div>
			<div class="grid grid-cols-1 gap-6 md:grid-cols-2">
				{#each services as service, i}
					<div in:fly={{ y: 20, duration: 500, delay: i * 100, easing: quintOut }}>
						<a
							href={service.link}
							class="hover:shadow-[var(--card-container-bg)]/50 group block h-full overflow-hidden rounded-2xl bg-[var(--card-bg)] p-8 transition-all duration-300 hover:-translate-y-1 hover:bg-[var(--card-hover-bg)] hover:shadow-xl"
						>
							<div class="mb-6 inline-flex rounded-xl bg-[var(--btn-primary)] p-4">
								<span class="{service.icon} text-[var(--text-primary)]"></span>
							</div>
							<h3 class="mb-3 text-2xl font-semibold text-[var(--text-primary)]">
								{service.title}
							</h3>
							<p class="text-[var(--text-secondary)]">{service.description}</p>
							<div class="mt-6 flex items-center text-sm text-[var(--tr-blue)]">
								<span>Learn more</span>
								<span
									class="icon-[mingcute--arrow-right-line] ml-2 h-4 w-4 transition-transform group-hover:translate-x-1"
								></span>
							</div>
						</a>
					</div>
				{/each}
			</div>
		</section>

		<!-- Activity Feed -->
		<section in:fade={{ duration: 500, delay: 300 }}>
			<div class="mb-8 flex items-center justify-between">
				<h2 class="text-2xl font-bold text-[var(--text-primary)]">Recent Activity</h2>
				<a
					href="/account/activity"
					class="text-sm text-[var(--tr-blue)] hover:text-[var(--tr-blue-alt)]">View All →</a
				>
			</div>
			<div class="rounded-2xl bg-[var(--card-bg)] p-6">
				<div class="space-y-4">
					{#each [{ icon: 'mingcute--check-line', color: 'text-[var(--btn-success)]', title: 'Account password updated', time: '2 hours ago', status: 'success' }, { icon: 'mingcute--cloud-line', color: 'text-[var(--btn-info)]', title: 'Backup completed successfully', time: 'Yesterday', status: 'info' }, { icon: 'mingcute--mail-line', color: 'text-[var(--btn-warning)]', title: 'New email account created', time: '2 days ago', status: 'warning' }] as activity, i}
						<div
							in:fly={{ y: 20, duration: 500, delay: i * 100, easing: quintOut }}
							class="group flex items-center gap-4 rounded-xl bg-[var(--card-hover-bg)] p-4 transition-all duration-300 hover:bg-[var(--card-bg)]"
						>
							<div
								class="flex h-12 w-12 items-center justify-center rounded-xl bg-[var(--card-container-bg)]"
							>
								{#if activity.icon === 'mingcute--check-line'}
									<span class="icon-[mingcute--check-line] h-6 w-6 {activity.color}"></span>
								{:else if activity.icon === 'mingcute--cloud-line'}
									<span class="icon-[mingcute--cloud-line] h-6 w-6 {activity.color}"></span>
								{:else if activity.icon === 'mingcute--mail-line'}
									<span class="icon-[mingcute--mail-line] h-6 w-6 {activity.color}"></span>
								{/if}
							</div>
							<div class="flex-1">
								<p class="font-medium text-[var(--text-primary)]">{activity.title}</p>
								<p class="text-sm text-[var(--text-secondary)]">{activity.time}</p>
							</div>
							<div
								class="flex h-8 w-8 items-center justify-center rounded-full bg-[var(--card-container-bg)]"
							>
								<div class="h-2 w-2 rounded-full {statusColors[activity.status]}"></div>
							</div>
						</div>
					{/each}
				</div>
			</div>
		</section>
	</div>
</div>

<style lang="postcss">
	@tailwind base;
	@tailwind components;
	@tailwind utilities;

	:global(body) {
		background: var(--bg);
	}

	:global(.service-block) {
		transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
	}

	:global(.service-block:hover) {
		transform: translateY(-4px);
		box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.3);
	}

	:global(.account-card) {
		transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
	}

	:global(.account-card:hover) {
		transform: translateY(-4px);
		box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.3);
	}
</style>
