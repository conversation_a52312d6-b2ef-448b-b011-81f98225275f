name: Deploy
on:
  push:
    branches:
      - master
      - staging
  workflow_dispatch:
    inputs:
      branch:
        description: 'Branch to deploy'
        required: true
        default: 'next'
        type: choice
        options:
          - next
          - master

jobs:
  auto-deploy:
    runs-on: website
    if: github.event_name == 'push' || (github.event_name == 'workflow_dispatch' && github.event.inputs.branch == 'next')
    steps:
      - name: Checkout Repository
        uses: actions/checkout@v4
        with:
          submodules: true
          token: ${{ secrets.GLOBAL_ACCESS_TOKEN }}

      - name: Deploy website
        env:
          DEPLOY_ENABLED: ${{ secrets.DEPLOY_ENABLED }}
          DATABASE_SECRET: ${{ secrets.DATABASE_SECRET }}
          DATABASE_PASSWORD: ${{ secrets.DATABASE_PASSWORD }}
          DATABASE_NAME: ${{ secrets.DATABASE_NAME }}
          SENDGRID_API_KEY: ${{ secrets.SENDGRID_API_KEY }}
        run: |
          chmod +x ./scripts/deploy.sh
          ./scripts/deploy.sh
