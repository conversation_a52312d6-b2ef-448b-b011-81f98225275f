<script lang="ts">
	import { fade } from 'svelte/transition';
</script>

<div class="space-y-16 py-8" in:fade>
	<!-- Hero Section -->
	<section
		class="relative overflow-hidden rounded-2xl bg-gradient-to-r from-violet-600 to-cyan-600 p-8 md:p-12"
	>
		<div class="relative z-10 max-w-3xl">
			<h1 class="mb-4 text-4xl font-bold md:text-5xl">Embedded Systems Development</h1>
			<p class="text-xl text-blue-100">
				Crafting custom, low-level solutions that bridge the gap between hardware and software,
				powered by cutting-edge technology and machine learning.
			</p>
		</div>
		<!-- Circuit Board Pattern Background -->
		<div class="absolute right-0 top-0 -z-10 h-full w-full opacity-20">
			<svg
				xmlns="http://www.w3.org/2000/svg"
				fill="none"
				viewBox="0 0 24 24"
				stroke-width="1.5"
				stroke="currentColor"
				class="h-full w-full"
			>
				<path
					stroke-linecap="round"
					stroke-linejoin="round"
					d="M3.75 3v11.25A2.25 2.25 0 006 16.5h2.25M3.75 3h-1.5m1.5 0h16.5m0 0h1.5m-1.5 0v11.25A2.25 2.25 0 0118 16.5h-2.25m-7.5 0h7.5m-7.5 0l-1 3m8.5-3l1 3m0 0l.5 1.5m-.5-1.5h-9.5m0 0l-.5 1.5m.75-9l3-3 2.148 2.148A12.061 12.061 0 0116.5 7.605"
				/>
			</svg>
		</div>
	</section>

	<!-- Features Grid -->
	<section
		class="relative rounded-2xl bg-gradient-to-br from-violet-600/10 via-cyan-600/5 to-transparent p-8"
	>
		<div class="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
			<div
				class="rounded-xl bg-gradient-to-br from-violet-600/20 via-cyan-600/10 to-transparent p-6 backdrop-blur-sm transition-all hover:from-violet-600/30 hover:via-cyan-600/20 hover:to-transparent hover:shadow-lg hover:shadow-violet-500/10"
			>
				<svg
					xmlns="http://www.w3.org/2000/svg"
					fill="none"
					viewBox="0 0 24 24"
					stroke-width="1.5"
					stroke="currentColor"
					class="mb-4 h-12 w-12 text-[var(--accent-primary)]"
				>
					<path
						stroke-linecap="round"
						stroke-linejoin="round"
						d="M9.75 3.104v5.714a2.25 2.25 0 01-.659 1.591L5 14.5M9.75 3.104c-.251.023-.501.05-.75.082m.75-.082a24.301 24.301 0 014.5 0m0 0v5.714c0 .597.237 1.17.659 1.591L19.8 15.3M14.25 3.104c.251.023.501.05.75.082M19.8 15.3l-1.57.393A9.065 9.065 0 0112 15a9.065 9.065 0 00-6.23-.693L5 14.5m14.8.8l1.402 1.402c1.232 1.232.65 3.318-1.067 3.611A48.309 48.309 0 0112 21c-2.773 0-5.491-.235-8.135-.687-1.718-.293-2.3-2.379-1.067-3.61L5 14.5"
					/>
				</svg>
				<h3 class="mb-2 text-xl font-semibold">Custom PLC Development</h3>
				<p class="text-gray-300">
					Tailored Programmable Logic Controllers designed to meet your specific industrial
					automation needs with precision and reliability.
				</p>
			</div>

			<div
				class="rounded-xl bg-gradient-to-br from-cyan-600/20 via-blue-600/10 to-transparent p-6 backdrop-blur-sm transition-all hover:from-cyan-600/30 hover:via-blue-600/20 hover:to-transparent hover:shadow-lg hover:shadow-cyan-500/10"
			>
				<svg
					xmlns="http://www.w3.org/2000/svg"
					fill="none"
					viewBox="0 0 24 24"
					stroke-width="1.5"
					stroke="currentColor"
					class="mb-4 h-12 w-12 text-[var(--accent-primary)]"
				>
					<path
						stroke-linecap="round"
						stroke-linejoin="round"
						d="M9.75 3.104v5.714a2.25 2.25 0 01-.659 1.591L5 14.5M9.75 3.104c-.251.023-.501.05-.75.082m.75-.082a24.301 24.301 0 014.5 0m0 0v5.714c0 .597.237 1.17.659 1.591L19.8 15.3M14.25 3.104c.251.023.501.05.75.082M19.8 15.3l-1.57.393A9.065 9.065 0 0112 15a9.065 9.065 0 00-6.23-.693L5 14.5m14.8.8l1.402 1.402c1.232 1.232.65 3.318-1.067 3.611A48.309 48.309 0 0112 21c-2.773 0-5.491-.235-8.135-.687-1.718-.293-2.3-2.379-1.067-3.61L5 14.5"
					/>
				</svg>
				<h3 class="mb-2 text-xl font-semibold">Machine Learning Integration</h3>
				<p class="text-gray-300">
					Embedded AI solutions that bring intelligent decision-making to edge devices, enabling
					real-time processing and analysis.
				</p>
			</div>

			<div
				class="rounded-xl bg-gradient-to-br from-emerald-600/20 via-teal-600/10 to-transparent p-6 backdrop-blur-sm transition-all hover:from-emerald-600/30 hover:via-teal-600/20 hover:to-transparent hover:shadow-lg hover:shadow-emerald-500/10"
			>
				<svg
					xmlns="http://www.w3.org/2000/svg"
					fill="none"
					viewBox="0 0 24 24"
					stroke-width="1.5"
					stroke="currentColor"
					class="mb-4 h-12 w-12 text-[var(--accent-primary)]"
				>
					<path
						stroke-linecap="round"
						stroke-linejoin="round"
						d="M3.75 13.5l10.5-11.25L12 10.5h8.25L9.75 21.75 12 13.5H3.75z"
					/>
				</svg>
				<h3 class="mb-2 text-xl font-semibold">Vision Systems</h3>
				<p class="text-gray-300">
					Advanced camera systems with embedded processing capabilities for industrial inspection,
					quality control, and automation.
				</p>
			</div>
		</div>
	</section>

	<!-- Detailed Service Description -->
	<section
		class="relative overflow-hidden rounded-2xl bg-gradient-to-br from-violet-600/5 via-cyan-600/5 to-transparent p-8"
	>
		<div class="relative">
			<h2 class="mb-12 text-center text-3xl font-bold">
				<span class="relative inline-block">
					Our Embedded Solutions
					<span
						class="absolute -bottom-2 left-0 h-1 w-full bg-gradient-to-r from-[var(--accent-primary)] to-[var(--accent-secondary)]"
					></span>
				</span>
			</h2>

			<div class="grid gap-12 md:grid-cols-2">
				<!-- Custom Hardware Integration -->
				<div
					class="group relative overflow-hidden rounded-xl bg-gradient-to-br from-violet-600/10 to-transparent p-8 transition-all hover:shadow-lg hover:shadow-violet-500/10"
				>
					<div class="bg-[var(--card-container-bg)]/80 absolute inset-0 backdrop-blur-sm"></div>
					<div class="relative">
						<div class="mb-6 flex items-center gap-4">
							<div
								class="flex h-12 w-12 items-center justify-center rounded-lg bg-gradient-to-br from-violet-500 to-cyan-500"
							>
								<svg
									xmlns="http://www.w3.org/2000/svg"
									fill="none"
									viewBox="0 0 24 24"
									stroke-width="1.5"
									stroke="currentColor"
									class="h-6 w-6 text-white"
								>
									<path
										stroke-linecap="round"
										stroke-linejoin="round"
										d="M9.75 3.104v5.714a2.25 2.25 0 01-.659 1.591L5 14.5M9.75 3.104c-.251.023-.501.05-.75.082m.75-.082a24.301 24.301 0 014.5 0m0 0v5.714c0 .597.237 1.17.659 1.591L19.8 15.3M14.25 3.104c.251.023.501.05.75.082M19.8 15.3l-1.57.393A9.065 9.065 0 0112 15a9.065 9.065 0 00-6.23-.693L5 14.5m14.8.8l1.402 1.402c1.232 1.232.65 3.318-1.067 3.611A48.309 48.309 0 0112 21c-2.773 0-5.491-.235-8.135-.687-1.718-.293-2.3-2.379-1.067-3.61L5 14.5"
									/>
								</svg>
							</div>
							<h3 class="text-2xl font-semibold text-[var(--accent-primary)]">
								Custom Hardware Integration
							</h3>
						</div>
						<p class="mb-6 text-gray-300">
							We specialize in developing custom embedded systems that seamlessly integrate with
							your existing hardware infrastructure. Our solutions are designed to be robust,
							efficient, and tailored to your specific requirements.
						</p>
						<ul class="space-y-3">
							{#each [{ icon: 'M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z', text: 'Custom PCB design and development' }, { icon: 'M3.75 13.5l10.5-11.25L12 10.5h8.25L9.75 21.75 12 13.5H3.75z', text: 'Hardware-software integration' }, { icon: 'M12 6v6h4.5m4.5 0a9 9 0 11-18 0 9 9 0 0118 0z', text: 'Real-time system optimization' }, { icon: 'M13.5 10.5V6.75a4.5 4.5 0 119 0v3.75M3.75 21.75h10.5a2.25 2.25 0 002.25-2.25v-6.75a2.25 2.25 0 00-2.25-2.25H3.75a2.25 2.25 0 00-2.25 2.25v6.75a2.25 2.25 0 002.25 2.25z', text: 'Power management solutions' }] as item}
								<li class="flex items-center gap-3 text-gray-300">
									<svg
										xmlns="http://www.w3.org/2000/svg"
										fill="none"
										viewBox="0 0 24 24"
										stroke-width="1.5"
										stroke="currentColor"
										class="h-5 w-5 text-[var(--accent-primary)]"
									>
										<path stroke-linecap="round" stroke-linejoin="round" d={item.icon} />
									</svg>
									{item.text}
								</li>
							{/each}
						</ul>
					</div>
				</div>

				<!-- Intelligent Edge Computing -->
				<div
					class="group relative overflow-hidden rounded-xl bg-gradient-to-br from-cyan-600/10 to-transparent p-8 transition-all hover:shadow-lg hover:shadow-cyan-500/10"
				>
					<div class="bg-[var(--card-container-bg)]/80 absolute inset-0 backdrop-blur-sm"></div>
					<div class="relative">
						<div class="mb-6 flex items-center gap-4">
							<div
								class="flex h-12 w-12 items-center justify-center rounded-lg bg-gradient-to-br from-cyan-500 to-blue-500"
							>
								<svg
									xmlns="http://www.w3.org/2000/svg"
									fill="none"
									viewBox="0 0 24 24"
									stroke-width="1.5"
									stroke="currentColor"
									class="h-6 w-6 text-white"
								>
									<path
										stroke-linecap="round"
										stroke-linejoin="round"
										d="M9.813 15.904L9 18.75l-.813-2.846a4.5 4.5 0 00-3.09-3.09L2.25 12l2.846-.813a4.5 4.5 0 003.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 003.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 00-3.09 3.09zM18.259 8.715L18 9.75l-.259-1.035a3.375 3.375 0 00-2.455-2.456L14.25 6l1.036-.259a3.375 3.375 0 002.455-2.456L18 2.25l.259 1.035a3.375 3.375 0 002.456 2.456L21.75 6l-1.035.259a3.375 3.375 0 00-2.456 2.456zM16.894 20.567L16.5 21.75l-.394-1.183a2.25 2.25 0 00-1.423-1.423L13.5 18.75l1.183-.394a2.25 2.25 0 001.423-1.423l.394-1.183.394 1.183a2.25 2.25 0 001.423 1.423l1.183.394-1.183.394a2.25 2.25 0 00-1.423 1.423z"
									/>
								</svg>
							</div>
							<h3 class="text-2xl font-semibold text-[var(--accent-primary)]">
								Intelligent Edge Computing
							</h3>
						</div>
						<p class="mb-6 text-gray-300">
							Bring the power of artificial intelligence to your edge devices with our embedded
							machine learning solutions. We develop systems that can process data locally, reducing
							latency and improving efficiency.
						</p>
						<ul class="space-y-3">
							{#each [{ icon: 'M9.813 15.904L9 18.75l-.813-2.846a4.5 4.5 0 00-3.09-3.09L2.25 12l2.846-.813a4.5 4.5 0 003.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 003.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 00-3.09 3.09zM18.259 8.715L18 9.75l-.259-1.035a3.375 3.375 0 00-2.455-2.456L14.25 6l1.036-.259a3.375 3.375 0 002.455-2.456L18 2.25l.259 1.035a3.375 3.375 0 002.456 2.456L21.75 6l-1.035.259a3.375 3.375 0 00-2.456 2.456zM16.894 20.567L16.5 21.75l-.394-1.183a2.25 2.25 0 00-1.423-1.423L13.5 18.75l1.183-.394a2.25 2.25 0 001.423-1.423l.394-1.183.394 1.183a2.25 2.25 0 001.423 1.423l1.183.394-1.183.394a2.25 2.25 0 00-1.423 1.423z', text: 'On-device machine learning models' }, { icon: 'M2.25 15.75l5.159-5.159a2.25 2.25 0 013.182 0l5.159 5.159m-1.5-1.5l1.409-1.409a2.25 2.25 0 013.182 0l2.909 2.909m-18 3.75h16.5a1.5 1.5 0 001.5-1.5V6a1.5 1.5 0 00-1.5-1.5H3.75A1.5 1.5 0 002.25 6v12a1.5 1.5 0 001.5 1.5zm10.5-11.25h.008v.008h-.008V8.25zm.375 0a.375.375 0 11-.75 0 .375.375 0 01.75 0z', text: 'Computer vision applications' }, { icon: 'M3.75 3v11.25A2.25 2.25 0 006 16.5h2.25M3.75 3h-1.5m1.5 0h16.5m0 0h1.5m-1.5 0v11.25A2.25 2.25 0 0118 16.5h-2.25m-7.5 0h7.5m-7.5 0l-1 3m8.5-3l1 3m0 0l.5 1.5m-.5-1.5h-9.5m0 0l-.5 1.5m.75-9l3-3 2.148 2.148A12.061 12.061 0 0116.5 7.605', text: 'Sensor fusion and data processing' }, { icon: 'M3.75 13.5l10.5-11.25L12 10.5h8.25L9.75 21.75 12 13.5H3.75z', text: 'Edge AI optimization' }] as item}
								<li class="flex items-center gap-3 text-gray-300">
									<svg
										xmlns="http://www.w3.org/2000/svg"
										fill="none"
										viewBox="0 0 24 24"
										stroke-width="1.5"
										stroke="currentColor"
										class="h-5 w-5 text-[var(--accent-primary)]"
									>
										<path stroke-linecap="round" stroke-linejoin="round" d={item.icon} />
									</svg>
									{item.text}
								</li>
							{/each}
						</ul>
					</div>
				</div>
			</div>
		</div>
	</section>

	<!-- Call to Action -->
	<section
		class="relative overflow-hidden rounded-2xl bg-gradient-to-br from-violet-600/20 via-cyan-600/20 to-transparent p-12"
	>
		<div class="bg-[var(--card-container-bg)]/40 absolute inset-0 backdrop-blur-sm"></div>
		<div class="relative mx-auto max-w-3xl text-center">
			<h2 class="mb-4 text-4xl font-bold">Ready to translate your ideas into real hardware?</h2>
			<p class="mb-8 text-xl text-gray-300">
				Let's discuss how our embedded systems expertise can help you achieve your goals. We're here
				to turn your ideas into reality.
			</p>
			<a
				href="/account/services/embedded"
				class="hover:shadow-[var(--accent-primary)]/20 group relative inline-flex items-center gap-2 overflow-hidden rounded-lg bg-gradient-to-r from-[var(--accent-primary)] to-[var(--accent-secondary)] px-10 py-4 text-lg font-semibold text-white transition-all hover:shadow-lg"
			>
				<span class="relative z-10">Get Started</span>
				<svg
					xmlns="http://www.w3.org/2000/svg"
					fill="none"
					viewBox="0 0 24 24"
					stroke-width="2"
					stroke="currentColor"
					class="h-5 w-5 transition-transform group-hover:translate-x-1"
				>
					<path
						stroke-linecap="round"
						stroke-linejoin="round"
						d="M13.5 4.5L21 12m0 0l-7.5 7.5M21 12H3"
					/>
				</svg>
			</a>
		</div>
	</section>
</div>

<style>
	/* Add any custom styles here */
</style>
