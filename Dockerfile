# syntax=docker/dockerfile:1
FROM ubuntu:rolling AS system-deps
RUN apt-get update -y && \
    apt-get upgrade -y && \
    apt-get install -y --no-install-recommends ca-certificates wget && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/*

FROM node:23-alpine

RUN npm i -g pnpm

RUN mkdir -p /home/<USER>/node_modules && \
    chown -R node:node /home/<USER>

WORKDIR /home/<USER>

COPY /assets /home/<USER>/assets

USER root
RUN chmod -R a+r /home/<USER>/assets
USER node

COPY --chown=node:node . .

RUN pnpm install --frozen-lockfile --force
RUN pnpm build
RUN pnpm prune --prod --ignore-scripts

RUN rm .env

# Do we need this?
RUN cp -r /home/<USER>/assets /home/<USER>/build/assets

# Set production environment
ENV NODE_ENV=production

# Expose the application port
EXPOSE 3000

# Start the application
CMD ["node", "--max-old-space-size=4096", "build"]