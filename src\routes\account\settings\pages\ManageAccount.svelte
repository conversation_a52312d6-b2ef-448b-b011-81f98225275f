<script lang="ts">
	// No props needed for this component
	import { enhance } from '$app/forms';
	import * as Form from '$lib/components/forms';

	let showDeleteModal = $state(false);
	let showDeletionConfirmation = $state(false);
	let showDisableModal = $state(false);
	let showDataDownloadedModal = $state(false);
	let buttonText = $state('Delete Account');
	let isDeleting = $state(false);
	let isDisabling = $state(false);
	let countdown = $state(4);
	let confirmDelete = $state(false);
	let confirmDisable = $state(false);

	$effect(() => {
		// if confirmDelete is true, confirmDisable must be false
		if (confirmDelete) {
			confirmDisable = false;
			buttonText = 'Delete Account';
		}
		// if confirmDisable is true, confirmDelete must be false
		if (confirmDisable) {
			confirmDelete = false;
			buttonText = 'Disable Account';
		}
	});

	async function handleDeleteAccount() {
		isDeleting = true;

		try {
			const response = await fetch('/account/settings', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
					INTENT: 'user'
				},
				body: JSON.stringify({
					confirmDelete: confirmDelete,
					confirmDisable: confirmDisable
				})
			});
			if (response.ok) {
				showDeleteModal = false;
				showDeletionConfirmation = true;

				if (confirmDisable) {
					showDisableModal = true;
					isDisabling = true;
					countdown = 15;

					// Start countdown
					const timer = setInterval(() => {
						countdown--;
						if (countdown <= 0) {
							clearInterval(timer);
							window.location.href = '/logout';
						}
					}, 1000);
				}

				if (confirmDelete) {
					showDeleteModal = false;
					showDeletionConfirmation = true;
					countdown = 10;
					// Start countdown
					const timer = setInterval(() => {
						countdown--;
						if (countdown <= 0) {
							clearInterval(timer);
							window.location.href = '/logout';
						}
					}, 1000);
				}
			} else {
				throw new Error('Failed to delete account');
			}
		} catch (error) {
			console.error('Error deleting account:', error);
			isDeleting = false;
		}
	}
</script>

<div class="space-y-8">
	<div>
		<h2 class="mb-1 text-xl font-semibold text-[var(--text-primary)]">Account Management</h2>
		<p class="text-sm text-[var(--text-secondary)]">
			Manage your subscription, data, and account preferences
		</p>
	</div>

	<!-- Data Management Section -->
	<section class="space-y-6">
		<div>
			<h3 class="text-base font-medium text-[var(--text-primary)]">Data Management</h3>
			<p class="mt-1 text-sm text-[var(--text-secondary)]">Access and manage your account data</p>
		</div>

		<div class="rounded-lg bg-[var(--card-container-bg)] p-4">
			<div class="flex items-center justify-between">
				<div>
					<h4 class="font-medium text-[var(--text-primary)]">Download Your Data</h4>
					<p class="text-sm text-[var(--text-secondary)]">
						Get a copy of your account data and history
					</p>
				</div>
				<Form.Button type="secondary" onClick={() => (showDataDownloadedModal = true)}>
					{#snippet text()}
						Download
					{/snippet}
				</Form.Button>
			</div>
		</div>
	</section>

	<!-- Account Deletion Section -->
	<section class="space-y-6">
		<div>
			<h3 class="text-base font-medium text-[var(--text-primary)]">Danger Zone</h3>
			<p class="mt-1 text-sm text-[var(--text-secondary)]">Irreversible and destructive actions</p>
		</div>

		<div class="rounded-lg bg-[var(--error-color-light)] p-4">
			<div class="flex items-center justify-between">
				<div>
					<h4 class="font-medium text-[var(--text-primary)]">Delete Account</h4>
					<p class="text-sm text-[var(--text-secondary)]">
						Permanently delete your account and all associated data
					</p>
				</div>
				<Form.Button type="error" onClick={() => (showDeleteModal = true)}>
					{#snippet text()}
						Delete Account
					{/snippet}
				</Form.Button>
			</div>
		</div>
	</section>
</div>

<Form.Modal
	title="Delete Account"
	show={showDeleteModal}
	showCloseButton={!isDeleting}
	onClose={() => (showDeleteModal = false)}
>
	{#snippet body()}
		<p class="text-sm text-[var(--text-secondary)]">
			In order to delete your account, you must confirm your intent to delete your account. Please
			select the checkbox below that best describes your intent.
		</p>
		<div class="mt-4 flex flex-col gap-2">
			<div class="flex flex-col gap-2">
				<Form.Checkbox id="confirmDelete" name="confirmDelete" bind:checked={confirmDelete}>
					{#snippet label()}
						Delete my account immediately. <span class="text-[var(--text-secondary)]"
							>(This is irreversible.)</span
						>
					{/snippet}
				</Form.Checkbox>
				<Form.Checkbox id="confirmDisable" name="confirmDisable" bind:checked={confirmDisable}>
					{#snippet label()}
						Disable my account but delete it in 30 days unless I re-enable it.
					{/snippet}
				</Form.Checkbox>
			</div>
		</div>
	{/snippet}
	{#snippet submitButton()}
		{#if confirmDelete || confirmDisable}
			<Form.Button type="error" onClick={handleDeleteAccount}>
				{#snippet text()}
					{#if isDeleting}
						<div class="flex items-center">
							<span class="icon-[mingcute--loading-line] mr-2 h-4 w-4 animate-spin"></span>
							{buttonText}
						</div>
					{:else}
						{buttonText}
					{/if}
				{/snippet}
			</Form.Button>
		{/if}
	{/snippet}
</Form.Modal>

<Form.Modal
	title="Account Deleted"
	description="Your account has been successfully deleted. You will be redirected to the login page in {countdown} seconds."
	show={showDeletionConfirmation}
	showCloseButton={false}
	onClose={() => {}}
>
	{#snippet submitButton()}
		<div class="flex items-center justify-center text-[var(--text-secondary)]">
			<span class="icon-[mingcute--loading-line] mr-2 h-4 w-4 animate-spin"></span>
			Redirecting...
		</div>
	{/snippet}
</Form.Modal>

<Form.Modal
	title="Account Disabled"
	show={showDisableModal}
	showCloseButton={!isDisabling}
	onClose={() => (showDisableModal = false)}
>
	{#snippet body()}
		<p class="text-sm text-[var(--text-secondary)]">
			Your account has been disabled. You will recieve an email with a link to re-enable your
			account.
		</p>

		<!-- red warning -->
		<div class="my-4 rounded-lg bg-[var(--error-color-light)] p-4">
			<p class="text-sm text-[var(--text-secondary)]">
				You will not be able to login to your account until you re-enable it.
			</p>
		</div>

		<p class="text-sm text-[var(--text-secondary)]">
			You will be logged out in <span class="font-bold">{countdown}</span> seconds.
		</p>
	{/snippet}
	{#snippet submitButton()}
		<div class="flex items-center justify-center text-[var(--text-secondary)]">
			<span class="icon-[mingcute--loading-line] mr-2 h-4 w-4 animate-spin"></span>
			Redirecting...
		</div>
	{/snippet}
</Form.Modal>

<Form.Modal
	title="Account Data"
	description="You can't download your data yet. We're working on it!"
	show={showDataDownloadedModal}
	showCloseButton={true}
	onClose={() => (showDataDownloadedModal = false)}
></Form.Modal>
