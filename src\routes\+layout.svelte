<script lang="ts">
	import '$lib/css/main.scss';
	import DebugMenu from '$lib/components/dev/DebugMenu.svelte';
	import { initalizeChecking, stopChecking } from '$lib/js/detectDevtools.js';
	import { animatePhasers } from '$lib/js/animate';
	import { onDestroy, onMount, type Snippet } from 'svelte';
	import type { PageData } from './$types';
	import { env } from '$env/dynamic/public';
	import { Loader, isLoading, waitForResources } from '$lib/components/loader';
	import { isNavigating, initNavigation } from '$lib/js/navigation';

	interface Props {
		data: PageData;
		children: Snippet;
	}

	let { data, children }: Props = $props();

	let showStatus = $state(true);
	let gitPullStatus = $state(data.gitPullStatus);
	let cleanup: (() => void) | undefined;

	onMount(() => {
		// Ensure loader is visible on first page load
		isLoading.set(true);

		// Start resource loading immediately
		waitForResources();

		if (env.PUBLIC_ENVIRONMENT_VERSION !== 'dev') {
			initalizeChecking();
		}

		// Initialize navigation handling
		cleanup = initNavigation();

		// clear the url params
		history.pushState({}, '', window.location.pathname);

		// give the components time to load
		setTimeout(() => {
			animatePhasers(document.body, 5), 10;
		});

		if (gitPullStatus === 'success' || (gitPullStatus === 'failed' && showStatus)) {
			setTimeout(() => {
				showStatus = false;
			}, 5000);
		}
	});

	onDestroy(() => {
		stopChecking();
		cleanup?.();
	});
</script>

{#if $isLoading || $isNavigating}
	<div class="fixed inset-0 z-[100000] overflow-hidden">
		<div class="h-full w-full overflow-hidden">
			<Loader transparent={true} />
		</div>
	</div>
{/if}

{#if !data.production && data.newerCommits}
	<div
		class="fixed right-0 top-0 z-[100000] w-full select-none bg-red-800 p-2 text-center font-bold text-white"
	>
		Remote commit:
		<a href="https://github.com/Texrepairs/Website/commit/{data.commit || ''}" target="_blank">
			<code class="mx-1 rounded-md bg-[#00000085] p-1 font-mono text-[var(--text-debug-value)]">
				{data.commit?.split('').splice(-10).join('') || '...'}
			</code>
		</a>
		is newer than local commit please use
		<a
			href="/dbg/git/pull"
			class="mx-1 select-text rounded-md px-2 py-1 text-sm hover:bg-[#ffffff15]">git pull</a
		>
	</div>
{/if}

{#if data.gitPullStatus === 'success' && showStatus}
	<div
		class="fixed right-0 top-0 z-[100000] w-full select-none bg-green-800 p-2 text-center font-bold text-white"
	>
		Git pull successful
	</div>
{/if}

{#if data.gitPullStatus === 'failed' && showStatus}
	<div
		class="fixed right-0 top-0 z-[100000] w-full select-none bg-red-800 p-2 text-center font-bold text-white"
	>
		Git pull failed
	</div>
{/if}

{#if !data.production && (data.debugInfo?.length ?? 0) > 0}
	<DebugMenu
		debugInfo={data.debugInfo as any}
		
		sessionUser={data.sessionUser}
		sessionPermissions={data.sessionPermissions}
	/>
{/if}

<div class="min-h-[100vh]">
	{#if children}
		{@render children()}
	{:else}
		fallback content
	{/if}
</div>

<style lang="scss">
	// @use '../../lib/css/main.scss';

	:global(body.loading) {
		overflow: hidden;
		height: 100vh;
		width: 100vw;
		position: fixed;
	}
</style>
