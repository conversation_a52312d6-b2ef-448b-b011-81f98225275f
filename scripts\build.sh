#!/bin/bash
bold=$(tput bold)
normal=$(tput sgr0)

colored_echo() {
    local text=$1
    local hex_color=$2

    # Convert hex color to RGB
    local r=$(printf '%d' 0x${hex_color:0:2})
    local g=$(printf '%d' 0x${hex_color:2:2})
    local b=$(printf '%d' 0x${hex_color:4:2})

    # Print the text with the specified color
    echo -e "\e[38;2;${r};${g};${b}m${text}\e[0m"
}

colored_label() {
    local label=$1
    local text=$2
    local hex_color=$3

    # Convert hex color to RGB
    local r=$(printf '%d' 0x${hex_color:0:2})
    local g=$(printf '%d' 0x${hex_color:2:2})
    local b=$(printf '%d' 0x${hex_color:4:2})

    # Print the text with the specified color
    echo -e "\e[38;2;${r};${g};${b}m${label}\e[38;2;${r};${g};${b}m$text\e[0m"
}

download_with_spinner() {
    local url=$1
    local output=$2

    spinner() {
        local pid=$!
        local delay=0.1
        local spinstr='|/-\'
        while [ "$(ps a | awk '{print $1}' | grep $pid)" ]; do
            local temp=${spinstr#?}
            printf " [%c]  " "$spinstr"
            local spinstr=$temp${spinstr%"$temp"}
            sleep $delay
            printf "\b\b\b\b\b\b"
        done
        printf "    \b\b\b\b"
    }

    # Download file in background
    sudo curl -fsSL $url -o $output &
    spinner
    wait $!

    if [ $? -eq 0 ]; then
        colored_echo "${bold}OK!${normal} Download completed" "00FF00"
    else
        panic "Download: $url failed"
    fi
}

wait_for_cmd_spinner() {
    local cmd=$1
    local message=$2

    spinner() {
        local pid=$!
        local delay=0.1
        local spinstr='|/-\'
        # check for crtl+c
        while [ "$(ps a | awk '{print $1}' | grep $pid)" ]; do
            local temp=${spinstr#?}
            printf " [%c]  " "$spinstr"
            local spinstr=$temp${spinstr%"$temp"}
            sleep $delay
            printf "\b\b\b\b\b\b"
        done
        printf "    \b\b\b\b"
    }

    # Run command in background
    $cmd > /dev/null &
    spinner
    wait $!

    if [ $? -eq 0 ]; then
        pass "$message"
    else
        panic "Command failed: $cmd"
    fi
}

success() {
    local message=$1
    colored_label "${bold}SUCESS!${normal} " "$message" "00FF00"
}

pass() {
    local message=$1 ##02ed4d
    colored_echo "${bold}OK${normal} $message" "02ed4d"
}

info() {
    local message=$1
    colored_echo "${bold}INFO${normal} $message" "8a8a8a"
}

panic() {
    local message=$1
    colored_label "${bold}ERR!${normal} " "$message" "FF0000"
    exit 1
}
docker_install() {
    # Remove conflicts (if they exist)
    for pkg in docker.io docker-doc docker-compose docker-compose-v2 podman-docker containerd runc; do sudo apt-get remove $pkg > /dev/null; done

    info "Installing Docker"
    
    sudo apt-get update -y > /dev/null
    sudo apt-get install ca-certificates curl -y > /dev/null
    sudo install -m 0755 -d /etc/apt/keyrings > /dev/null

    info "Adding Docker GPG key"

    download_with_spinner https://download.docker.com/linux/ubuntu/gpg /etc/apt/keyrings/docker.asc
    sudo chmod a+r /etc/apt/keyrings/docker.asc > /dev/null

    pass "Docker GPG key added"

    echo \
        "deb [arch=$(dpkg --print-architecture) signed-by=/etc/apt/keyrings/docker.asc] https://download.docker.com/linux/ubuntu \
        $(. /etc/os-release && echo "$VERSION_CODENAME") stable" | \
        sudo tee /etc/apt/sources.list.d/docker.list > /dev/null
    sudo apt-get update -y > /dev/null

    pass "Docker added to apt"
    info "Installing apt dependencies"

    wait_for_cmd_spinner "sudo apt-get install -y docker-ce docker-ce-cli containerd.io docker-buildx-plugin docker-compose-plugin" "Apt dependencies installed"
}

docker_check() {
    if ! command -v docker &> /dev/null
    then
        info "Docker is not installed"
        docker_install

        if ! command -v docker &> /dev/null
        then
            panic "Docker installation failed"
        else
            pass "Docker is installed"
        fi
    else
        pass "Docker is installed"
    fi
}

# check sudo
if [ "$EUID" -ne 0 ]; then
    panic "Please run as root"
fi

# Make sure the build diretory exists
if [ ! -d "./.svelte-kit" ]; then
    panic "Build directory does not exist, please build using \"pnpm run build\""
fi

info "Checking Docker installation"
docker_check

# Authenticating with Docker Hub
info "Checking authentication status"

if [ ! "$(sudo docker info | grep Username)" ]; then
    panic "Authentication failed, use \"docker login\""
else
    pass "Authenticated with Docker Hub"
    info "$(sudo docker info | grep Username)"
fi

info "Building Docker image"

# Texrepairs website
sudo docker build -q -t johnvbg/texrepairs:website -f ./Dockerfile . &> /dev/null

# validate the image built successfully
if [ $? -eq 0 ]; then
    pass "Docker image built successfully"
else
    panic "Docker image build failed"
fi

info "Pushing Docker image to Docker Hub"

# create a tag "latest" from the locally built image hash
sudo docker push johnvbg/texrepairs:website &> /dev/null

if [ $? -eq 0 ]; then
    success "Docker image pushed to Docker Hub"
else
    panic "Docker image push failed (Are you authenticated with \"docker login\"?)"
fi