/* We define these because they are the default values
   for the phase-in and phase-out animations */
:root {
	--phase-duration: 1s;
	--phase-distance: 100px;
}

.phase-in {
	transform: translateY(100px);
	opacity: 0;

	&[data-phase-stage='after'] {
		animation: phaseIn var(--phase-duration) ease forwards;

		@for $i from 0 through 100 {
			li:nth-child(#{$i}) {
				animation: phaseIn calc(#{$i * 0.9}s + var(--phase-duration)) ease forwards;
			}
		}

		@for $i from 0 through 100 {
			&[data-phase-children]:nth-child(#{$i}) {
				animation: phaseIn calc(#{$i * 0.9}s + var(--phase-duration)) ease forwards;
			}
		}
	}

	// phaseout
	&[data-phase-stage='before'] {
		animation: phaseOut var(--phase-duration) ease forwards;

		@for $i from 0 through 100 {
			// was #{$i * 0.9}s
			li:nth-child(#{$i}) {
				animation: phaseOut calc(#{$i * 0.9}s + var(--phase-duration)) ease forwards;
			}
		}

		@for $i from 0 through 100 {
			&[data-phase-children]:nth-child(#{$i}) {
				animation: phaseOut calc(#{$i * 0.9}s + var(--phase-duration)) ease forwards;
			}
		}
	}
}
@keyframes phaseIn {
	0% {
		transform: translateY(var(--phase-distance));
		transition: ease-in;
		opacity: 0;
	}

	100% {
		transform: translateY(0px);
		opacity: 1;
	}
}

@keyframes phaseOut {
	0% {
		transform: translateY(0px);
		opacity: 1;
	}

	100% {
		transform: translateY(var(--phase-distance));
		transition: ease-out;
		opacity: 0;
	}
}
