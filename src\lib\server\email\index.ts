import nodemailer from 'nodemailer';
import { env } from '$env/dynamic/private';

const transporter = nodemailer.createTransport({
	host: env.SMTP_HOST,
	port: parseInt(env.SMTP_PORT || '587'),
	secure: env.SMTP_SECURE === 'true',
	auth: {
		user: env.SMTP_USER,
		pass: env.SMTP_PASSWORD
	}
});

export async function sendPasswordResetEmail(email: string, resetToken: string) {
	const resetUrl = `${env.BASE_URL}/account/reset-password?token=${resetToken}`;

	// todo: export this to the API & use twilio to send the email not nodemailer
	await transporter.sendMail({
		from: env.SMTP_FROM || '<EMAIL>',
		to: email,
		subject: 'Password Reset Request',
		html: `
            <h1>Password Reset Request</h1>
            <p>You have requested to reset your password. Click the link below to proceed:</p>
            <a href="${resetUrl}">Reset Password</a>
            <p>This link will expire in 1 hour.</p>
            <p>If you did not request this password reset, please ignore this email.</p>
        `
	});
}
