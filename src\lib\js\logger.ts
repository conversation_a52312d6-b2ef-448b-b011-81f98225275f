export function logError(message: string | null = null, ...args: any[]): void {
	console.error(
		`%c[ERR!]%c${message ? ' ' + message : ''}`,
		'font-weight: bold; color: #f83838;',
		'color: #fe6d6d',
		...args
	);
}

export function logWarn(message: string | null = null, ...args: any[]): void {
	console.warn(
		`%c[WARN]%c${message ? ' ' + message : ''}`,
		'font-weight: bold; color: #f8f838;',
		'color: #fefe6d',
		...args
	);
}

export function logInfo(message: string | null = null, ...args: any[]): void {
	console.info(
		`%c[INFO]%c${message ? ' ' + message : ''}`,
		'font-weight: bold; color: #38f838;',
		'color: #6dfe6d',
		...args
	);
}

export function logDebug(message: string | null = null, ...args: any[]): void {
	console.debug(
		`%c[DBG!]%c${message ? ' ' + message : ''}`,
		'font-weight: bold; color: #5c5c5c;',
		'color: #bfbfbf',
		...args
	);
}

export function logModule(
	fn: (message: string | null, ...args: any[]) => void,
	module: string,
	message: string | null = null,
	...args: any[]
): void {
	fn(
		`%c[@texrepairs/${module}]%c${message ? ' ' + message : ''}`,
		'color: #4d78f8;',
		'color: reset;',
		...args
	);
}
