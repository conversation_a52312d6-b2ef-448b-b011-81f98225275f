/**
 * This file loads all the hooks in the hooks directory and exports them as an array.
 */
import fs from 'fs';
import path from 'path';
import type { Handle, RequestEvent, ResolveOptions } from '@sveltejs/kit';
import * as session from './sessions.js';
import * as dbg from './dbg.js';
import * as guards from './guards.js';
import apiHook from './api.js';

export type MaybePromise<T> = T | Promise<T>;

export type HandleExtension = {
	onHandle: Hook;
	onCleanup?: HookCleanup;
	default?: Hook;
	meta?: Record<string, any>;
};

export type HookEventOptions = {
	event: RequestEvent;
	resolve(event: RequestEvent, opts?: ResolveOptions): MaybePromise<Response>;
};

export type NextHookFn = () => MaybePromise<Response | void | null>;

export type Hook = (
	input: HookEventOptions,
	next: NextHookFn
) => MaybePromise<Response | null | void>;
export type HookCleanup = (input: HookEventOptions, next: NextHookFn) => MaybePromise<void>;

/**
 * ADD YOUR HOOKS HERE:
 */
let hooks: HandleExtension[] = [session, dbg, guards, apiHook];

let registered: boolean = false;

export async function registerHooks(hooks: HandleExtension[]): Promise<void> {
	if (registered) {
		return;
	}

	registered = true;

	for (const hook of hooks) {
		if (hook.onHandle || hook.default) {
			if (hook.default) {
				hook.onHandle = hook.default;
			}
			hooks.push(hook);
			console.log(`[HOOKS]: Registered hook ${hook.meta?.name} from ${hook.meta?.path}`);
		}
	}
}

export async function runHooks(input: HookEventOptions): Promise<Response> {
	async function next(index: number = 0): Promise<Response | null> {
		if (index < hooks.length) {
			const hook: HandleExtension = hooks[index];
			return (await hook.onHandle(input, () => next(index + 1))) || null;
		}
		return null;
	}

	const response = await next();

	// now we run the wrap up hooks
	for (const hook of hooks) {
		if (hook.onCleanup) {
			try {
				await hook.onCleanup(input, () => Promise.resolve(null));
			} catch {
				console.log(
					`[HOOKS]: Error running cleanup hook ${hook.meta?.name} from ${hook.meta?.path}`
				);
			}
		}
	}

	return response || input.resolve(input.event);
}

/**
 * Cleans up hooks
 */
export async function runCleanup(input: HookEventOptions): Promise<void> {
	// now we run the wrap up hooks
	for (const hook of hooks) {
		if (hook.onCleanup) {
			try {
				await hook.onCleanup(input, () => Promise.resolve(null));
			} catch {
				console.log(
					`[HOOKS]: Error running cleanup hook ${hook.meta?.name} from ${hook.meta?.path}`
				);
			}
		}
	}

	return;
}

export async function getHooks(): Promise<HandleExtension[]> {
	// const base = path.resolve(process.cwd(), './src/hooks');
	// const files = (await walk(base)).filter(
	//     (file) => file !== path.resolve(base, '/mod.js') && file.endsWith('.js')
	// );
	const files = [session] as any[];

	let modules: HandleExtension[] = [];

	for (const file of files) {
		// const fileName = path.basename(file);
		// const module = await import(`./${fileName}.js`);
		// console.log(module);
		const module = file;

		if (module.default) {
			module.default = module.default as Handle;
		}

		if (module.onHandle) {
			module.onHandle = module.onHandle as Handle;
		}

		// module.meta = {
		//     file,
		//     name: path.basename(file),
		//     path: path.dirname(file)
		// }

		if (module.onHandle || module.default) {
			// console.log(`[HOOKS]: Loaded hook ${module.meta.name} from ${module.meta.path}`);
			modules.push(module as HandleExtension);
		}
	}

	return modules;
}

async function walk(dir: string, list: string[] = [], depth = 0): Promise<string[]> {
	const files = await fs.promises.readdir(dir);

	if (depth > 3) {
		console.log(`[HOOKS]: Skipping directory ${dir} due to depth limit`);
		return list;
	}

	for (const file of files) {
		const filePath = path.join(dir, file);
		const stats = fs.statSync(filePath);

		if (stats.isDirectory()) {
			await walk(filePath, list, depth + 1);
		} else {
			list.push(filePath);
		}
	}

	return list;
}
