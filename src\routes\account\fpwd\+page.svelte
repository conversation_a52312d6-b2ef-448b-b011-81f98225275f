<script lang="ts">
	import Logo from '$lib/components/icon/Logo.svelte';
	import * as Form from '$lib/components/forms';
	import { enhance } from '$app/forms';
	import type { ActionResult } from '@sveltejs/kit';
	import cityBg from '@assets/images/images/vector-city-temp.jpg';

	export let form: {
		success?: boolean;
		message?: string;
		errors?: Record<string, string>;
		formData?: Record<string, string>;
	} = {};

	const getValue = (field: string) => form?.formData?.[field] || '';

	function handleSubmit() {
		return async ({ result, update }: { result: ActionResult; update: () => Promise<void> }) => {
			await update();
		};
	}
</script>

<div class="relative min-h-screen">
	<!-- Background image with overlay -->
	<div class="absolute inset-0 z-0">
		<img src={cityBg} alt="City background" class="h-full w-full object-cover blur-sm filter" />
		<div class="absolute inset-0 bg-[#0f172a] bg-opacity-80"></div>
	</div>

	<!-- Content -->
	<div
		class="relative z-10 flex min-h-screen flex-col items-center justify-center px-4 py-8 md:py-16"
	>
		<div
			class="w-full max-w-md overflow-hidden rounded-2xl bg-[#0a0e10d9] p-8 shadow-2xl backdrop-blur-sm"
		>
			<div class="mb-6 flex justify-center">
				<a href="/" data-sveltekit-reload>
					<Logo fill="#fff" />
				</a>
			</div>

			<div class="mb-6 text-center">
				<h2 class="text-2xl font-bold text-white">Reset Password</h2>
				<p class="mt-2 text-gray-400">
					Enter your email address and we'll send you instructions to reset your password.
				</p>
			</div>

			{#if form?.success}
				<div class="rounded-lg border border-gray-600 bg-gray-800/30 p-4 text-gray-300">
					{form.message}
				</div>
			{:else}
				<form method="POST" action="?/reset" use:enhance={handleSubmit} class="space-y-4">
					<Form.TextField
						label="Email"
						type="email"
						name="email"
						value={getValue('email')}
						placeholder="<EMAIL>"
						error={form?.errors?.email}
					/>

					<div class="mt-6">
						<button
							type="submit"
							class="w-full rounded-lg bg-[var(--btn-primary)] px-4 py-2.5 font-medium text-white transition-colors duration-200 hover:bg-[var(--btn-hover-primary)] focus:outline-none focus:ring-2 focus:ring-[var(--btn-primary)] focus:ring-offset-2"
						>
							Send Reset Instructions
						</button>
					</div>

					<div class="mt-6 text-center text-sm">
						<p class="text-gray-400">
							Remember your password?
							<a
								href="/login"
								class="ml-1 font-medium text-[var(--text-link)] hover:text-[var(--text-link-hover)]"
							>
								Sign in
							</a>
						</p>
					</div>
				</form>
			{/if}
		</div>
	</div>
</div>
