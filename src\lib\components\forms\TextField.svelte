<script lang="ts">
	let {
		label,
		type = 'text',
		name,
		value = $bindable(''),
		placeholder = '',
		error = undefined,
		disabled = false,
		readonly = false
	} = $props<{
		label?: string;
		type?: string;
		name: string;
		value?: string;
		placeholder?: string;
		error?: string;
		disabled?: boolean;
		readonly?: boolean;
	}>();
</script>

<div>
	{#if label}
		<label for={name} class="mb-1 block text-sm font-medium text-gray-300">{label}</label>
	{/if}
	<input
		{type}
		{name}
		id={name}
		{value}
		{placeholder}
		{disabled}
		{readonly}
		class={`w-full rounded-lg border px-3 py-2 text-sm transition-colors focus:border-[var(--btn-primary)] focus:ring focus:ring-[var(--btn-primary)] focus:ring-opacity-20 md:text-base
			${error ? 'border-red-500' : 'border-[#ffffff1a]'}
			${
				disabled
					? 'cursor-[var(--input-disabled-cursor)] bg-[var(--input-disabled-bg)] text-[var(--input-disabled-text)]'
					: readonly
						? 'cursor-[var(--input-readonly-cursor)] bg-[var(--input-readonly-bg)] text-[var(--input-readonly-text)]'
						: 'bg-[var(--bg-form-field-input)] text-white placeholder-gray-500'
			}`}
	/>
	{#if error}
		<p class="mt-1 text-xs text-red-500">{error}</p>
	{/if}
</div>
