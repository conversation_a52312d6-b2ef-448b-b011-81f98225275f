import type { PageServerLoad, Actions } from './$types';
import { AuthService } from '@backend/auth';
import { redirect } from '@sveltejs/kit';
import { db } from '@backend/db';

export const load: PageServerLoad = async ({ cookies }) => {
	const sessionId = cookies.get('sessionid');
	if (sessionId) {
		const user = await AuthService.getUserFromSession(sessionId);
		if (user) {
			throw redirect(303, '/account');
		}
	}
	return {};
};

export const actions = {
	register: async ({ cookies, request }) => {
		const data = await request.formData();
		const email = data.get('email');
		const phone = data.get('phone');
		const firstName = data.get('firstName');
		const lastName = data.get('lastName');
		const password = data.get('password');
		const username = data.get('username');
		const confirmPassword = data.get('confirmPassword');
		const billingAddress = data.get('billingAddress');

		// Create an errors object to track field-specific validation errors
		const errors: Record<string, string> = {};

		// Validate each field individually
		if (!firstName) {
			errors.firstName = 'First name is required';
		}

		if (!lastName) {
			errors.lastName = 'Last name is required';
		}

		if (!username) {
			errors.username = 'Username is required';
		} else if (!/^[a-zA-Z0-9_]+$/.test(username?.toString() || '')) {
			errors.username = 'Username must contain only letters, numbers, and underscores';
		} else if (username.toString().length < 3) {
			errors.username = 'Username must be at least 3 characters long';
		}

		if (!email) {
			errors.email = 'Email is required';
		} else if (!/^\S+@\S+\.\S+$/.test(email.toString())) {
			errors.email = 'Please enter a valid email address';
		}

		if (!phone) {
			errors.phone = 'Phone number is required';
		} else if (!/^\d{10}$/.test(phone.toString().replace(/\D/g, ''))) {
			errors.phone = 'Please enter a valid 10-digit phone number';
		}

		if (!password) {
			errors.password = 'Password is required';
		} else if (password.toString().length < 8) {
			errors.password = 'Password must be at least 8 characters long';
		}

		if (!confirmPassword) {
			errors.confirmPassword = 'Please confirm your password';
		} else if (password !== confirmPassword) {
			errors.confirmPassword = 'Passwords do not match';
		}

		// Return any validation errors
		if (Object.keys(errors).length > 0) {
			// Return values back to the form
			const formData = {
				firstName: firstName?.toString() || '',
				lastName: lastName?.toString() || '',
				username: username?.toString() || '',
				email: email?.toString() || '',
				phone: phone?.toString() || '',
				billingAddress: billingAddress?.toString() || ''
			};

			return {
				success: false,
				errors,
				formData
			};
		}

		// make sure the email is not already in use
		const existingUser = await db.getUser(email!.toString());
		if (existingUser) {
			errors.email = 'Email already in use';
		}

		// make sure the username is not already in use
		const existingUsername = await db.getUserByUsername(username!.toString());
		if (existingUsername) {
			errors.username = 'Username already in use';
		}

		// Return if there are any errors from the checks above
		if (Object.keys(errors).length > 0) {
			return {
				success: false,
				errors,
				formData: {
					firstName: firstName?.toString() || '',
					lastName: lastName?.toString() || '',
					username: username?.toString() || '',
					email: email?.toString() || '',
					phone: phone?.toString() || '',
					billingAddress: billingAddress?.toString() || ''
				}
			};
		}

		try {
			// Attempt to register
			const result = await AuthService.register({
				email: email!.toString(),
				username: username!.toString(),
				password: password!.toString(),
				firstName: firstName!.toString(),
				lastName: lastName!.toString(),
				phone: phone!.toString(),
				billingAddress: billingAddress?.toString()
			});

			// Handle null result from database operation
			if (!result || !result.user || !result.token) {
				return {
					success: false,
					errors: {
						form: 'Registration failed. Please try again later.'
					},
					formData: {
						firstName: firstName?.toString() || '',
						lastName: lastName?.toString() || '',
						username: username?.toString() || '',
						email: email?.toString() || '',
						phone: phone?.toString() || '',
						billingAddress: billingAddress?.toString() || ''
					}
				};
			}

			// Set session cookie
			cookies.set('session_id', result.session.id, {
				path: '/',
				httpOnly: true,
				sameSite: 'strict',
				secure: process.env.NODE_ENV === 'production',
				maxAge: 60 * 60 * 24 * 7 // 7 days
			});

			return {
				success: true,
				redirect: '/account/@me'
			};
		} catch (error) {
			console.error('Registration error:', error);
			return {
				success: false,
				errors: {
					form: 'An error occurred during registration. Please try again later.'
				},
				formData: {
					firstName: firstName?.toString() || '',
					lastName: lastName?.toString() || '',
					username: username?.toString() || '',
					email: email?.toString() || '',
					phone: phone?.toString() || '',
					billingAddress: billingAddress?.toString() || ''
				}
			};
		}
	}
} satisfies Actions;
