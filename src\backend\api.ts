import type { RequestEvent } from '@sveltejs/kit';
import { db } from './db';
import { readFile } from 'fs/promises';
import { join } from 'path';
import { AuthService } from './auth';

type BucketId = 'BUCKET_3' | 'BUCKET_4';

const RATE_LIMITS: Record<BucketId, { limit: number; refill: number }> = {
	BUCKET_3: { limit: 1, refill: 5000 }, // 1 request per 5 seconds
	BUCKET_4: { limit: 36, refill: 60000 } // 36 requests per minute
};

const buckets = new Map<BucketId, { tokens: number; lastRefill: number }>();

function getBucket(id: BucketId) {
	if (!buckets.has(id)) {
		buckets.set(id, { tokens: RATE_LIMITS[id].limit, lastRefill: Date.now() });
	}
	return buckets.get(id)!;
}

function refillBucket(id: BucketId) {
	const bucket = getBucket(id);
	const now = Date.now();
	const timePassed = now - bucket.lastRefill;
	const refillAmount = Math.floor(timePassed / RATE_LIMITS[id].refill);

	if (refillAmount > 0) {
		bucket.tokens = Math.min(RATE_LIMITS[id].limit, bucket.tokens + refillAmount);
		bucket.lastRefill = now;
	}
}

function checkRateLimit(id: BucketId): { allowed: boolean; retryAfter: number } {
	refillBucket(id);
	const bucket = getBucket(id);

	if (bucket.tokens > 0) {
		bucket.tokens--;
		return { allowed: true, retryAfter: 0 };
	}

	return {
		allowed: false,
		retryAfter: RATE_LIMITS[id].refill - (Date.now() - bucket.lastRefill)
	};
}

function getAuthToken(event: RequestEvent): string | null {
	const authHeader = event.request.headers.get('Authorization');
	if (!authHeader?.startsWith('Bearer ')) return null;
	return authHeader.slice(7);
}

function validateDomainIntent(event: RequestEvent): boolean {
	const intent = event.request.headers.get('X-Domain-Intent');
	return intent === 'agent' || intent === 'bot' || intent === 'user' || intent === 'app';
}

function jsonResponse(data: any, status = 200, headers: Record<string, string> = {}) {
	return new Response(JSON.stringify(data), {
		status,
		headers: {
			'Content-Type': 'application/json',
			...headers
		}
	});
}

function errorResponse(message: string, status = 400) {
	return jsonResponse({ status, message }, status);
}

async function getLegalDocument(docType: string): Promise<string | null> {
	try {
		const docPath = join(process.cwd(), 'assets', 'terms', `${docType.toUpperCase()}.md`);
		const content = await readFile(docPath, 'utf-8');
		return content;
	} catch (error) {
		console.error('[!] Error reading legal document:', error);
		return null;
	}
}

export async function handleApiRequest(event: RequestEvent) {
	const { pathname } = event.url;

	if (!validateDomainIntent(event)) {
		return errorResponse('Invalid domain intent', 403);
	}

	if (
		pathname.match(/^\/api\/legal\/(privacy|repair-terms|terms)$/) &&
		event.request.method === 'GET'
	) {
		try {
			const docType = pathname.match(/^\/api\/legal\/(privacy|repair-terms|terms)$/)?.[1];
			const content = await getLegalDocument(docType!);
			if (!content) {
				return errorResponse('Legal document not found', 404);
			}
			return jsonResponse({ content }, 200);
		} catch (err) {
			console.error('Error fetching legal document:', err);
			return errorResponse('Failed to fetch legal document', 500);
		}
	}

	const token = getAuthToken(event);
	if (!token) {
		return errorResponse('Missing authorization token', 401);
	}

	// validate the token
	const decoded = AuthService.verifyJWT(token);
	if (!decoded) {
		return errorResponse('Invalid authorization token', 401);
	}

	const bucketId: BucketId =
		event.url.pathname.includes('/users') && !event.url.pathname.includes('/transactions')
			? 'BUCKET_4'
			: 'BUCKET_3';

	const rateLimit = checkRateLimit(bucketId);
	if (!rateLimit.allowed) {
		return new Response(null, {
			status: 429,
			headers: {
				'Retry-After': Math.ceil(rateLimit.retryAfter / 1000).toString(),
				'X-Rate-Limit-Bucket-ID': bucketId,
				'X-Rate-Limit-BUCKET-REMAINING': '0',
				'X-Rate-Limit-BUCKET-REFILL-IN': Math.ceil(rateLimit.retryAfter / 1000).toString(),
				'X-Rate-Limit-BUCKET-FULL-AFTER': (RATE_LIMITS[bucketId].refill / 1000).toString()
			}
		});
	}

	// GET /users/{id}
	if (pathname.match(/^\/users\/[^/]+$/) && event.request.method === 'GET') {
		const userId = pathname.split('/')[2];
		const user = await db.getUser(userId);

		if (!user) {
			return errorResponse('User not found', 404);
		}

		return jsonResponse(user, 201);
	}

	// GET /users/{id}/transactions
	if (pathname.match(/^\/users\/[^/]+\/transactions$/) && event.request.method === 'GET') {
		const userId = pathname.split('/')[2];
		const transactions = await db.getUserTransactions(userId);

		return jsonResponse(transactions, 201);
	}

	// GET /users
	if (pathname === '/users' && event.request.method === 'GET') {
		const params = event.url.searchParams;
		const size = Math.min(parseInt(params.get('size') || '2000'), 2000);
		const chunk = parseInt(params.get('chunk') || '0');
		const sensitive = params.get('sensitive') === 'true';

		const users = await db.getUsers(size, chunk * size, sensitive);

		return jsonResponse(
			{
				redacted: sensitive ? [] : ['email', 'permissions', 'roles', 'updated_at', 'provider'],
				users
			},
			201,
			{
				'X-NEXT-CHUNK': (chunk + 1).toString(),
				'X-RATE-LIMIT-BUCKET-ID': bucketId,
				'X-RATE-LIMIT-BUCKET-REMAINING': (RATE_LIMITS[bucketId].limit - 1).toString(),
				'X-RATE-LIMIT-BUCKET-REFILL-IN': (RATE_LIMITS[bucketId].refill / 1000).toString()
			}
		);
	}

	// Handle 404 for unknown routes
	return errorResponse('Not found', 404);
}
