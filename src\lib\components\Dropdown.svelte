<script lang="ts">
	import { slide } from 'svelte/transition';

	let { title, isOpen, onToggle, children } = $props<{
		title: string;
		isOpen: boolean;
		onToggle: () => void;
		children: any;
	}>();

	let buttonClasses = $derived(`flex w-full items-center justify-between p-6 text-left`);
	let iconClasses = $derived(
		`h-5 w-5 text-[var(--text-secondary)] transition-transform duration-300 ${isOpen ? 'rotate-180' : ''}`
	);
</script>

<div
	class="overflow-hidden rounded-xl bg-[var(--card-bg)] transition-all duration-300"
	class:shadow-lg={isOpen}
>
	<button class={buttonClasses} onclick={onToggle}>
		<h3 class="text-lg font-semibold text-[var(--text-primary)]">{title}</h3>
		<span class="icon-[mingcute--up-fill] {iconClasses}"></span>
	</button>

	{#if isOpen}
		<div class="px-6 pb-6 text-[var(--text-secondary)]" transition:slide={{ duration: 300 }}>
			{@render children()}
		</div>
	{/if}
</div>
