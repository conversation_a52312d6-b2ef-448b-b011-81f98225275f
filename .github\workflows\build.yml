name: Build Check
on:
  workflow_dispatch:
    inputs:
      branch:
        description: 'Branch to build'
        required: true
        default: 'main'
        type: choice
        options:
          - main
jobs:
  Build:
    runs-on: ubuntu-latest
    if: github.event_name == 'push' || (github.event_name == 'workflow_dispatch' && github.event.inputs.branch == 'main')
    steps:
      - name: Checkout Repository
        uses: actions/checkout@v4
      - name: Setup Node.js environment
        uses: actions/setup-node@v4.1.0
      - name: Install Dependencies
        run: npm i
      - name: Build
        run: npm run build
      - name: Upload build artifact
        uses: actions/upload-artifact@v4
        with:
          name: build
          path: build/
          retention-days: 1
