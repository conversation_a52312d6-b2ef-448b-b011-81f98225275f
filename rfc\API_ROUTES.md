# API Specifications 1.0.0

This document goes over all api endpoints for the 1.0.0.

> This is a provisional document, it will be changed

## Sections

1. [Authorization](#authorization)
2. [Endpoints](#Endpoints)
3. [Types](#types)

## Authorization

There are 3 ways which you can access our API:

1. As a user (via texrepairs.com)
2. As a entity (3rd party)
3. Via oauth2, (the user authorizes it in their settings)

**General Authorization Method:**

Texrepairs uses _rotating_ Bearer tokens to identify an individual, entity or oauth app. All requests for all authorization methods must follow the following structure for each request made to the API:

```http
GET / HTTP/3.0
Authorization: "Bearer <TOKEN>"
Content-Type: application/json
X-Domain-Intent: "agent"
```

The response from the server will vary, however in a success scenario you should see:

```http
HTTP/3.0 200 OK
Content-Type: application/json

{
    "user_id": "0",
    "type": 1,
    "expires_at": 0,
}
```

Header details:

| Headers           | Description                                                                                                                                                                                                                                                           | Default?           |
| ----------------- | --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | ------------------ |
| `Authorization`   | The authorization header token serves as a way for the webservice to communicate with the API. Possible tokens include: `User`, `Bearer`, and `Entity`                                                                                                                | N/A                |
| `Content-Type`    | The type of request/response structure the application expects, this can be one of the following: `application/x-www-form-urlencoded`, ` application/octet-stream`, `application/texrepair-nd` or `application/json`                                                  | `application/json` |
| `X-Domain-Intent` | The intent of the third party. This must be authorized by Texrepairs Auth Guard, if the intent is not authorized, then your request will return a [403 Forbidden](). For more information on this, please see [Authorized Entity Intents](#authorized-entity-intents) | `bot`              |

**Authorizing as a third party**

Third party providers have additional access to the Texrepairs API granted by us at our discretion for business purposes only. A third party is required to have the following additional information on each request when accessing _sensitive data_:

| Header                | Description                                                                                                                                                                                                                                                                                                             | Optional? |
| --------------------- | ----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | --------- |
| `X-Reporting-Service` | The name of your internal service you are reporting this information to, if not provided, you will see failures in services that require this field.                                                                                                                                                                    | No        |
| `X-Server-Clock`      | The current unix epoch of the machine that made the request.                                                                                                                                                                                                                                                            | No        |
| `X-Intent-To-Store`   | Whether or not the service intends on storing this information on their machine, if so, the Texrepairs API will provide a `ASSIGNED-CONTENT-ID` associated with the response, that the server is expected to associate that content with. (Failure to comply with this standard is a violation of our terms of service) | Yes       |

# Endpoints

Endpoints are your way to access data from texrepairs via the API.

## GET `/users/{user.id}`

Retrieves any [User](#User) from the API by its id.

#### Authorization:

You must, at the very minimum, have a valid token. You will be able to view any visible information for a user as long as that data is public facing and not private.

> [!IMPORTANT]
>
> If the user profile is private, then you will need to at least have the permissions: `db.users.read` or `user.read.any`

#### `201` Response Example

```http
HTTP/3.0 201 OK
Content-Type: application/json
Content-Length: 318

{
    "id": "182899018744108068",
    "username": "john_doe",
    "email": "<EMAIL>",
    "language": "en-US",
    "roles": [
        "guest",
        "beta"
    ],
    "permissions": [
        "rollouts.beta.themes"
    ],
    verified: true,
    updated_at: 0,
    provider: "texrepairs"
}
```

#### `403` Response Example

```http
HTTP/3.0 403 Forbidden
Content-Type: application/json

{
    "status": 403
    "message": "Forbidden"
}
```

## GET `/users/{user.id}/transactions`

Gets a users [Transaction]() history on the store. This includes:

- Purchases for software or licenses
- Any purchase for real goods
- Returns and chargebacks

#### Authorization

This endpoint requires the `user.transactions` scope.

### `201` Response Example

```http
HTTP/3.0 201 OK
Content-Type: application/json

[
    {
        "id": "00000001",
        "type": "charge",
        "amount": 9900,
        "refunded": false,
        "currency": "usd",
        "receipt": null,
        "provider": "stripe",
        "intent": ""
    }
]
```

## GET `/users`

Gets a list of all users ever registered to Texrepairs LLC services.

> [!CAUTION]
> This endpoint is cautiously monitored by Texrepairs, we impose the following restrictions:
>
> - Heavily rate-limits to third parties or sole developers. ([BUCKET-4](#buckets)) For limit increases please visit https://texrepairs.com/support.
> - Data on the user object is truncated, to fetch sensitive information such as their email, roles, or permissions you must individually query that user or use the `sensitive` parameter.
> - Access information is stored for 90 days from any party, which includes the SHA-256 Hash of the response body as well as the timestamp the request was sent and the entity that was authorized to receive it, by ID.

### Parameters

| Name         | Type   | Description                                                                                                                      |
| ------------ | ------ | -------------------------------------------------------------------------------------------------------------------------------- |
| size         | `int`  | The amount of users you want to retrieve on a single request. The maximum for this size is `2000`.                               |
| chunk        | `int`  | The page you want to fetch.                                                                                                      |
| \*sensitive  | `bool` | Whether or not to include sensitive user information in the request.                                                             |
| \*use_secret | `bool` | Whether or not to use your client secret to encrypt the response on the web-server. (Automatically `true` if `sensitive` is set) |

### `201` Response Example

```http
HTTP/3.0 201 OK
Content-Type: application/json
X-NEXT-CHUNK: 2
X-RATE-LIMIT-BUCKET-ID: BUCKET-4
X-RATE-LIMIT-BUCKET-REMAINING: 30
x-RATE-LIMIT-BUCKET-REFILL-IN: 60

{
	"redacted": [
		"email",
		"permissions",
		"roles",
		"updated_at",
		"provider"
	],
	"users": [
        {
           "id": "182899018744108068",
           "username": "john_doe",
           "language": "en-US",
           "verified": true,
        }
	]
}
```

# Rate Limit Buckets

Rate limit buckets is how we rate limit our users access to our API. In short, they are a buckets that fill up with "tokens" / "ids" at a constant rate. Each request you make with take out one id/token from this bucket. When the bucket is empty, the your requests are blocked until the next token/id gets added back to the bucket.

1. The rate at which your token's refill is defined as the `refill_interval` on our API.
2. The physical amount of requests you can send in a single bucket is called the `limit`.

The following is an example of the http headers sent back on a successful request:

```http
HTTP/3.0 201 OK
X-RATE-LIMIT-BUCKET-ID: BUCKET-9
X-RATE-LIMIT-BUCKET-REMAINING: 30
x-RATE-LIMIT-BUCKET-REFILL-IN: 60
```

In the event you get rate limited the following headers are sent back:

```http
HTTP/3.0 429 Too Many Requests
RETRY-AFTER: 5
X-RATE-LIMIT-BUCKET-ID: BUCKET-4
X-RATE-LIMIT-BUCKET-REMAINING: 0
X-RATE-LIMIT-BUCKET-REFILL-IN: 5
X-RATE-LIMIT-BUCKET-FULL-AFTER: 60
```

> [!NOTE]
> The `Retry-After` header is strictly an indicator to the user about the amount of time you must wait until you can physically send one more request to the API. It does NOT include the amount of time until your bucket is fully refilled, for that you must use `X-RATE-LIMIT-BUCKET-FULL-AFTER`.

### Global Rate Limit

When the API service is 100% healthy and no services are degraded the global limit will be set to:

| Variable | Value  |
| -------- | ------ |
| `limit`  | `1800` |
| `refill` | `60s`  |

This means you can send a total of `1800` requests at once, but achieve a steady 30 requests per second.

#### Limits during status: `Degraded`

| Variable | Value    |
| -------- | -------- |
| `limit`  | `1200`   |
| `refill` | `1m 30s` |

During these times you can only send `1200` requests at once, but achieve a steady `13` requests a second until all systems are `Operational`

#### Limits during status: `Maintenance`

| Variable | Value    |
| -------- | -------- |
| `limit`  | `500`    |
| `refill` | `1m 30s` |

During these times you can only send `500` requests at once, but achieve a steady `5` requests a second until all systems are `Operational`

#### Limits during status: `Critical`

| Variable | Value    |
| -------- | -------- |
| `limit`  | `10`     |
| `refill` | `1m 30s` |

During these times you can only send `10` requests at once, but send a request every `7 seconds` until all systems are `Operational`

> [!WARNING]
> In some cases, we may completely restrict API access to all parties, setting the global limit to `0`.

### Buckets

Some requests fall in different "bucket" categories, we have 4 different buckets with different limits:

| Bucket   | `limit` | `refill` | Description                                                                         |
| -------- | ------- | -------- | ----------------------------------------------------------------------------------- |
| BUCKET-3 | `1`     | `5s`     | This is the default bucket for our customers regularly using our website.           |
| BUCKET-4 | `36`    | `60s`    | Access to private, or otherwise "sensitive" information regarding a user or entity. |

# Types

This index houses most types useful for the API, most of these are structured aliases or interfaces.

## User

| Field       | Type                         | Required Scopes | Description                                                                                                                                                                                     |
| ----------- | ---------------------------- | --------------- | ----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| id          | [`BoltId`]()                 | N/A             | The id for the user.                                                                                                                                                                            |
| username    | `string`                     | N/A             | The username for the user. Max length is 22 chars.                                                                                                                                              |
| email       | `string`                     | `user.identity` | The email the user has on their account.                                                                                                                                                        |
| language    | [`Language`](#type-language) | `user.identity` | The language the user has set for their account.                                                                                                                                                |
| roles       | `string[]`                   | `user.identity` | The roles this user has, IE: `guest` or `support`                                                                                                                                               |
| permissions | `scope[]`                    | `user.scopes`   | The permissions specifically granted to this user, while this generally remains the same for all users, this can vary based on if the user has paid for certain items or is enrolled in beta's. |
| verified    | `bool`                       | N/A             | Whether or not the users account has been verified completely either by 2FA or Email.                                                                                                           |
| updated_at  | `epoch`                      | N/A             | The time the user was last updated.                                                                                                                                                             |
| provider    | `string`                     | `user.identity` | The provider the user used to login with. If Texrepairs was used the provider is `texrepairs` otherwise it's one of: `apple`, `google` or `github`.                                             |

## BoltId

A BoltId is a 64bit unsigned integer with embedded metadata for time, randomness and origin/type data.

Structure:

| Bits | Field           | Description                                                                        |
| ---- | --------------- | ---------------------------------------------------------------------------------- |
| 42   | Timestamp       | The time the id was created                                                        |
| 10   | Node ID         | Identifies the machine, region or service, often string encoded.                   |
| 12   | Randomness Bits | Adds entropy to avoid collisions with other snowflakes generated at the same time. |

## Language

The following strings are valid languages currently supported by Texrepairs:

1. `en-US`
2. `en-AU`
3. `en-GB`
4. `es-MX`
5. `es-ES`
6. `fr-CA`
7. `fr-FR`
8. `de-DE`
9. `de-AT`

## Transaction

A transaction is a record that records any financial operation between a customer and Texrepairs LLC.

Structure:

| Field                | Type                                   | Required Scopes             | Description                                                                                                                             |
| -------------------- | -------------------------------------- | --------------------------- | --------------------------------------------------------------------------------------------------------------------------------------- | -------------------------------------------------------------------------------------------------------------------------------------- |
| id                   | `string`                               | `users.transactions.view`   | An identifier for this transaction. In the event of charge-backs or refunds this is the ID used as an association for proof of delivery |
| type                 | [`TransactionType`](#transaction-type) | N/A                         | Defines how the `amount` should be interpreted.                                                                                         |
| amount               | `u64`                                  | N/A                         | Unit of _currency_ that was posted during this transaction.                                                                             |
| amount_refunded      | `u64`                                  | N/A                         | Unit of _currency_ that was refunded for this transaction.                                                                              |
| currency             | `string`                               | N/A                         | The currency code for the exchange that was used. IE `USD`.<br />Currently, only `USD` is supported here.                               |
| provider             | `string`                               | `admin.transactions`        | The provider used for this transaction. If you are missing access for this field `internal` is provided.                                |
| billing              | [`BillingAddress`](#billing-address)   | `users.sensitive_data.view` | The billing address associated with this transaction.                                                                                   |
| created_at           | `u64`                                  | N/A                         | Unix epoch of when the transaction occurred. Measured in seconds.                                                                       |
| status               | `string`                               | N/A                         | The state of the transaction and all its details                                                                                        |
| payment_method       | `PaymentMethod`                        | `users.sensitive_data.view` | The payment method for this transaction.                                                                                                |
| paid                 | `bool`                                 | N/A                         | `true` if the charge succeeded, or if it was successfully authorized. `false` if otherwise.                                             |
| fraud_factor         | [`FraudFactor`]()                      | `null`                      | N/A                                                                                                                                     | Information regarding the fraud analysis of this charge, set by our providers, our fraud intelligence, and manually by support agents. |
| delivery_id          | `string`                               | `null`                      | N/A                                                                                                                                     | Delivery ID for this purchase (if applicable).                                                                                         |
| item_id              | `string`                               | N/A                         | ID of the item purchased. You must look this item up for a description of the product/service etc.                                      |
| statement_descriptor | `string`                               | N/A                         | This is the string that shows up on the customers bank account. IE: `TEXREPAIRS LLC`.                                                   |

### Transaction Type

A transaction type is a `string` that can be only one of the following:

| Variant              | Description                                                                                                                                                                                                                                    |
| -------------------- | ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| `charge`             | One time payment associated with a product or service.                                                                                                                                                                                         |
| `subscription`       | Recurring payment for ongoing access to our products or services.                                                                                                                                                                              |
| `refund`             | Reversal of a prior transaction.                                                                                                                                                                                                               |
| `chargeback`         | Disputed payment forcibly by the provider, usually in the case of fraud or user discretion.                                                                                                                                                    |
| `provisional_charge` | This is a temporary transaction that is applied to a customer's balance to offset a charge. This is a negative value, and carries the same weight as a `charge`. This is usually applied during disputes through our ticketing support system. |
| `provisional_refund` | A temporary adjustment to a customer's balance in cases of pending transactions, this is usually only applied during disputes through our ticketing support system.                                                                            |

# Database Schema:

```sql
-- Users table
CREATE TABLE IF NOT EXISTS Users (
    -- Id comes in the format of UUID
    -- This is stored in sessions and is used across the platform to reference this user
    -- This does not in any way represent a token and can not be used to access session info.
    id                     text PRIMARY KEY NOT NULL,
    -- This can be null for the time being cause we are UNSURE of how this will look in the future
    -- I have no idea if we have the intention of designing accounts around usernames.
    username               varchar(22),
    email                  text NOT NULL,
    language               text NOT NULL,
    -- Site roles is an array of generally available roles.
    -- The site will validate these roles and prune invalid ones.
    roles                  text[],
    -- These are the scopes specifically granted to this user.
    -- usually a result of a site admin
    permissions            text[],
    -- Whether or not the email on this account has been verified. If this is false,
    -- The user still needs to verify their email, and a token will be present in
    -- AccountsPending
    verified               Boolean NOT NULL,
    created_at             timestamp(3) DEFAULT NOW()::TIMESTAMP(3),
    updated_at             timestamp(3) DEFAULT NOW()::TIMESTAMP(3),
    -- the provider of the user
    provider               text NOT NULL,
    -- Whether or not the account is pending deletion.
    -- This could be set by the user or an administrator.
    deletion_timestamp     timestamp(3) DEFAULT NULL,
    -- Whether or not the account is pending deletion.
    pending_deletion       Boolean NOT NULL DEFAULT false
);

-- Scopes table
CREATE TABLE IF NOT EXISTS Scopes (
    -- The scope is a string that represents the scope of the application.
    -- For example: user.read, user.write, etc.
    name                  text PRIMARY KEY NOT NULL,
    description           text,
    -- This is whether or not the scope is required for the web application.
    -- If it is, it can not be removed from the application.
    required              boolean NOT NULL DEFAULT false,
    created_at            timestamp(3) DEFAULT NOW()::TIMESTAMP(3),
    updated_at            timestamp(3) DEFAULT NOW()::TIMESTAMP(3)
);

-- EmailTokens table
CREATE TABLE IF NOT EXISTS EmailTokens (
    user_id                text PRIMARY KEY NOT NULL,
    -- This is a token that is used to verify the email of a user.
    -- This is a one time use token.
    token                  text NOT NULL,
    -- The time the token was created at.
    created_at             timestamp(3) NOT NULL,
    -- The time the token expires at.
    expires_at             timestamp(3) NOT NULL,
    CONSTRAINT fk_user FOREIGN KEY(user_id)
        REFERENCES Users(id)
        ON DELETE CASCADE
);

-- Roles table
CREATE TABLE IF NOT EXISTS Roles (
    id                    int PRIMARY KEY NOT NULL,
    -- The name of the role
    name                  text NOT NULL,
    -- The description of the role
    description           text,
    -- The permissions of the role
    permissions           text[],
    -- The parent role of the role
    parent_id             int,
    created_at            timestamp(3) DEFAULT NOW()::TIMESTAMP(3),
    updated_at            timestamp(3) DEFAULT NOW()::TIMESTAMP(3),
    CONSTRAINT fk_parent FOREIGN KEY(parent_id)
        REFERENCES Roles(id)
        ON DELETE CASCADE
);

-- Applications table (depends on Users)
CREATE TABLE IF NOT EXISTS Applications (
    id                    varchar(32) PRIMARY KEY NOT NULL,
    name                  text NOT NULL,
    icon                  text,
    secret                text NOT NULL,
    -- This is a list of VALID URI's that the application
    -- allows. If there are none allowed, then this application will reject
    -- any attempts to verify.
    redirect_uri          text[] NOT NULL,
    description           text,
    -- Whether or not the author is allowing users to use this application.
    enabled               boolean,
    -- This is a entity status object
    -- It represents the state of the application.
    status                jsonb,
    -- These are internal options for a Application set by VLP, such as status.
    -- This is mainly used to ban an application.
    owner_id             text NOT NULL,
    -- The scopes applied by a site admin, these can be used.
    -- By default none are set all need to be requested
    permissions          text[],
    -- The scopes enabled by the developer.
    scopes               text[],
    -- The date the application was created at.
    created_at           timestamp(3) DEFAULT NOW()::TIMESTAMP(3),
    -- The time the application expires (if at all)
    expires_at           timestamp(3) DEFAULT NULL,
    -- Application will be deleted when the user is deleted!
    -- This is important because company apps will stop working if the associated
    -- user account is removed.
    CONSTRAINT fk_owner FOREIGN KEY(owner_id)
        REFERENCES Users(id)
        ON DELETE CASCADE
);

-- Sessions table
CREATE TABLE IF NOT EXISTS Sessions (
    id                    int PRIMARY KEY NOT NULL,
    -- This is a token that is used to verify the email of a user.
    -- This is a one time use token.
    token                 text NOT NULL,
    -- The time the token was created at.
    created_at            timestamp(3) DEFAULT NOW()::TIMESTAMP(3),
    -- The time the token expires at.
    expires_at            timestamp(3) DEFAULT NULL,
    -- The time the token was last used at.
    last_used             timestamp(3) DEFAULT NULL,
    -- The owner of the session
    owner_id              text NOT NULL,
    -- The type of owner
    owner_type            text NOT NULL CHECK (owner_type IN ('user', 'application'))
);

-- AccessTokens table
CREATE TABLE IF NOT EXISTS AccessTokens (
    id                    int PRIMARY KEY NOT NULL,
    access_token          text NOT NULL,
    user_id               text NOT NULL,
    client_id             text NOT NULL,
    expires               bigint NOT NULL,
    scopes                text[] NOT NULL,
    created_at            bigint NOT NULL,
    updated_at            bigint NOT NULL
);
```
