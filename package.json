{"name": "texrepairs-website", "version": "1.0.0", "private": true, "type": "module", "scripts": {"dev": "vite dev", "build": "vite build", "preview": "vite preview", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --fail-on-warnings --compiler-warnings \"css_unused_selector:ignore,a11y_missing_attribute:error\"", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch", "format": "prettier --write .", "fmt": "npm run format", "lint": "prettier --check .", "prepare": "husky", "run": "", "setup": "node ./scripts/setup_psql.js", "migrate": "node ./scripts/migrations.js"}, "devDependencies": {"@iconify-json/f7": "^1.2.2", "@iconify-json/ic": "^1.2.2", "@iconify-json/la": "^1.2.1", "@iconify-json/lets-icons": "^1.2.1", "@iconify-json/line-md": "^1.2.5", "@iconify-json/material-symbols": "^1.2.23", "@iconify-json/mdi": "^1.2.3", "@iconify-json/mingcute": "^1.2.3", "@iconify-json/nrk": "^1.2.3", "@iconify-json/qlementine-icons": "^1.2.5", "@iconify-json/ri": "^1.2.5", "@iconify/tailwind": "^1.2.0", "@internationalized/date": "^3.7.0", "@sveltejs/adapter-node": "^5.2.12", "@sveltejs/kit": "^2.17.1", "@sveltejs/vite-plugin-svelte": "^5.0.1", "@types/nodemailer": "^6.4.17", "@types/pg": "^8.15.1", "autoprefixer": "^10.4.20", "bits-ui": "^1.3.5", "chalk": "^5.4.1", "clsx": "^2.1.1", "dotenv": "^16.5.0", "embla-carousel-svelte": "^8.5.2", "formsnap": "^2.0.0", "husky": "^9.1.7", "lucide-svelte": "^0.477.0", "mdsvex": "^0.12.3", "mode-watcher": "^0.5.1", "paneforge": "1.0.0-next.4", "prettier": "^3.4.2", "prettier-plugin-svelte": "^3.3.2", "prettier-plugin-tailwindcss": "^0.6.9", "sass": "^1.83.0", "sass-embedded": "^1.83.0", "svelte": "^5.20.5", "svelte-check": "^4.1.1", "svelte-language-server": "^0.17.8", "svelte-preprocess": "^6.0.3", "svelte-sonner": "^0.3.28", "sveltekit-superforms": "^2.23.1", "tailwind-merge": "^3.0.2", "tailwind-variants": "^0.3.1", "tailwindcss": "^3.4.17", "tailwindcss-animate": "^1.0.7", "typescript": "^5.7.2", "vaul-svelte": "1.0.0-next.6", "vite": "^6.0.3", "zod": "^3.24.2"}, "dependencies": {"@sendgrid/mail": "^8.1.5", "@tailwindcss/typography": "^0.5.15", "@tailwindcss/vite": "^4.0.5", "@texrepairs/website": "file:", "@types/bcrypt": "^5.0.2", "@types/jsonwebtoken": "^9.0.5", "@types/node": "^22.10.2", "@types/nprogress": "^0.2.3", "bcrypt": "^6.0.0", "github-markdown-css": "^5.8.1", "jsonwebtoken": "^9.0.2", "marked": "^15.0.7", "marked-alert": "^2.1.2", "marked-gfm-heading-id": "^4.1.1", "nodemailer": "^6.10.0", "nprogress": "^0.2.0", "pg": "^8.15.6", "redis": "^4.7.0", "texrepairs-website": "file:", "uuid": "^11.0.5"}}