<script lang="ts">
	import type { PageData } from './$types';
	import Navbar from '$lib/components/nav/Navbar.svelte';
	import Profile from './pages/Profile.svelte';
	import Security from './pages/Security.svelte';
	import Notifications from './pages/Notifications.svelte';
	import Billing from './pages/Billing.svelte';
	import ManageAccount from './pages/ManageAccount.svelte';

	interface Props {
		data: PageData;
	}

	let { data } = $props();
	let activeTab = $state('profile');

	const tabs = [
		{ id: 'profile', label: 'Profile', icon: 'icon-[mingcute--user-4-line]' },
		{ id: 'security', label: 'Security', icon: 'icon-[mingcute--lock-line]' },
		{ id: 'notifications', label: 'Notifications', icon: 'icon-[mingcute--notification-line]' },
		// { id: 'billing', label: 'Billing', icon: 'icon-[mingcute--bill-line]' },
		{ id: 'manage-account', label: 'Manage Account', icon: 'icon-[mingcute--settings-3-line]' }
	];

	let notificationPreferences = {
		emailNotifications: true,
		serviceUpdates: true
	};

	let formData = {
		firstName: data.sessionUser?.user_info?.first_name || '',
		lastName: data.sessionUser?.user_info?.last_name || '',
		email: data.sessionUser?.email || '',
		phone_number: data.sessionUser?.user_info?.phone_number || '',
		notifications: {
			email: true,
			sms: false,
			marketing: false
		},
		verified: data.sessionUser?.verified ?? false
	};

	function handleSubmit(event: Event) {
		event.preventDefault();
	}
</script>

<Navbar />

<div class="min-h-screen">
	<div class="mx-auto max-w-7xl px-4 py-10 sm:px-6 lg:px-8">
		<!-- Header -->
		<div class="mb-8 flex items-center justify-between">
			<div class="flex items-center gap-4">
				<a
					href="/account/@me"
					aria-label="Go back to account"
					class="flex h-10 w-10 items-center justify-center"
				>
					<span class="icon-[mingcute--arrow-left-line] h-5 w-5 text-[var(--text-primary)]"></span>
				</a>
				<h1 class="text-3xl font-bold text-[var(--text-primary)]">Account Settings</h1>
			</div>
			<div class="flex items-center gap-4">
				<div class="flex items-center gap-3">
					<div
						class="flex h-10 w-10 items-center justify-center rounded-full bg-gradient-to-br from-[var(--btn-primary)] to-[var(--tr-blue-alt)]"
					>
						<span class="icon-[mingcute--user-4-fill] h-5 w-5 text-white"></span>
					</div>
					<div class="hidden sm:block">
						<p class="text-sm font-medium text-[var(--text-primary)]">
							{data.sessionUser?.user_info?.first_name}
							{data.sessionUser?.user_info?.last_name}
						</p>
						<p class="text-xs text-[var(--text-secondary)]">{data.sessionUser?.email}</p>
					</div>
				</div>
			</div>
		</div>

		<div class="grid grid-cols-12 gap-6">
			<!-- Sidebar -->
			<div class="col-span-12 lg:col-span-3">
				<div class="rounded-2xl bg-[var(--card-bg)] shadow-sm">
					<nav class="p-2">
						{#each tabs as tab}
							<button
								class="flex w-full items-center gap-3 rounded-xl px-4 py-3.5 text-left transition-all {activeTab ===
								tab.id
									? 'bg-[var(--btn-primary)] text-white'
									: 'text-[var(--text-secondary)] hover:bg-[var(--card-container-bg)]'}"
								onclick={() => (activeTab = tab.id)}
							>
								<span class="{tab.icon} h-5 w-5"></span>
								<span class="font-medium">{tab.label}</span>
							</button>
						{/each}
					</nav>
				</div>
			</div>

			<!-- Main Content -->
			<div class="col-span-12 lg:col-span-9">
				<div class="rounded-2xl bg-[var(--card-bg)] p-6 shadow-sm">
					{#if activeTab === 'profile'}
						<Profile {formData} {handleSubmit} />
					{:else if activeTab === 'security'}
						<Security {handleSubmit} />
					{:else if activeTab === 'notifications'}
						<Notifications {formData} {handleSubmit} />
					{:else if activeTab === 'billing'}
						<Billing />
					{:else if activeTab === 'manage-account'}
						<ManageAccount />
					{/if}
				</div>
			</div>
		</div>
	</div>
</div>

<style lang="scss">
	.settings-container {
		max-width: 800px;
		margin: 0 auto;
		padding: 2rem;
	}

	.settings-nav {
		display: flex;
		align-items: center;
		gap: 1rem;
		margin-bottom: 2rem;

		.back-button {
			display: flex;
			align-items: center;
			gap: 0.5rem;
			padding: 0.5rem;
			color: var(--text-primary);
			background: none;
			border: none;
			cursor: pointer;
			font-size: 1rem;

			&:hover {
				color: var(--btn-primary);
			}
		}

		h1 {
			font-size: 1.5rem;
			font-weight: 600;
			margin: 0;
		}
	}

	.tabs {
		display: flex;
		gap: 1rem;
		margin-bottom: 2rem;
		border-bottom: 1px solid var(--border-color);
		padding-bottom: 1rem;

		button {
			padding: 0.5rem 1rem;
			background: none;
			border: none;
			color: var(--text-secondary);
			cursor: pointer;
			font-size: 1rem;
			position: relative;

			&.active {
				color: var(--btn-primary);

				&::after {
					content: '';
					position: absolute;
					bottom: -1rem;
					left: 0;
					width: 100%;
					height: 2px;
					background-color: var(--btn-primary);
				}
			}

			&:hover {
				color: var(--btn-primary);
			}
		}
	}

	.tab-content {
		background: var(--bg-secondary);
		border-radius: 0.5rem;
		padding: 2rem;
	}

	.profile-form,
	.security-form,
	.notifications-form {
		display: flex;
		flex-direction: column;
		gap: 1.5rem;
	}

	.form-grid {
		display: grid;
		grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
		gap: 1.5rem;
	}

	.form-actions {
		margin-top: 1rem;
		display: flex;
		justify-content: flex-end;
	}

	.security-content,
	.notifications-content,
	.billing-content {
		h2 {
			font-size: 1.25rem;
			font-weight: 600;
			margin-bottom: 1.5rem;
		}
	}

	@media (max-width: 768px) {
		.settings-container {
			padding: 1rem;
		}

		.tabs {
			overflow-x: auto;
			padding-bottom: 0.5rem;
		}

		.tab-content {
			padding: 1rem;
		}
	}
</style>
