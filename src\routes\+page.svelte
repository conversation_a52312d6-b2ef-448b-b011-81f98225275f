<script lang="ts">
	import Navbar from '$lib/components/nav/Navbar.svelte';
	import Card from '$lib/components/Card.svelte';
	import AboutUs from '$lib/components/AboutUs.svelte';
	import GetStarted from '$lib/components/GetStarted.svelte';
	import Footer from '$lib/components/Footer.svelte';
	import Faq from '$lib/components/Faq.svelte';
	import ContactUs from '$lib/components/ContactUs.svelte';
	import Logo from '$lib/components/icon/Logo.svelte';
</script>

<Navbar />

<div class="text-white">
	<section>
		<GetStarted />
	</section>
	<!-- Services Section -->
	<section id="services" class="bg-[var(--card-container-bg)] py-20">
		<div class="container mx-auto px-4">
			<div class="mb-16 text-center">
				<h2 class="mb-4 text-3xl font-bold md:text-4xl">Our Services</h2>
				<p class="mx-auto max-w-2xl text-gray-400">
					We provide comprehensive solutions tailored to your needs, from custom software
					development to embedded systems and web development.
				</p>
			</div>
			<div class="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-4">
				<Card
					title="Custom Software Development"
					description="Tailored solutions for your business needs, from inventory systems to custom applications that drive efficiency and growth."
					link="/services/software"
				/>
				<Card
					title="Embedded Systems"
					description="Smart devices and industrial automation solutions that integrate seamlessly with your existing infrastructure."
					link="/services/embedded"
				/>
				<Card
					title="Website Development"
					description="Modern, responsive websites and web applications that engage your audience and drive results."
					link="/services/web"
				/>
				<Card
					title="Device Repairs"
					description="Professional repair services for smartphones, tablets, laptops, and other electronic devices with quick turnaround times."
					link="/services/repairs"
				/>
			</div>
		</div>
	</section>

	<!-- About Section -->
	<section class="py-20">
		<div class="container mx-auto px-4">
			<AboutUs />
		</div>
	</section>

	<!-- Contact Section -->
	<section>
		<ContactUs />
	</section>

	<!-- FAQ Section -->
	<section class="py-20">
		<div class="container mx-auto px-4">
			<Faq />
		</div>
	</section>
</div>

<Footer />
