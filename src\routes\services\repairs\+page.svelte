<script lang="ts">
	import { fade } from 'svelte/transition';
</script>

<div class="space-y-16 py-8" in:fade>
	<!-- Hero Section -->
	<section
		class="relative overflow-hidden rounded-2xl bg-gradient-to-r from-cyan-600 to-purple-800 p-8 md:p-12"
	>
		<div class="relative z-10 max-w-3xl">
			<h1 class="mb-4 text-4xl font-bold md:text-5xl">Professional Device Repairs</h1>
			<p class="text-xl text-violet-100">
				Expert repair services for all your devices, from smartphones to computers. Fast, reliable,
				and backed by our satisfaction guarantee.
			</p>
		</div>
		<div class="absolute right-0 top-0 -z-10 h-full w-full opacity-20">
			<svg
				xmlns="http://www.w3.org/2000/svg"
				fill="none"
				viewBox="0 0 24 24"
				stroke-width="1.5"
				stroke="currentColor"
				class="h-full w-full"
			>
				<path
					stroke-linecap="round"
					stroke-linejoin="round"
					d="M11.42 15.17L17.25 21A2.652 2.652 0 0021 17.25l-5.877-5.877M11.42 15.17l2.496-3.03c.317-.384.74-.626 1.208-.766M11.42 15.17l-4.655 5.653a2.548 2.548 0 11-3.586-3.586l6.837-5.63m5.108-.233c.55-.164 1.163-.188 1.743-.14a4.5 4.5 0 004.486-6.336l-3.276 3.277a3.004 3.004 0 01-2.25-2.25l3.276-3.276a4.5 4.5 0 00-6.336 4.486c.091 1.076-.071 2.264-.904 2.95l-.102.085m-1.745 1.437L5.909 7.5H4.5L2.25 3.75l1.5-1.5L7.5 4.5v1.409l4.26 4.26m-1.745 1.437l1.745-1.437m6.615 8.206L15.75 15.75M4.867 19.125h.008v.008h-.008v-.008z"
				/>
			</svg>
		</div>
	</section>

	<!-- Features Grid -->
	<section
		class="relative rounded-2xl bg-gradient-to-br from-cyan-600/10 via-purple-800/5 to-transparent p-8"
	>
		<div class="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
			<div
				class="rounded-xl bg-gradient-to-br from-cyan-600/20 via-purple-800/10 to-transparent p-6 backdrop-blur-sm transition-all hover:from-cyan-600/30 hover:via-purple-800/20 hover:to-transparent hover:shadow-lg hover:shadow-cyan-500/10"
			>
				<svg
					xmlns="http://www.w3.org/2000/svg"
					fill="none"
					viewBox="0 0 24 24"
					stroke-width="1.5"
					stroke="currentColor"
					class="mb-4 h-12 w-12 text-[var(--accent-primary)]"
				>
					<path
						stroke-linecap="round"
						stroke-linejoin="round"
						d="M11.42 15.17L17.25 21A2.652 2.652 0 0021 17.25l-5.877-5.877M11.42 15.17l2.496-3.03c.317-.384.74-.626 1.208-.766M11.42 15.17l-4.655 5.653a2.548 2.548 0 11-3.586-3.586l6.837-5.63m5.108-.233c.55-.164 1.163-.188 1.743-.14a4.5 4.5 0 004.486-6.336l-3.276 3.277a3.004 3.004 0 01-2.25-2.25l3.276-3.276a4.5 4.5 0 00-6.336 4.486c.091 1.076-.071 2.264-.904 2.95l-.102.085m-1.745 1.437L5.909 7.5H4.5L2.25 3.75l1.5-1.5L7.5 4.5v1.409l4.26 4.26m-1.745 1.437l1.745-1.437m6.615 8.206L15.75 15.75M4.867 19.125h.008v.008h-.008v-.008z"
					/>
				</svg>
				<h3 class="mb-2 text-xl font-semibold">Expert Technicians</h3>
				<p class="text-gray-300">
					Our certified technicians have years of experience in repairing all types of devices.
				</p>
			</div>
			<div
				class="rounded-xl bg-gradient-to-br from-purple-800/20 via-fuchsia-800/10 to-transparent p-6 backdrop-blur-sm transition-all hover:from-purple-800/30 hover:via-fuchsia-800/20 hover:to-transparent hover:shadow-lg hover:shadow-purple-700/10"
			>
				<svg
					xmlns="http://www.w3.org/2000/svg"
					fill="none"
					viewBox="0 0 24 24"
					stroke-width="1.5"
					stroke="currentColor"
					class="mb-4 h-12 w-12 text-[var(--accent-primary)]"
				>
					<path
						stroke-linecap="round"
						stroke-linejoin="round"
						d="M12 6v6h4.5m4.5 0a9 9 0 11-18 0 9 9 0 0118 0z"
					/>
				</svg>
				<h3 class="mb-2 text-xl font-semibold">Quick Turnaround</h3>
				<p class="text-gray-300">
					Most repairs completed within 24-48 hours, with priority service available.
				</p>
			</div>
			<div
				class="rounded-xl bg-gradient-to-br from-fuchsia-800/20 via-pink-800/10 to-transparent p-6 backdrop-blur-sm transition-all hover:from-fuchsia-800/30 hover:via-pink-800/20 hover:to-transparent hover:shadow-lg hover:shadow-fuchsia-700/10"
			>
				<svg
					xmlns="http://www.w3.org/2000/svg"
					fill="none"
					viewBox="0 0 24 24"
					stroke-width="1.5"
					stroke="currentColor"
					class="mb-4 h-12 w-12 text-[var(--accent-primary)]"
				>
					<path
						stroke-linecap="round"
						stroke-linejoin="round"
						d="M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
					/>
				</svg>
				<h3 class="mb-2 text-xl font-semibold">Quality Guarantee</h3>
				<p class="text-gray-300">
					All repairs backed by our 90-day warranty and satisfaction guarantee.
				</p>
			</div>
		</div>
	</section>

	<!-- Detailed Service Description -->
	<section
		class="relative rounded-2xl bg-gradient-to-br from-cyan-600/5 via-purple-800/5 to-transparent p-8"
	>
		<div class="relative">
			<h2 class="mb-12 text-center text-3xl font-bold">
				<span class="relative inline-block">
					Our Repair Services
					<span
						class="absolute -bottom-2 left-0 h-1 w-full bg-gradient-to-r from-[var(--accent-primary)] to-[var(--accent-secondary)]"
					></span>
				</span>
			</h2>

			<div class="grid gap-12 md:grid-cols-2">
				<!-- Smartphone Repairs -->
				<div
					class="group relative overflow-hidden rounded-xl bg-gradient-to-br from-cyan-600/10 to-transparent p-8 transition-all hover:shadow-lg hover:shadow-cyan-500/10"
				>
					<div class="bg-[var(--card-container-bg)]/80 absolute inset-0 backdrop-blur-sm"></div>
					<div class="relative">
						<div class="mb-6 flex items-center gap-4">
							<div
								class="flex h-12 w-12 items-center justify-center rounded-lg bg-gradient-to-br from-cyan-500 to-purple-700"
							>
								<svg
									xmlns="http://www.w3.org/2000/svg"
									fill="none"
									viewBox="0 0 24 24"
									stroke-width="1.5"
									stroke="white"
									class="h-6 w-6"
								>
									<path
										stroke-linecap="round"
										stroke-linejoin="round"
										d="M10.5 1.5H8.25A2.25 2.25 0 006 3.75v16.5a2.25 2.25 0 002.25 2.25h7.5A2.25 2.25 0 0018 20.25V3.75a2.25 2.25 0 00-2.25-2.25H13.5m-3 0V3h3V1.5m-3 0h3m-3 18.75h3"
									/>
								</svg>
							</div>
							<h3 class="text-2xl font-semibold text-[var(--accent-primary)]">
								Smartphone Repairs
							</h3>
						</div>
						<p class="mb-6 text-gray-300">
							Comprehensive repair services for all major smartphone brands, including screen
							replacements, battery repairs, and more.
						</p>
						<ul class="space-y-3">
							{#each [{ icon: 'M10.5 1.5H8.25A2.25 2.25 0 006 3.75v16.5a2.25 2.25 0 002.25 2.25h7.5A2.25 2.25 0 0018 20.25V3.75a2.25 2.25 0 00-2.25-2.25H13.5m-3 0V3h3V1.5m-3 0h3m-3 18.75h3', text: 'Screen replacements' }, { icon: 'M12 6v6h4.5m4.5 0a9 9 0 11-18 0 9 9 0 0118 0z', text: 'Battery replacements' }, { icon: 'M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z', text: 'Camera repairs' }, { icon: 'M12 6v6h4.5m4.5 0a9 9 0 11-18 0 9 9 0 0118 0z', text: 'Water damage repair' }] as item}
								<li class="flex items-center gap-3 text-gray-300">
									<svg
										xmlns="http://www.w3.org/2000/svg"
										fill="none"
										viewBox="0 0 24 24"
										stroke-width="1.5"
										stroke="currentColor"
										class="h-5 w-5 text-[var(--accent-primary)]"
									>
										<path stroke-linecap="round" stroke-linejoin="round" d={item.icon} />
									</svg>
									{item.text}
								</li>
							{/each}
						</ul>
					</div>
				</div>

				<!-- Computer Repairs -->
				<div
					class="group relative overflow-hidden rounded-xl bg-gradient-to-br from-purple-800/10 to-transparent p-8 transition-all hover:shadow-lg hover:shadow-purple-700/10"
				>
					<div class="bg-[var(--card-container-bg)]/80 absolute inset-0 backdrop-blur-sm"></div>
					<div class="relative">
						<div class="mb-6 flex items-center gap-4">
							<div
								class="flex h-12 w-12 items-center justify-center rounded-lg bg-gradient-to-br from-purple-700 to-fuchsia-700"
							>
								<svg
									xmlns="http://www.w3.org/2000/svg"
									fill="none"
									viewBox="0 0 24 24"
									stroke-width="1.5"
									stroke="white"
									class="h-6 w-6"
								>
									<path
										stroke-linecap="round"
										stroke-linejoin="round"
										d="M9 17.25v1.007a3 3 0 01-.879 2.122L7.5 21h9l-.621-.621A3 3 0 0115 18.257V17.25m6-12V15a2.25 2.25 0 01-2.25 2.25H5.25A2.25 2.25 0 013 15V5.25m18 0A2.25 2.25 0 0018.75 3H5.25A2.25 2.25 0 003 5.25m18 0V12a2.25 2.25 0 01-2.25 2.25H5.25A2.25 2.25 0 013 12V5.25"
									/>
								</svg>
							</div>
							<h3 class="text-2xl font-semibold text-[var(--accent-primary)]">Computer Repairs</h3>
						</div>
						<p class="mb-6 text-gray-300">
							Professional computer repair services for both hardware and software issues, including
							data recovery and system optimization.
						</p>
						<ul class="space-y-3">
							{#each [{ icon: 'M9 17.25v1.007a3 3 0 01-.879 2.122L7.5 21h9l-.621-.621A3 3 0 0115 18.257V17.25m6-12V15a2.25 2.25 0 01-2.25 2.25H5.25A2.25 2.25 0 013 15V5.25m18 0A2.25 2.25 0 0018.75 3H5.25A2.25 2.25 0 003 5.25m18 0V12a2.25 2.25 0 01-2.25 2.25H5.25A2.25 2.25 0 013 12V5.25', text: 'Hardware repairs and upgrades' }, { icon: 'M20.25 6.375c0 2.278-3.694 4.125-8.25 4.125S3.75 8.653 3.75 6.375m16.5 0c0-2.278-3.694-4.125-8.25-4.125S3.75 4.097 3.75 6.375m16.5 0v11.25c0 2.278-3.694 4.125-8.25 4.125s-8.25-1.847-8.25-4.125V6.375m16.5 0v3.75m-16.5-3.75v3.75m16.5 0v3.75C20.25 16.153 16.556 18 12 18s-8.25-1.847-8.25-4.125v-3.75m16.5 0c0 2.278-3.694 4.125-8.25 4.125s-8.25-1.847-8.25-4.125', text: 'Data recovery services' }, { icon: 'M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z', text: 'Virus removal and security' }, { icon: 'M12 6v6h4.5m4.5 0a9 9 0 11-18 0 9 9 0 0118 0z', text: 'System optimization' }] as item}
								<li class="flex items-center gap-3 text-gray-300">
									<svg
										xmlns="http://www.w3.org/2000/svg"
										fill="none"
										viewBox="0 0 24 24"
										stroke-width="1.5"
										stroke="currentColor"
										class="h-5 w-5 text-[var(--accent-primary)]"
									>
										<path stroke-linecap="round" stroke-linejoin="round" d={item.icon} />
									</svg>
									{item.text}
								</li>
							{/each}
						</ul>
					</div>
				</div>

				<!-- Printer Services -->
				<div
					class="group relative overflow-hidden rounded-xl bg-gradient-to-br from-fuchsia-800/20 via-pink-800/10 to-transparent p-8 transition-all hover:shadow-lg hover:shadow-fuchsia-700/10"
				>
					<div class="bg-[var(--card-container-bg)]/80 absolute inset-0 backdrop-blur-sm"></div>
					<div class="relative">
						<div class="mb-6 flex items-center gap-4">
							<div
								class="flex h-12 w-12 items-center justify-center rounded-lg bg-gradient-to-br from-fuchsia-700 to-pink-700"
							>
								<svg
									xmlns="http://www.w3.org/2000/svg"
									fill="none"
									viewBox="0 0 24 24"
									stroke-width="1.5"
									stroke="white"
									class="h-6 w-6"
								>
									<path
										stroke-linecap="round"
										stroke-linejoin="round"
										d="M6.72 13.829c-.24.03-.48.062-.72.096m.72-.096a42.415 42.415 0 0110.56 0m-10.56 0L6.34 18m10.94-4.171c.24.03.48.062.72.096m-.72-.096L17.66 18m0 0l.229 2.523a1.125 1.125 0 01-1.12 1.227H7.231c-.662 0-1.18-.568-1.12-1.227L6.34 18m11.318 0h1.091A2.25 2.25 0 0021 15.75V9.456c0-1.081-.768-2.015-1.837-2.175a48.055 48.055 0 00-1.913-.247M6.34 18H5.25A2.25 2.25 0 013 15.75V9.456c0-1.081.768-2.015 1.837-2.175a48.041 48.041 0 011.913-.247m10.5 0a48.536 48.536 0 00-10.5 0m10.5 0V3.375c0-.621-.504-1.125-1.125-1.125h-8.25c-.621 0-1.125.504-1.125 1.125v3.659M18 10.5h.008v.008H18V10.5zm-3 0h.008v.008H15V10.5z"
									/>
								</svg>
							</div>
							<h3 class="text-2xl font-semibold text-[var(--accent-primary)]">Printer Services</h3>
						</div>
						<p class="mb-6 text-gray-300">
							Professional maintenance and repair services for all types of printers and scanners,
							ensuring optimal performance.
						</p>
						<ul class="space-y-3">
							{#each [{ icon: 'M6.72 13.829c-.24.03-.48.062-.72.096m.72-.096a42.415 42.415 0 0110.56 0m-10.56 0L6.34 18m10.94-4.171c.24.03.48.062.72.096m-.72-.096L17.66 18m0 0l.229 2.523a1.125 1.125 0 01-1.12 1.227H7.231c-.662 0-1.18-.568-1.12-1.227L6.34 18m11.318 0h1.091A2.25 2.25 0 0021 15.75V9.456c0-1.081-.768-2.015-1.837-2.175a48.055 48.055 0 00-1.913-.247M6.34 18H5.25A2.25 2.25 0 013 15.75V9.456c0-1.081.768-2.015 1.837-2.175a48.041 48.041 0 011.913-.247m10.5 0a48.536 48.536 0 00-10.5 0m10.5 0V3.375c0-.621-.504-1.125-1.125-1.125h-8.25c-.621 0-1.125.504-1.125 1.125v3.659M18 10.5h.008v.008H18V10.5zm-3 0h.008v.008H15V10.5z', text: 'Maintenance and cleaning' }, { icon: 'M12 6v6h4.5m4.5 0a9 9 0 11-18 0 9 9 0 0118 0z', text: 'Cartridge replacement' }, { icon: 'M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z', text: 'Network setup' }, { icon: 'M12 6v6h4.5m4.5 0a9 9 0 11-18 0 9 9 0 0118 0z', text: 'Scanner repairs' }] as item}
								<li class="flex items-center gap-3 text-gray-300">
									<svg
										xmlns="http://www.w3.org/2000/svg"
										fill="none"
										viewBox="0 0 24 24"
										stroke-width="1.5"
										stroke="currentColor"
										class="h-5 w-5 text-[var(--accent-primary)]"
									>
										<path stroke-linecap="round" stroke-linejoin="round" d={item.icon} />
									</svg>
									{item.text}
								</li>
							{/each}
						</ul>
					</div>
				</div>

				<!-- Gaming Devices -->
				<div
					class="group relative overflow-hidden rounded-xl bg-gradient-to-br from-pink-800/20 via-rose-800/10 to-transparent p-8 transition-all hover:shadow-lg hover:shadow-pink-700/10"
				>
					<div class="bg-[var(--card-container-bg)]/80 absolute inset-0 backdrop-blur-sm"></div>
					<div class="relative">
						<div class="mb-6 flex items-center gap-4">
							<div
								class="flex h-12 w-12 items-center justify-center rounded-lg bg-gradient-to-br from-pink-700 to-rose-700"
							>
								<svg
									xmlns="http://www.w3.org/2000/svg"
									fill="none"
									viewBox="0 0 24 24"
									stroke-width="1.5"
									stroke="white"
									class="h-6 w-6"
								>
									<path
										stroke-linecap="round"
										stroke-linejoin="round"
										d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9m-9 9a9 9 0 019-9"
									/>
								</svg>
							</div>
							<h3 class="text-2xl font-semibold text-[var(--accent-primary)]">Gaming Devices</h3>
						</div>
						<p class="mb-6 text-gray-300">
							Specialized repair services for gaming consoles, controllers, and accessories, keeping
							you in the game.
						</p>
						<ul class="space-y-3">
							{#each [{ icon: 'M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9m-9 9a9 9 0 019-9', text: 'Console repairs' }, { icon: 'M12 6v6h4.5m4.5 0a9 9 0 11-18 0 9 9 0 0118 0z', text: 'Controller fixes' }, { icon: 'M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z', text: 'Accessory repairs' }, { icon: 'M12 6v6h4.5m4.5 0a9 9 0 11-18 0 9 9 0 0118 0z', text: 'Performance upgrades' }] as item}
								<li class="flex items-center gap-3 text-gray-300">
									<svg
										xmlns="http://www.w3.org/2000/svg"
										fill="none"
										viewBox="0 0 24 24"
										stroke-width="1.5"
										stroke="currentColor"
										class="h-5 w-5 text-[var(--accent-primary)]"
									>
										<path stroke-linecap="round" stroke-linejoin="round" d={item.icon} />
									</svg>
									{item.text}
								</li>
							{/each}
						</ul>
					</div>
				</div>
			</div>
		</div>
	</section>

	<!-- Repair Process -->
	<section
		class="relative rounded-2xl bg-gradient-to-br from-cyan-600/10 via-purple-800/10 to-transparent p-8"
	>
		<div class="relative">
			<h2 class="mb-12 text-center text-3xl font-bold">
				<span class="relative inline-block">
					Our Repair Process
					<span
						class="absolute -bottom-2 left-0 h-1 w-full bg-gradient-to-r from-[var(--accent-primary)] to-[var(--accent-secondary)]"
					></span>
				</span>
			</h2>

			<div class="grid gap-8 md:grid-cols-3">
				<div
					class="group relative overflow-hidden rounded-xl bg-gradient-to-br from-cyan-600/10 to-transparent p-8 transition-all hover:shadow-lg hover:shadow-cyan-500/10"
				>
					<div class="bg-[var(--card-container-bg)]/80 absolute inset-0 backdrop-blur-sm"></div>
					<div class="relative">
						<div
							class="mb-6 flex h-12 w-12 items-center justify-center rounded-lg bg-gradient-to-br from-cyan-500 to-purple-700"
						>
							<svg
								xmlns="http://www.w3.org/2000/svg"
								fill="none"
								viewBox="0 0 24 24"
								stroke-width="1.5"
								stroke="white"
								class="h-6 w-6"
							>
								<path
									stroke-linecap="round"
									stroke-linejoin="round"
									d="M9.594 3.94c.09-.542.56-.94 1.11-.94h2.593c.55 0 1.02.398 1.11.94l.213 1.281c.063.374.313.686.645.87.074.04.147.083.22.127.324.196.72.257 1.075.124l1.217-.456a1.125 1.125 0 011.37.49l1.296 2.247a1.125 1.125 0 01-.26 1.431l-1.003.827c-.293.24-.438.613-.431.992a6.759 6.759 0 010 .255c-.007.378.138.75.43.99l1.005.828c.424.35.534.954.26 1.43l-1.298 2.247a1.125 1.125 0 01-1.369.491l-1.217-.456c-.355-.133-.75-.072-1.076.124a6.57 6.57 0 01-.22.128c-.331.183-.581.495-.644.869l-.213 1.28c-.09.543-.56.941-1.11.941h-2.594c-.55 0-1.02-.398-1.11-.94l-.213-1.281c-.062-.374-.312-.686-.644-.87a6.52 6.52 0 01-.22-.127c-.325-.196-.72-.257-1.076-.124l-1.217.456a1.125 1.125 0 01-1.369-.49l-1.297-2.247a1.125 1.125 0 01.26-1.431l1.004-.827c.292-.24.437-.613.43-.992a6.932 6.932 0 010-.255c.007-.378-.138-.75-.43-.99l-1.004-.828a1.125 1.125 0 01-.26-1.43l1.297-2.247a1.125 1.125 0 011.37-.491l1.216.456c.356.133.751.072 1.076-.124.072-.044.146-.087.22-.128.332-.183.582-.495.644-.869l.214-1.281z"
								/>
								<path
									stroke-linecap="round"
									stroke-linejoin="round"
									d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
								/>
							</svg>
						</div>
						<h3 class="mb-2 text-xl font-semibold text-[var(--accent-primary)]">Diagnosis</h3>
						<p class="text-gray-300">
							Thorough assessment of your device's issues using advanced diagnostic tools and expert
							analysis.
						</p>
					</div>
				</div>

				<div
					class="group relative overflow-hidden rounded-xl bg-gradient-to-br from-purple-800/10 to-transparent p-8 transition-all hover:shadow-lg hover:shadow-purple-700/10"
				>
					<div class="bg-[var(--card-container-bg)]/80 absolute inset-0 backdrop-blur-sm"></div>
					<div class="relative">
						<div
							class="mb-6 flex h-12 w-12 items-center justify-center rounded-lg bg-gradient-to-br from-purple-700 to-fuchsia-700"
						>
							<svg
								xmlns="http://www.w3.org/2000/svg"
								fill="none"
								viewBox="0 0 24 24"
								stroke-width="1.5"
								stroke="white"
								class="h-6 w-6"
							>
								<path
									stroke-linecap="round"
									stroke-linejoin="round"
									d="M11.42 15.17L17.25 21A2.652 2.652 0 0021 17.25l-5.877-5.877M11.42 15.17l2.496-3.03c.317-.384.74-.626 1.208-.766M11.42 15.17l-4.655 5.653a2.548 2.548 0 11-3.586-3.586l6.837-5.63m5.108-.233c.55-.164 1.163-.188 1.743-.14a4.5 4.5 0 004.486-6.336l-3.276 3.277a3.004 3.004 0 01-2.25-2.25l3.276-3.276a4.5 4.5 0 00-6.336 4.486c.091 1.076-.071 2.264-.904 2.95l-.102.085m-1.745 1.437L5.909 7.5H4.5L2.25 3.75l1.5-1.5L7.5 4.5v1.409l4.26 4.26m-1.745 1.437l1.745-1.437m6.615 8.206L15.75 15.75M4.867 19.125h.008v.008h-.008v-.008z"
								/>
							</svg>
						</div>
						<h3 class="mb-2 text-xl font-semibold text-[var(--accent-primary)]">Repair</h3>
						<p class="text-gray-300">
							Expert repair using genuine parts and following manufacturer specifications for
							optimal results.
						</p>
					</div>
				</div>

				<div
					class="group relative overflow-hidden rounded-xl bg-gradient-to-br from-fuchsia-800/20 via-pink-800/10 to-transparent p-8 transition-all hover:shadow-lg hover:shadow-fuchsia-700/10"
				>
					<div class="bg-[var(--card-container-bg)]/80 absolute inset-0 backdrop-blur-sm"></div>
					<div class="relative">
						<div
							class="mb-6 flex h-12 w-12 items-center justify-center rounded-lg bg-gradient-to-br from-fuchsia-700 to-pink-700"
						>
							<svg
								xmlns="http://www.w3.org/2000/svg"
								fill="none"
								viewBox="0 0 24 24"
								stroke-width="1.5"
								stroke="white"
								class="h-6 w-6"
							>
								<path
									stroke-linecap="round"
									stroke-linejoin="round"
									d="M9 12.75L11.25 15 15 9.75m-3-7.036A11.959 11.959 0 013.598 6 11.99 11.99 0 003 9.749c0 5.592 3.824 10.29 9 11.623 5.176-1.332 9-6.03 9-11.622 0-1.31-.21-2.571-.598-3.751h-.152c-3.196 0-6.1-1.248-8.25-3.285z"
								/>
							</svg>
						</div>
						<h3 class="mb-2 text-xl font-semibold text-[var(--accent-primary)]">Testing</h3>
						<p class="text-gray-300">
							Comprehensive testing to ensure your device is working perfectly and meets all quality
							standards.
						</p>
					</div>
				</div>
			</div>
		</div>
	</section>

	<!-- Call to Action -->
	<section
		class="relative overflow-hidden rounded-2xl bg-gradient-to-br from-cyan-600/20 via-purple-800/20 to-transparent p-12"
	>
		<div class="bg-[var(--card-container-bg)]/40 absolute inset-0 backdrop-blur-sm"></div>
		<div class="relative mx-auto max-w-3xl text-center">
			<h2 class="mb-4 text-4xl font-bold">Need a Repair?</h2>
			<p class="mb-8 text-xl text-violet-100">
				Let us help you get your device back in perfect working condition. Fast, reliable, and
				professional service guaranteed.
			</p>
			<a
				href="/account/services/repairs"
				class="hover:shadow-[var(--accent-primary)]/20 group relative inline-flex items-center gap-2 overflow-hidden rounded-lg bg-gradient-to-r from-[var(--accent-primary)] to-[var(--accent-secondary)] px-10 py-4 text-lg font-semibold text-white transition-all hover:shadow-lg"
			>
				<span class="relative z-10">Schedule a Repair</span>
				<svg
					xmlns="http://www.w3.org/2000/svg"
					fill="none"
					viewBox="0 0 24 24"
					stroke-width="2"
					stroke="currentColor"
					class="h-5 w-5 transition-transform group-hover:translate-x-1"
				>
					<path
						stroke-linecap="round"
						stroke-linejoin="round"
						d="M13.5 4.5L21 12m0 0l-7.5 7.5M21 12H3"
					/>
				</svg>
			</a>
		</div>
	</section>
</div>
