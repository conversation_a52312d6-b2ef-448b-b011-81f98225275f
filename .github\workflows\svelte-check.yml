name: Svelte Check
on:
  push:
    branches:
      - master
      - staging
      - next
  pull_request:
    branches:
      - master
      - staging
      - next
jobs:
  Svelte-Check:
    runs-on: ubuntu-latest
    if: ${{ !contains(github.event.head_commit.message, '-skip') }}
    steps:
      - name: Checkout Repository
        uses: actions/checkout@v4
      - name: Setup Node.js environment
        uses: actions/setup-node@v4.1.0
      - name: Install depenedencies
        run: npm i
      - name: Svelte Check
        run: npm run check
