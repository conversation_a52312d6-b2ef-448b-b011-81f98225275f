import { v6 } from 'uuid';
import { SessionStore } from './Store';
import type { JsonSession, JsonSessionStorage } from '$lib/shared/Session';
import { db } from '@backend/db';

export class Session {
	private __storage: JsonSessionStorage = {
		createdAt: Date.now(),
		permissions: ['texrepairs.default']
	};
	private __modified: boolean = false;
	private __sessionId: string = v6();

	public constructor(sessionId: string | undefined = undefined) {
		if (sessionId) {
			this.__sessionId = sessionId;
		}
	}

	/**
	 * Returns the user object from the session
	 */
	public get user(): JsonSessionStorage['user'] | undefined {
		return this.__storage.user;
	}

	/**
	 * Returns the user object from the session
	 * todo: Make this type return the actual api type
	 */
	public async getUser(): Promise<JsonSessionStorage['user'] | null> {
		return this.__storage.user || null;
	}

	public async getUserEmailVerified(): Promise<boolean> {
		// fetch from db
		const id = this.__storage.user?.id;
		if (!id) {
			return false;
		}
		const user = await db.getUser(id);
		return user?.verified || false;
	}

	/**
	 * Destroys the session
	 */
	public async destroy(): Promise<void> {
		SessionStore.delete(this.__sessionId);
	}

	/**
	 * Returns the session storage
	 * @returns {JsonSessionStorage}
	 */
	public get storage(): JsonSessionStorage {
		return this.__storage;
	}

	public set storage(value: JsonSessionStorage) {
		this.__storage = value;
		this.__modified = true;
	}

	public get id(): string {
		return this.__sessionId;
	}
	public get dirty(): boolean {
		return this.__modified;
	}

	public toJSON(): JsonSession {
		return {
			id: this.__sessionId,
			storage: this.__storage
		};
	}

	public static fromJSON(json: JsonSession): Session {
		let s = new Session(json.id);
		s.__storage = json.storage;
		s.__modified = false;
		return s;
	}

	public async save(force: boolean = false): Promise<void> {
		// If modified.
		if (force || this.dirty) {
			SessionStore.set(this.__sessionId, this);
		}
	}
}
