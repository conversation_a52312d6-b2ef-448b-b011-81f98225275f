import { building } from '$app/environment';
import { db } from '../src/backend/db.js';
import { Time } from '$lib/texrepairs/utils/time.js';
import { Session } from '../src/lib/server/sessions/index.js';
import { SessionStore } from '../src/lib/server/sessions/Store.js';
import type { Hook, HookCleanup } from './index.js';
import type { Permissions } from '$lib/shared/Session';

/**
 * Handles texrepair session
 */
export const onHandle: Hook = async ({ event, resolve }, next) => {
	if (building) {
		return resolve(event);
	}

	const sessionId: string = await (async () => {
		let sessionId = event.cookies.get('session_id');
		if (!sessionId) {
			// we don't have a session, create one with session service.
			const session = new Session();
			await session.save(true);

			event.cookies.set('session_id', session.id, {
				path: '/',
				maxAge: new Time().days(30).asSecs(),
				secure: process.env.NODE_ENV === 'production'
			});

			event.locals.session = session;
			return session.id;
		} else {
			return sessionId;
		}
	})();

	if (!event.locals.session) {
		event.locals.session = await SessionStore.get(sessionId);

		if (!event.locals.session) {
			const session = new Session();
			await session.save(true);
			event.locals.session = session;
		}

		event.locals.session = await getSessionFromDb(sessionId);
	}

	if (event.url.pathname === '/logout') {
		const ALWAYS_EXPIRED = new Date(0);
		event.locals.session?.destroy();
		return new Response(null, {
			status: 303,
			headers: {
				Location: '/',
				'Set-Cookie': `session_id=; path=/; expires=${ALWAYS_EXPIRED.toUTCString()}; secure=${process.env.NODE_ENV === 'production'}`
			}
		});
	}

	if (event.locals.session?.user) {
		if (!event.locals.session.user.verified) {
			// should we be doing this?
			await updateSessionFromDb(event.locals.session);
			if (
				event.url.pathname !== '/account/grant/not-verified' &&
				!event.url.pathname.includes('/account/grant')
			) {
				console.log('redirecting to /account/grant/not-verified');
				return new Response(null, {
					status: 303,
					headers: { Location: '/account/grant/not-verified' }
				});
			} else if (event.url.pathname.includes('/account/grant')) {
				// if they're not verified, lets update their session to the one with the db
				// cause maybe they've verified their email since the last time they logged in
				// make sure the path is not already /account/grant/not-verified
				if (event.locals.session?.user?.verified) {
					return new Response(null, {
						status: 303,
						headers: { Location: '/account/settings' }
					});
				}
			}
		}
	}

	return next();
};

export const onCleanup: HookCleanup = async ({ event, resolve }, next) => {
	if (event.locals.session) {
		await event.locals.session.save();
	}
};

async function getSessionFromDb(sessionId: string): Promise<Session | undefined> {
	const dbSession = await db.getSession(sessionId);
	if (!dbSession) {
		return;
	}
	const serverSession = new Session(dbSession.id);
	const user = await db.getUserById(dbSession.owner_id);
	if (user) {
		const validPermissions = user.permissions.filter(
			(p): p is Permissions =>
				p === 'texrepairs.default' || p === 'texrepairs.email.share' || p === '*'
		);
		serverSession.storage = {
			createdAt: dbSession.created_at.getTime(),
			permissions: validPermissions,
			user: {
				id: user.id,
				username: user.username || '',
				email: user.email,
				verified: user.verified,
				user_info: user.user_info
					? {
							first_name: user.user_info.first_name || '',
							last_name: user.user_info.last_name || '',
							phone_number: user.user_info.phone_number || '',
							billing_address: user.user_info.billing_address || undefined,
							billing_city: user.user_info.billing_city || undefined,
							billing_state: user.user_info.billing_state || undefined,
							billing_country: user.user_info.billing_country || undefined,
							billing_postal_code: user.user_info.billing_postal_code || undefined
						}
					: undefined
			}
		};
	}
	return serverSession;
}

async function updateSessionFromDb(session: Session) {
	const dbSession = await db.getSession(session.id);
	console.log('dbSession', dbSession);
	if (!dbSession) {
		return;
	}

	if (session.id !== dbSession.id) {
		session.destroy();
		session = new Session(dbSession.id);
	}

	const user = await db.getUserById(dbSession.owner_id);
	if (user) {
		const validPermissions = user.permissions.filter(
			(p): p is Permissions =>
				p === 'texrepairs.default' || p === 'texrepairs.email.share' || p === '*'
		);
		session.storage = {
			createdAt: dbSession.created_at.getTime(),
			permissions: validPermissions,
			user: {
				id: user.id,
				username: user.username || '',
				email: user.email,
				verified: user.verified,
				user_info: user.user_info
					? {
							first_name: user.user_info.first_name || '',
							last_name: user.user_info.last_name || '',
							phone_number: user.user_info.phone_number || '',
							billing_address: user.user_info.billing_address || undefined,
							billing_city: user.user_info.billing_city || undefined,
							billing_state: user.user_info.billing_state || undefined,
							billing_country: user.user_info.billing_country || undefined,
							billing_postal_code: user.user_info.billing_postal_code || undefined
						}
					: undefined
			}
		};
		await session.save(true);
	}
}
