<script lang="ts">
	interface Props {
		onClick?: () => void;
		href?: string;
		text: any;
		type?:
			| 'button'
			| 'error'
			| 'success'
			| 'warning'
			| 'info'
			| 'primary'
			| 'secondary'
			| 'tertiary'
			| 'gradient'
			| 'link';
		width?: string;
		height?: string;
		class?: string;
		disabled?: boolean;
	}

	let {
		onClick = () => {},
		href,
		text,
		type = 'primary',
		height = '36px',
		width = 'fit-content',
		disabled = false,
		class: className = ''
	}: Props = $props();
</script>

{#if href}
	<a
		class="tex-btn {type} {className}"
		style="min-height: {height}; max-height: {height}; min-width: {width}; max-width: {width}"
		{href}
		data-sveltekit-reload
		data-disabled={disabled}
	>
		{@render text()}
	</a>
{:else if type === 'link'}
	<button
		class="tex-btn {type} {className}"
		style="min-height: {height}; max-height: {height}; min-width: {width}; max-width: {width}"
		onclick={onClick}
		{disabled}
	>
		{@render text()}
	</button>
{:else}
	<button
		class="tex-btn {type} {className}"
		style="min-height: {height}; max-height: {height}; min-width: {width}; max-width: {width}"
		onclick={onClick}
		{disabled}
	>
		{@render text()}
	</button>
{/if}

<style lang="scss">
	/* if its a a.button we need to do some special stuff to make it center */
	a.tex-btn {
		display: flex;
		justify-content: center;
		align-items: center;
		text-decoration: none;
	}

	.tex-btn {
		color: white;
		background-color: var(--btn-primary);
		font-family: Roboto, Arial;
		text-align: center;
		padding: 0 30px;
		border-radius: 8px;
		transition:
			background-color 200ms,
			transform 200ms;
		// padding: 10px 12px;

		&:hover {
			background-color: var(--btn-hover-primary);
		}

		&.error {
			background-color: var(--btn-error);
			&:hover {
				background-color: var(--btn-hover-error);
			}
		}

		&.success {
			background-color: var(--btn-success);
			&:hover {
				background-color: var(--btn-hover-success);
			}
		}

		&.warning {
			background-color: var(--btn-warning);
			&:hover {
				background-color: var(--btn-hover-warning);
			}
		}

		&.info {
			background-color: var(--btn-info);
			&:hover {
				background-color: var(--btn-hover-info);
			}
		}

		&.primary {
			background-color: var(--btn-primary);
			&:hover {
				background-color: var(--btn-hover-primary);
			}
		}

		&.secondary {
			background-color: var(--btn-secondary);
			&:hover {
				background-color: var(--btn-hover-secondary);
			}
		}

		&.tertiary {
			background-color: var(--btn-tertiary);
			&:hover {
				background-color: var(--btn-hover-tertiary);
			}
		}

		&.link {
			background-color: transparent;
			padding: 0;
			color: var(--link-btn-text);
			&:hover {
				color: var(--link-btn-text-hover);
			}
		}
	}
</style>
