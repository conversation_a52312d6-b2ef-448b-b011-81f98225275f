<script lang="ts">
	import Button from '$lib/components/forms/Button.svelte';
	import type { Snippet } from 'svelte';
	import PropError from '../util/PropError.svelte';

	let {
		title,
		description,
		body,
		show = false,
		showCloseButton = true,
		onClose,
		cancelLabel = 'Cancel',
		submitButton,
		children
	} = $props<{
		title: string;
		description?: string;
		body?: Snippet<[]> | undefined;
		show: boolean;
		showCloseButton?: boolean;
		onClose: () => void;
		cancelLabel?: string;
		children?: Snippet<[]> | undefined;
		submitButton?: Snippet<[]> | undefined;
	}>();
</script>

{#if show}
	<div class="fixed inset-0 z-50 flex items-center justify-center bg-black/50">
		<div class="w-full max-w-md rounded-2xl bg-[var(--card-bg)] p-6 shadow-lg">
			<h3 class="mb-4 text-lg font-medium text-[var(--text-primary)]">{title}</h3>
			{#if !description && !body}
				<p class="mb-4 text-sm text-[var(--text-secondary)]">
					<PropError prop="description or body" component="Modal" />
				</p>
			{/if}
			{#if description}
				<p class="mb-4 text-sm text-[var(--text-secondary)]">{description}</p>
			{/if}
			{#if body}
				<div class="mb-4">
					{@render body?.()}
				</div>
			{/if}
			{#if children}
				{@render children?.()}
			{/if}
			<div class="flex justify-end gap-2">
				{#if showCloseButton}
					<Button type="secondary" onClick={onClose}>
						{#snippet text()}
							{cancelLabel}
						{/snippet}
					</Button>
				{/if}
				{#if submitButton}
					{@render submitButton?.()}
				{/if}
			</div>
		</div>
	</div>
{/if}
