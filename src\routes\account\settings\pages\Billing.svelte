<script lang="ts">
	// No props needed for this component
</script>

<div class="space-y-8">
	<div>
		<h2 class="mb-1 text-xl font-semibold text-[var(--text-primary)]">Billing Information</h2>
		<p class="text-sm text-[var(--text-secondary)]">
			Manage your payment methods and view billing history.
		</p>
	</div>
	<div class="space-y-6">
		<div class="rounded-xl bg-[var(--card-container-bg)] p-6">
			<div class="mb-6 flex items-center justify-between">
				<h3 class="text-lg font-medium text-[var(--text-primary)]">Payment Methods</h3>
				<form method="POST" action="?/addPaymentMethod">
					<button
						type="submit"
						class="rounded-xl bg-[var(--btn-primary)] px-4 py-2 text-sm font-medium text-white transition-opacity hover:opacity-90"
					>
						Add Payment Method
					</button>
				</form>
			</div>
			<div
				class="flex h-32 items-center justify-center rounded-xl border-2 border-dashed border-[var(--border-color)]"
			>
				<p class="text-sm text-[var(--text-secondary)]">No payment methods added yet</p>
			</div>
		</div>
		<div class="rounded-xl bg-[var(--card-container-bg)] p-6">
			<h3 class="mb-6 text-lg font-medium text-[var(--text-primary)]">Billing History</h3>
			<div
				class="flex h-32 items-center justify-center rounded-xl border-2 border-dashed border-[var(--border-color)]"
			>
				<p class="text-sm text-[var(--text-secondary)]">No billing history available</p>
			</div>
		</div>
	</div>
</div>
