import { db } from '@backend/db';
import type { Session, User, UserResponse } from './types';
import { randomBytes } from 'crypto';
import jwt from 'jsonwebtoken';
import bcrypt from 'bcrypt';
import type { JsonSessionStorage } from '$lib/shared/Session';
import { EmailService } from './email';

const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';
const SESSION_DURATION = 7 * 24 * 60 * 60 * 1000; // 7 days
const SALT_ROUNDS = 10; // Number of salt rounds for bcrypt

export class AuthService {
	static addPaymentMethod(id: string | undefined, paymentMethod: string) {
		return false;
	}
	static updateNotificationPreferences(
		id: string | undefined,
		arg1: { emailNotifications: boolean; serviceUpdates: boolean }
	) {
		return false;
	}
	static async sendPasswordResetEmail(email: string): Promise<boolean> {
		try {
			// Check if user exists
			const user = await db.getUser(email);
			if (!user) {
				// We return true even if user doesn't exist to prevent email enumeration
				return true;
			}

			// Delete any existing tokens and create a new one
			await db.deleteEmailToken(user.id);
			const resetToken = await db.createEmailToken(user.id, 1); // 1 hour expiry
			if (!resetToken) {
				throw new Error('Failed to create reset token');
			}

			// Send the password reset email
			const emailSent = await EmailService.sendPasswordResetEmail(email, resetToken);
			if (!emailSent) {
				console.error('[!] Failed to send password reset email to:', email);
				return false;
			}

			return true;
		} catch (error) {
			console.error('[!] Error in sendPasswordResetEmail:', error);
			return false;
		}
	}

	static async updateProfile(userId: string, profileData: Partial<JsonSessionStorage['user']>) {
		return false;
	}

	static async updatePassword(userId: string, currentPassword: string, newPassword: string) {
		return false;
	}

	static async hashPassword(password: string): Promise<string> {
		return bcrypt.hash(password, SALT_ROUNDS);
	}

	static async verifyPassword(password: string, hash: string): Promise<boolean> {
		return bcrypt.compare(password, hash);
	}

	private static generateToken(): string {
		return randomBytes(32).toString('hex');
	}

	private static createJWT(user: UserResponse): string {
		return jwt.sign(
			{
				id: user.id,
				email: user.email,
				roles: user.roles,
				permissions: user.permissions
			},
			JWT_SECRET,
			{ expiresIn: '7d' }
		);
	}

	static verifyJWT(
		token: string
	): { id: string; email: string; roles: string[]; permissions: string[] } | null {
		try {
			return jwt.verify(token, JWT_SECRET) as any;
		} catch {
			return null;
		}
	}

	static async register(data: {
		email: string;
		username: string | null;
		password: string;
		firstName: string;
		lastName: string;
		phone: string;
		billingAddress?: string;
	}): Promise<{ user: UserResponse; token: string; session: Session }> {
		const existingUser = await db.getUser(data.email);
		if (existingUser) {
			throw new Error('User already exists');
		}

		const now = new Date();
		const expiresAt = new Date(now.getTime() + SESSION_DURATION);
		const verified = process.env.NODE_ENV === 'development' ? true : false;

		const user = await db.createUser({
			email: data.email,
			username: data.username,
			password: data.password, // Will be hashed in createUser
			language: 'en-US',
			roles: ['guest'],
			permissions: [],
			verified: verified,
			provider: 'texrepairs',
			pending_deletion: false
		});

		if (!user) {
			throw new Error('Failed to create user');
		}

		// Create user info
		const userInfo = await db.createUserInfo(user.id, {
			first_name: data.firstName,
			last_name: data.lastName,
			phone_number: data.phone,
			billing_address: data.billingAddress ?? null,
			billing_city: null,
			billing_state: null,
			billing_country: null,
			billing_postal_code: null
		});

		if (!userInfo) {
			throw new Error('Failed to create user info');
		}

		// if not in dev mode, send verification email
		if (process.env.NODE_ENV !== 'development') {
			// Generate and store email verification token
			const verificationToken = await db.createEmailToken(user.id);
			if (!verificationToken) {
				throw new Error('Failed to create verification token');
			}

			// Send verification email
			const emailSent = await EmailService.sendVerificationEmail(data.email, verificationToken);
			if (!emailSent) {
				console.error('[!] Failed to send verification email to:', data.email);
				// We don't throw here because the user is still created, they just need to request a new verification email
			}
		}

		const token = this.createJWT(user);
		const session = await db.createSession({
			token,
			expires_at: expiresAt,
			owner_id: user.id,
			owner_type: 'user',
			created_at: now,
			last_used_at: now
		});

		if (!session) {
			throw new Error('Failed to create session');
		}

		return {
			user: { ...user, user_info: userInfo },
			token,
			session
		};
	}

	static async login(
		email: string,
		password: string
	): Promise<{ user: UserResponse; token: string; session: Session }> {
		const userWithHash = await db.getUserWithPassword(email);
		if (!userWithHash) {
			throw new Error('Invalid username or password');
		}

		if (userWithHash.pending_deletion) {
			throw new Error('Account disabled');
		}

		const isValid = await this.verifyPassword(password, userWithHash.password_hash);
		if (!isValid) {
			throw new Error('Invalid username or password');
		}

		const now = new Date();
		const expiresAt = new Date(now.getTime() + SESSION_DURATION);
		const token = this.createJWT(userWithHash);
		const session = await db.createSession({
			token,
			expires_at: expiresAt,
			owner_id: userWithHash.id,
			owner_type: 'user',
			created_at: now,
			last_used_at: now
		});

		if (!session) {
			throw new Error('Failed to create session');
		}

		const { password_hash, ...user } = userWithHash;
		return { user, token, session };
	}

	static async getUserFromSession(token: string): Promise<UserResponse | null> {
		return db.getUserFromSession(token);
	}

	static async logout(token: string): Promise<void> {
		await db.deleteSession(token);
	}

	static async resendVerificationEmail(email: string): Promise<boolean> {
		const user = await db.getUser(email);
		if (!user) {
			throw new Error('User not found');
		}

		// create a new token (this will automatically delete any existing tokens)
		await db.deleteEmailToken(user.id);
		const verificationToken = await db.createEmailToken(user.id);
		if (!verificationToken) {
			throw new Error('Failed to create verification token');
		}
		return EmailService.sendVerificationEmail(email, verificationToken);
	}

	static async deleteAccount(userId: string): Promise<boolean> {
		const account = await db.getUserById(userId);
		if (!account) {
			throw new Error('Account not found');
		}

		if (!account.username) {
			throw new Error('Account username not found');
		}

		const emailSent = await EmailService.sendDeleteAccountEmail(account.email, account.username);

		if (!emailSent) {
			throw new Error('Failed to send delete account email');
		}
		return await db.deleteUser(userId);
	}

	static async disableAccount(userId: string): Promise<boolean> {
		return await db.disableUser(userId);
	}
}
