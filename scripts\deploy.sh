#!/bin/bash
# THIS SCRIPT IS MEANT TO BE RUN BY THE GITHUB ACTIONS WORKFLOW

# Environment variables (use from github actions)
DEPLOY_ENABLED=${DEPLOY_ENABLED:-false}
DATABASE_SECRET=${DATABASE_SECRET:-}
NODE_ENV=production
PUBLIC_ENVIRONMENT_VERSION=production
ENABLE_DEBUGGER=false
DATABASE_USER=postgres
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_PASSWORD=${DATABASE_PASSWORD:-}
DATABASE_NAME=${DATABASE_NAME:-}
REDIS_URL=redis://localhost:6379
ACCOUNT_GRANT_BASE=https://texrepairs.com/account/grant
EMAIL_BASE_URL=https://texrepairs.com
BOLT_NODE_ID=${BOLT_NODE_ID:-1}
SENDGRID_API_KEY=${SENDGRID_API_KEY:-}

# Debug information
echo "DEPLOY_ENABLED value: '$DEPLOY_ENABLED'"
echo "DEPLOY_ENABLED type: $(declare -p DEPLOY_ENABLED 2>/dev/null || echo 'not set')"

if [ "$DEPLOY_ENABLED" != "true" ]; then
    echo "Deploy is disabled, skipping"
    exit 0
fi

# check if the database secret is set
if [ -z "$DATABASE_SECRET" ]; then
    echo "DATABASE_SECRET is not set, please set it"
    exit 1
fi

if [ -z "$DATABASE_PASSWORD" ]; then
    echo "DATABASE_PASSWORD is not set, please set it"
    exit 1
fi

if [ -z "$DATABASE_NAME" ]; then
    echo "DATABASE_NAME is not set, please set it"
    exit 1
fi

# check if docker is installed
if ! command -v docker &> /dev/null
then
    echo "Docker is not installed, please install it"
    exit 1
fi

# Check Docker disk usage and prune if over 20GB
# Get total Docker space usage
# DOCKER_USAGE=$(docker system df | awk 'NR==2 {print $2}' | sed 's/GB//')
# if [ $(echo "$DOCKER_USAGE > 20" | bc) -eq 1 ]; then
#     echo "Docker usage is ${DOCKER_USAGE}GB, which exceeds 20GB. Pruning Docker build data..."
docker system prune -a -f --volumes
# else
#     echo "Docker usage is ${DOCKER_USAGE}GB, which is under 20GB. Skipping prune."
# fi

echo "Creating a .env file..."
echo "DATABASE_SECRET=$DATABASE_SECRET" > .env
echo "DATABASE_PASSWORD=$DATABASE_PASSWORD" >> .env
echo "DATABASE_NAME=$DATABASE_NAME" >> .env
echo "DATABASE_USER=$DATABASE_USER" >> .env
echo "DATABASE_HOST=$DATABASE_HOST" >> .env
echo "DATABASE_PORT=$DATABASE_PORT" >> .env
echo "REDIS_URL=$REDIS_URL" >> .env
echo "ACCOUNT_GRANT_BASE=$ACCOUNT_GRANT_BASE" >> .env
echo "EMAIL_BASE_URL=$EMAIL_BASE_URL" >> .env
echo "NODE_ENV=$NODE_ENV" >> .env

echo "Saving dev dependencies to run migrations..."
pnpm install --frozen-lockfile --force

echo "Running migrations..."
pnpm run migrate

echo "Deleting node_modules..."
rm -rf node_modules

# Build the docker image
echo "Building Docker image..."
docker build -t texrepairs-website .

# Check if the image was built successfully
if [ $? -ne 0 ]; then
    echo "Docker image build failed"
    exit 1
fi

echo "Deleting .env file..."
rm .env

# Stop existing container if running
if docker ps -a | grep -q texrepairs-website; then
    echo "Stopping existing container..."
    docker stop texrepairs-website
    docker rm texrepairs-website
fi

# We probably don't need to add all these env variables, but it's easier to do it this way for now
# allow postgresql and redis ports via host

# TODO: figure out if we should use the .env file instead
echo "Starting new container..."
docker run -d \
    --name texrepairs-website \
    --network host \
    -p 8001:3000 \
    -e DATABASE_SECRET="$DATABASE_SECRET" \
    -e DATABASE_PASSWORD="$DATABASE_PASSWORD" \
    -e DATABASE_NAME="$DATABASE_NAME" \
    -e DATABASE_HOST="$DATABASE_HOST" \
    -e DATABASE_PORT="$DATABASE_PORT" \
    -e DATABASE_USER="$DATABASE_USER" \
    -e SENDGRID_API_KEY="$SENDGRID_API_KEY" \
    -e ACCOUNT_GRANT_BASE="$ACCOUNT_GRANT_BASE" \
    -e EMAIL_BASE_URL="$EMAIL_BASE_URL" \
    -e NODE_ENV="$NODE_ENV" \
    texrepairs-website

# Check if the container was started successfully
if [ $? -ne 0 ]; then
    echo "Container failed to start!"
    exit 1
fi

# Print the container ID
echo "Deployed successfully with ID: $(docker ps -q --filter name=texrepairs-website)"
echo "You can now access the website at http://localhost:3000"