/**
 * Bolt ID Generator
 *
 * A BoltId is a 64-bit unsigned integer with embedded metadata:
 * - 42 bits for timestamp (milliseconds since epoch)
 * - 10 bits for node ID (machine/region/service identifier)
 * - 12 bits for randomness
 */

// Constants for bit allocation
const TIMESTAMP_BITS = 42n;
const NODE_ID_BITS = 10n;
const RANDOM_BITS = 12n;

// Maximum values for each component
const MAX_TIMESTAMP = (1n << TIMESTAMP_BITS) - 1n;
const MAX_NODE_ID = (1n << NODE_ID_BITS) - 1n;
const MAX_RANDOM = (1n << RANDOM_BITS) - 1n;

// Bit shift positions
const NODE_ID_SHIFT = RANDOM_BITS;
const TIMESTAMP_SHIFT = NODE_ID_SHIFT + NODE_ID_BITS;

export class BoltId {
	private static nodeId: bigint = 0n;
	private static lastTimestamp: bigint = 0n;
	private static sequence: bigint = 0n;

	/**
	 * Initialize the Bolt ID generator with a node ID
	 * @param nodeId A number between 0 and 1023 (10 bits) to identify the machine/region/service
	 */
	static initialize(nodeId: number) {
		if (nodeId < 0 || nodeId > Number(MAX_NODE_ID)) {
			throw new Error(`Node ID must be between 0 and ${MAX_NODE_ID}`);
		}
		BoltId.nodeId = BigInt(nodeId);
	}

	/**
	 * Generate a new Bolt ID
	 * @returns A 64-bit unsigned integer as a string
	 */
	static generate(): string {
		let timestamp = BigInt(Date.now());

		// Handle clock drift by incrementing sequence if timestamp hasn't changed
		if (timestamp === BoltId.lastTimestamp) {
			BoltId.sequence = (BoltId.sequence + 1n) & MAX_RANDOM;
			if (BoltId.sequence === 0n) {
				// If we've used all random bits, wait for next millisecond
				while (timestamp === BoltId.lastTimestamp) {
					timestamp = BigInt(Date.now());
				}
			}
		} else {
			BoltId.sequence = 0n;
		}

		BoltId.lastTimestamp = timestamp;

		// Ensure timestamp fits in 42 bits
		if (timestamp > MAX_TIMESTAMP) {
			throw new Error('Timestamp exceeds maximum value');
		}

		// Combine all components into a single 64-bit integer
		const boltId =
			(timestamp << TIMESTAMP_SHIFT) | (BoltId.nodeId << NODE_ID_SHIFT) | BoltId.sequence;

		return boltId.toString();
	}

	/**
	 * Parse a Bolt ID into its components
	 * @param boltId The Bolt ID to parse
	 * @returns An object containing the timestamp, node ID, and random sequence
	 */
	static parse(boltId: string): {
		timestamp: Date;
		nodeId: number;
		sequence: number;
	} {
		const id = BigInt(boltId);

		const timestamp = Number((id >> TIMESTAMP_SHIFT) & MAX_TIMESTAMP);
		const nodeId = Number((id >> NODE_ID_SHIFT) & MAX_NODE_ID);
		const sequence = Number(id & MAX_RANDOM);

		return {
			timestamp: new Date(timestamp),
			nodeId,
			sequence
		};
	}
}
