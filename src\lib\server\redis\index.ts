import { browser, building } from '$app/environment';
import { createClient } from 'redis';
import Util from 'util';

// todo: REDIS MAY NOT ALWAYS BE ON THE SAME INSTANCE!!!
// todo: Replace with skyline???
export const client = createClient();
export const ssr = true;
export default client;

if (browser) {
	throw new Error('[->] Skipping redis connection in browser mode...');
}

try {
	if (building) {
		throw new Error('[->] Skipping redis connection in build mode...');
	}
	await client.connect();
} catch (e) {
	console.log('[!] Failed to connect to redis!');
	console.log('[!] Redis error: ' + Util.inspect(e, false, 3, true));
}
