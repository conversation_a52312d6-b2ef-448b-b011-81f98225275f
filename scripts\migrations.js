import { Pool } from 'pg';
import fs from 'fs';
import path from 'path';
import dotenv from 'dotenv';
import chalk from 'chalk';
import { fileURLToPath } from 'url';

const __dirname = path.dirname(fileURLToPath(import.meta.url));

if (!fs.existsSync(path.join(process.cwd(), '.env'))) {
	// list all files in the current directory
	console.error(chalk.red(chalk.bold('ERR!')), 'Missing .env file');
	process.exit(1);
}

dotenv.config({ path: path.join(process.cwd(), '.env') });

const {
	DATABASE_USER,
	DATABASE_PASSWORD,
	DATABASE_NAME,
	DATABASE_HOST = 'localhost',
	DATABASE_PORT = '5432'
} = process.env;

if (!DATABASE_USER || !DATABASE_PASSWORD || !DATABASE_NAME) {
	const missingVars = [];
	if (!DATABASE_USER) missingVars.push('DATABASE_USER');
	if (!DATABASE_PASSWORD) missingVars.push('DATABASE_PASSWORD');
	if (!DATABASE_NAME) missingVars.push('DATABASE_NAME');
	console.error(
		chalk.red(chalk.bold('ERR!')),
		'Missing required environment variables. Please check your .env file'
	);
	console.error(chalk.yellow('Required variables:'), chalk.cyan(missingVars.join(', ')));
	process.exit(1);
}

const pool = new Pool({
	user: DATABASE_USER,
	password: DATABASE_PASSWORD,
	host: DATABASE_HOST,
	port: DATABASE_PORT,
	database: DATABASE_NAME
});

async function createMigrationsTable() {
	const client = await pool.connect();
	try {
		await client.query(`
            CREATE TABLE IF NOT EXISTS migrations (
                id SERIAL PRIMARY KEY,
                name TEXT NOT NULL UNIQUE,
                applied_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
            );
        `);
	} finally {
		client.release();
	}
}

async function getAppliedMigrations() {
	const client = await pool.connect();
	try {
		const result = await client.query('SELECT name FROM migrations ORDER BY id');
		return result.rows.map((row) => row.name);
	} finally {
		client.release();
	}
}

async function applyMigration(client, migrationName, migrationPath) {
	console.log(`${chalk.blue(chalk.bold('RUN!'))} Applying migration: ${chalk.cyan(migrationName)}`);

	const migrationContent = fs.readFileSync(migrationPath, 'utf8');
	const statements = migrationContent
		.split(';')
		.map((stmt) => stmt.trim())
		.filter((stmt) => stmt.length > 0);

	try {
		await client.query('BEGIN');

		for (const statement of statements) {
			await client.query(statement);
		}

		await client.query('INSERT INTO migrations (name) VALUES ($1)', [migrationName]);

		await client.query('COMMIT');
		console.log(
			`${chalk.green(chalk.bold('OK!'))} Migration ${chalk.cyan(migrationName)} applied successfully`
		);
	} catch (error) {
		await client.query('ROLLBACK');
		throw error;
	}
}

async function runMigrations() {
	const migrationsDir = path.join(__dirname, '..', 'migrations');

	if (!fs.existsSync(migrationsDir)) {
		console.error(
			chalk.red(chalk.bold('ERR!')),
			'Migrations directory not found at:',
			chalk.yellow(migrationsDir)
		);
		process.exit(1);
	}

	try {
		// Test the connection
		process.stdout.write(`${chalk.blue(chalk.bold('RUN!'))} Testing database connection...\r`);
		const testClient = await pool.connect();
		testClient.release();
		console.log(`${chalk.green(chalk.bold('OK!'))} Database connection successful`);

		// Ensure migrations table exists
		await createMigrationsTable();

		// Get list of migration files
		const migrationFiles = fs
			.readdirSync(migrationsDir)
			.filter((file) => file.endsWith('.sql'))
			.sort((a, b) => {
				const numA = parseInt(a.split('_')[0]);
				const numB = parseInt(b.split('_')[0]);
				return numA - numB;
			});

		// Get already applied migrations
		const appliedMigrations = await getAppliedMigrations();

		// Apply pending migrations
		const migrationClient = await pool.connect();
		try {
			for (const migrationFile of migrationFiles) {
				if (!appliedMigrations.includes(migrationFile)) {
					const migrationPath = path.join(migrationsDir, migrationFile);
					await applyMigration(migrationClient, migrationFile, migrationPath);
				} else {
					console.log(
						`${chalk.gray('SKIP!')} Migration ${chalk.cyan(migrationFile)} already applied`
					);
				}
			}
		} finally {
			migrationClient.release();
		}

		console.log(`${chalk.green(chalk.bold('\nDONE!'))} All migrations completed successfully!`);
	} catch (error) {
		console.error(
			chalk.red(chalk.bold('\nERR!')),
			'Error running migrations:',
			chalk.yellow(error.message)
		);
		process.exit(1);
	} finally {
		await pool.end();
	}
}

try {
	await runMigrations();
} catch (error) {
	console.error(
		chalk.red(chalk.bold('ERR!')),
		'An unexpected error occurred:',
		chalk.yellow(error.message)
	);
	process.exit(1);
}
