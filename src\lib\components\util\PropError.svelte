<script lang="ts">
	interface Props {
		prop: string;
		component?: string;
	}

	let { prop = 'missing prop', component = 'unknown' }: Props = $props();
</script>

<div class="bg-red-800 p-5 text-white">
	<div>
		<h1 class="text-2xl font-bold">PropError</h1>
		<p>
			An error occurred while rendering <code>{component}</code>.
		</p>
		<p>
			Property <code>{prop}</code> is required but was not provided.
		</p>
	</div>
</div>

<style lang="scss">
	code {
		background-color: rgba(0, 0, 0, 0.246);
		padding: 0.2rem 0.4rem;
		border-radius: 0.2rem;
	}
	p {
		margin: 0;
	}
	div {
		border-radius: 0.5rem;
	}
</style>
