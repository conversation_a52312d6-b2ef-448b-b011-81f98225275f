import type { LayoutServerLoad } from './$types';
import type { JsonSessionStorage } from '$lib/shared/Session';
import { getDebugItems } from '$lib/server/util/debugInfo';
import { env } from '$env/dynamic/public';
export const load: LayoutServerLoad = async (ev) => {
	const session = ev.locals.session;
	const sessionUser: JsonSessionStorage['user'] = session?.storage?.user;
	const sessionPermissions = session?.storage?.permissions || [];
	const debugItems = await getDebugItems(ev);

	// if the env is dev, then we need to get the git pull status
	if (env.PUBLIC_ENVIRONMENT_VERSION === 'dev') {
		const gitPullStatus = ev.url.searchParams.get('git-pull-status');
		return { sessionUser, sessionPermissions, ...debugItems, gitPullStatus };
	}

	return { sessionUser, sessionPermissions, ...debugItems };
};
