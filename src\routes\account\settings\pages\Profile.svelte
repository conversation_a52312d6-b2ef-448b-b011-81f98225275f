<script lang="ts">
	import * as Form from '$lib/components/forms';
	import Button from '$lib/components/forms/Button.svelte';
	import type { Snippet } from 'svelte';

	interface Props {
		formData: {
			firstName: string;
			lastName: string;
			email: string;
			phone_number: string;
			verified: boolean;
		};
		handleSubmit: (event: Event) => void;
	}

	let { formData, handleSubmit } = $props();

	let showChangeEmailModal = $state(false);
	let newEmail = $state('');
	let confirmEmail = $state('');
	let isChangingEmail = $state(false);

	async function handleChangeEmail() {
		if (newEmail !== confirmEmail) {
			// TODO: Show error message
			return;
		}

		isChangingEmail = true;
		try {
			const response = await fetch('/account/settings', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
					INTENT: 'changeEmail'
				},
				body: JSON.stringify({
					newEmail
				})
			});

			if (response.ok) {
				showChangeEmailModal = false;
				// TODO: Show success message and update formData.email
			} else {
				// TODO: Show error message
			}
		} catch (error) {
			console.error('Error changing email:', error);
			// TODO: Show error message
		} finally {
			isChangingEmail = false;
		}
	}
</script>

<div class="space-y-8">
	<div>
		<h2 class="mb-1 text-xl font-semibold text-[var(--text-primary)]">Profile Information</h2>
		<p class="text-sm text-[var(--text-secondary)]">
			Update your personal information and email address.
		</p>
	</div>
	<form onsubmit={handleSubmit} class="space-y-6">
		<div class="grid grid-cols-1 gap-6 md:grid-cols-2">
			<div class="space-y-2">
				<Form.TextField
					label="First Name"
					name="firstName"
					bind:value={formData.firstName}
					placeholder="Enter your first name"
				/>
			</div>
			<div class="space-y-2">
				<Form.TextField
					label="Last Name"
					name="lastName"
					bind:value={formData.lastName}
					placeholder="Enter your last name"
				/>
			</div>
			<div class="space-y-2 md:col-span-2">
				<label class="mb-1 block text-sm font-medium text-[var(--text-primary)]" for="email-field">
					Email Address
					{#if formData.verified}
						<span class="icon-[material-symbols--verified-rounded] ml-2 align-middle text-green-500"
						></span>
					{:else}
						<span
							class="icon-[material-symbols--verified-off-rounded] ml-2 align-middle text-red-500"
						></span>
					{/if}
				</label>
				<div class="flex flex-col gap-1">
					<div class="flex-1">
						<Form.TextField
							name="email"
							type="email"
							bind:value={formData.email}
							placeholder="Enter your email"
							disabled={formData.verified}
							readonly={formData.verified}
						/>
					</div>
					{#if formData.verified}
						<div class="flex w-full justify-start">
							<Button type="link" onClick={() => (showChangeEmailModal = true)} class="text-sm">
								{#snippet text()}
									Change Email
								{/snippet}
							</Button>
						</div>
					{/if}
				</div>
			</div>
			<div class="space-y-2 md:col-span-2">
				<Form.TextField
					label="Phone Number"
					name="phone"
					type="tel"
					bind:value={formData.phone_number}
					placeholder="Enter your phone number"
				/>
			</div>
		</div>
		<div class="flex justify-end">
			<Button type="primary">
				{#snippet text()}
					Save Changes
				{/snippet}
			</Button>
		</div>
	</form>
</div>

<Form.Modal
	title="Change Email Address"
	show={showChangeEmailModal}
	showCloseButton={!isChangingEmail}
	onClose={() => (showChangeEmailModal = false)}
>
	{#snippet body()}
		<p class="mb-4 text-sm text-[var(--text-secondary)]">
			Please enter your new email address. You will need to verify this email address after changing
			it.
		</p>
		<div class="space-y-4">
			<Form.TextField
				label="New Email Address"
				name="newEmail"
				type="email"
				bind:value={newEmail}
				placeholder="Enter your new email"
			/>
			<Form.TextField
				label="Confirm New Email Address"
				name="confirmEmail"
				type="email"
				bind:value={confirmEmail}
				placeholder="Confirm your new email"
			/>
		</div>
	{/snippet}
	{#snippet submitButton()}
		<Form.Button
			type="primary"
			onClick={handleChangeEmail}
			disabled={isChangingEmail || !newEmail || !confirmEmail}
		>
			{#snippet text()}
				{#if isChangingEmail}
					<div class="flex items-center">
						<span class="icon-[mingcute--loading-line] mr-2 h-4 w-4 animate-spin"></span>
						Changing Email...
					</div>
				{:else}
					Change Email
				{/if}
			{/snippet}
		</Form.Button>
	{/snippet}
</Form.Modal>
